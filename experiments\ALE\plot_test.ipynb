{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["        <script type=\"text/javascript\">\n", "        window.PlotlyConfig = {MathJaxConfig: 'local'};\n", "        if (window.MathJax) {MathJax.Hub.Config({SVG: {font: \"STIX-Web\"}});}\n", "        if (typeof require !== 'undefined') {\n", "        require.undef(\"plotly\");\n", "        requirejs.config({\n", "            paths: {\n", "                'plotly': ['https://cdn.plot.ly/plotly-latest.min']\n", "            }\n", "        });\n", "        require(['plotly'], function(Plotly) {\n", "            window._Plotly = Plotly;\n", "        });\n", "        }\n", "        </script>\n", "        "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import plotly.graph_objects as go\n", "from plotly.offline import init_notebook_mode, plot, iplot\n", "init_notebook_mode(connected=True)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"linkText": "Export to plot.ly", "plotlyServerURL": "https://plot.ly", "showLink": false}, "data": [{"line": {"color": "rgba(0, 0, 0, 0.08333333333333333)"}, "marker": {"size": 1}, "name": "up", "type": "scatter", "uid": "ba018419-3783-4e3a-a394-d09f3ccfaaa3", "x": [0, 0, null, 1, 1, null, 0, 0, null, 1, 1, null], "y": [0, 0.1, null, 0, 0.1, null, 1, 1.1, null, 1, 1.1, null]}, {"line": {"color": "rgba(0, 0, 0, 0.16666666666666666)"}, "marker": {"size": 1}, "name": "right", "type": "scatter", "uid": "6315d40f-fc89-4fe3-a4c4-14ad6c57a31f", "x": [0, 0.1, null, 1, 1.1, null, 0, 0.1, null, 1, 1.1, null], "y": [0, 0, null, 0, 0, null, 1, 1, null, 1, 1, null]}, {"marker": {"size": 10, "symbol": "x"}, "mode": "markers", "type": "scatter", "uid": "862609e9-8e02-4020-a9d6-b9a6bbf810b6", "x": [0, 1, 0, 1], "y": [0, 0, 1, 1]}], "layout": {}}, "text/html": ["<div>\n", "        \n", "        \n", "            <div id=\"27811dd5-8892-4163-bf42-20b8d03fa2b7\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>\n", "            <script type=\"text/javascript\">\n", "                require([\"plotly\"], function(Plotly) {\n", "                    window.PLOTLYENV=window.PLOTLYENV || {};\n", "                    window.PLOTLYENV.BASE_URL='https://plot.ly';\n", "                    \n", "                if (document.getElementById(\"27811dd5-8892-4163-bf42-20b8d03fa2b7\")) {\n", "                    Plotly.newPlot(\n", "                        '27811dd5-8892-4163-bf42-20b8d03fa2b7',\n", "                        [{\"line\": {\"color\": \"rgba(0, 0, 0, 0.08333333333333333)\"}, \"marker\": {\"size\": 1}, \"name\": \"up\", \"type\": \"scatter\", \"uid\": \"ba018419-3783-4e3a-a394-d09f3ccfaaa3\", \"x\": [0, 0.0, null, 1, 1.0, null, 0, 0.0, null, 1, 1.0, null], \"y\": [0, 0.1, null, 0, 0.1, null, 1, 1.1, null, 1, 1.1, null]}, {\"line\": {\"color\": \"rgba(0, 0, 0, 0.16666666666666666)\"}, \"marker\": {\"size\": 1}, \"name\": \"right\", \"type\": \"scatter\", \"uid\": \"6315d40f-fc89-4fe3-a4c4-14ad6c57a31f\", \"x\": [0, 0.1, null, 1, 1.1, null, 0, 0.1, null, 1, 1.1, null], \"y\": [0, 0.0, null, 0, 0.0, null, 1, 1.0, null, 1, 1.0, null]}, {\"marker\": {\"size\": 10, \"symbol\": \"x\"}, \"mode\": \"markers\", \"type\": \"scatter\", \"uid\": \"862609e9-8e02-4020-a9d6-b9a6bbf810b6\", \"x\": [0, 1, 0, 1], \"y\": [0, 0, 1, 1]}],\n", "                        {},\n", "                        {\"showLink\": false, \"linkText\": \"Export to plot.ly\", \"plotlyServerURL\": \"https://plot.ly\", \"responsive\": true}\n", "                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('27811dd5-8892-4163-bf42-20b8d03fa2b7');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })\n", "                };\n", "                });\n", "            </script>\n", "        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = np.array([0, 1, 0, 1])\n", "y = np.array([0, 0, 1, 1])\n", "\n", "transitions_up = np.array([[0, 0.1], [1, 0.1], [0, 1.1], [1, 1.1]])\n", "transitions_right = np.array([[0.1, 0], [1.1, 0], [0.1, 1], [1.1, 1]])\n", "\n", "all_transitions = [transitions_up, transitions_right]\n", "action_names = ['up', 'right']\n", "n_actions = 2\n", "opacity_unit = 0.75 / 9\n", "opacities = [(i + 1) * opacity_unit for i in range(n_actions)]\n", "trace_data = []\n", "\n", "for trans, action_name, opacity in zip(all_transitions, action_names, opacities):\n", "    plot_x = []\n", "    plot_y = []\n", "    for x_o, y_o, x_y_n in zip(x, y, trans):\n", "        plot_x += [x_o, x_y_n[0], None]\n", "        plot_y += [y_o, x_y_n[1], None]\n", "    trace_data.append(\n", "        go.<PERSON>(x=plot_x, \n", "                   y=plot_y, \n", "                   line=dict(color='rgba(0, 0, 0, ' + str(opacity) + ')'), \n", "                   marker=dict(size=1),\n", "                    name=action_name)\n", "    )\n", "\n", "scatter = go.Scatter(x=x, y=y, mode='markers', marker=dict(symbol='x', size=10))\n", "trace_data.append(scatter)\n", "\n", "fig = dict(data=trace_data)\n", "iplot(fig)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}