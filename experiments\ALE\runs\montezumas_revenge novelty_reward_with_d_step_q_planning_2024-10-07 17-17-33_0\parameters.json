{"game": "MontezumaRevenge-v0", "k": 5, "train_reward": true, "beta": 0.0, "internal_dim": 3, "dropout_p": 0.1, "consec_dist": 0.5, "slack_ratio": 10, "depth": 5, "monitor": false, "env": "mon<PERSON><PERSON><PERSON>_revenge", "obs_per_state": 1, "reward_learning": "combined", "train_nstep": 5, "learn_representation": true, "reward_type": "novelty_reward", "action_type": "d_step_q_planning", "knn": "batch_knn", "score_func": "avg_knn_scores", "iters_per_update": 30, "higher_dim_obs": true, "extra-description": "", "experiment_dir": null, "start_count": 0, "viz_host": "localhost", "viz_port": 8097, "offline_plotting": false, "epochs": 1, "steps_per_epoch": 25000, "steps_per_test": 25000, "period_btw_summary_perfs": 1, "frame_skip": 2, "job_id": "0", "update_rule": "rmsprop", "learning_rate": 0.0001, "learning_rate_decay": 1, "rms_decay": 0.9, "rms_epsilon": 0.0001, "momentum": 0, "clip_norm": 1.0, "discount": 0.8, "discount_inc": 0.995, "discount_max": 0.8, "exp_priority": 0.0, "epsilon_start": 0, "epsilon_min": 0, "epsilon_decay": 100, "replay_memory_size": 25000, "batch_size": 64, "freeze_interval": 5000, "update_frequency": 1, "deterministic": true, "param1": null, "param2": null, "param3": null, "env_name": "monte<PERSON><PERSON>_revenge novelty_reward_with_d_step_q_planning_2024-10-07 17-17-33", "replay_start_size": 64, "torch_version": "2.3.1+cu121"}