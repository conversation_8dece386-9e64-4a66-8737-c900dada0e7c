{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.axes_grid1 import AxesGrid\n", "plt.rcParams.update({'font.size': 18})\n", "\n", "from definitions import ROOT_DIR\n", "from nsrl.helper.data import DataSet"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["root_save_path = os.path.join(ROOT_DIR, \"experiments\", \"maze\", \"runs\")\n", "experiment_dir = os.path.join(root_save_path, 'simple maze novelty reward with d step q planning_2019-07-12 17-02-38_1682112')\n", "\n", "to_print = [100, 200, 300, 500, 1000]\n", "\n", "dataset_fname = os.path.join(experiment_dir, f'dataset.epoch={to_print[-1]}.pkl')\n", "dataset = DataSet.load(dataset_fname)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["all_observations = dataset.observations()[0]\n", "input_size = dataset._environment.inputDimensions()[0]\n", "trajectory = dataset._environment._trajectory\n", "borders = dataset._environment._map == 1\n", "trajectory_by_inds = {n: np.zeros(input_size[1:]) + borders for n in to_print}\n", "for i in trajectory_by_inds.keys():\n", "    for pos_y, pos_x in trajectory[:i]:\n", "        trajectory_by_inds[i][pos_y, pos_x] += 1"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["<mpl_toolkits.axes_grid1.colorbar.Colorbar at 0x7f73d11cbe48>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABc0AAAFGCAYAAAC467eEAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjAsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+17YcXAAAgAElEQVR4nOzdf7xlZ10f+s83GZLMSZzAkFSGmQgtiFBpAAmS4A9+2t6CpV5b8MeV8lNqFQRub39QEFAs2B8gBbnViIqi9kIsP3sFBSWYCikMNCbaQKEhMJkMmDBKSGaSMHOe/rHXwZPDmZm99tlrn332eb/zWq81e63nWc+z9lr7m32+5znPqtZaAAAAAACA5LTN7gAAAAAAAMwLSXMAAAAAAOhImgMAAAAAQEfSHAAAAAAAOpLmAAAAAADQkTQHAAAAAICOpDmDqqpvq6p/XlVvq6rPVlXrlgefot55VfX6qrq+qu6oqoNV9WtVdZ9T1Ht0Vb23qr5UVUeq6uqq+n+qasd0zwyYpaq6W1V9T1W9rqquqqpbu9jw2ar6lap60EnqiifA16mq51fV71TVp6rqL6rqq1X1har6L1X1vSepd98uhhzsYsr1XYy55yna+z+r6kNV9ZddDPtoVT1r+mcGzFpVvXnVzznrLe87QT3fUWAb2gp5kqra0ZW5uqvzpar63ar67knPG7aaaq1tdh9YYFX1ziR/f51df6u19qcnqLMvyUeS7Evy6SSfSPLAJA9J8pdJvmu9ut0Pnm9K0pJ8KMmXkjwuye4kv5fke1trxzZ6TsDsVdUTkry/e3lDko8nOZ7k25LcN8kdSX6gtfauNfXEE2BdVXVDkvOTXJPkxiS3J/kbSR7eFfn51tr/vabOQzKKCecm+ZMkn8woDn1zks8nuaS1duM6bb0iycuT3JnkDzKKWd+T5Owkv9Jae86UTw+Yoap6c5KnJ/njJJ9Zp8g1rbXXrKnjOwpsU/OeJ6mquyX53SRP6Mp/MMk9kzy6K/LM1tpv9Dhl2JpaaxbLYEuSf5HklUm+L6Pgfn1GwfrBJ6nz+12ZX0ly2qrtr+i2X7N6e7fvvhn9APrVJI9ftf0eSf57V++fb/b7YbFYJlsy+mL3tiQXr9l+epKf6z7jf5nknmv2iycWi2XdJcl3JFlaZ/t3Jrm1+6xfsmr76Un+R7f95au2n5bkV7vtv7vO8R7V7ftykgtXbf+mjBLtLclTN/v9sFgsky9J3tx9lp/Ro47vKBbLNl3mPU+S5F91+z6R5B6rtj+hO9btSb5ps99Hi2XoxUhzZqqqrk9yn5zgN6hV9dCMgvdfJNnXWjuyal8l+bMkD0ry5Nbae1bte12SFyT5pdbaj6055sUZ/Ub2piR7WmvHp31ewObpYsO1Sb4lydNbN+pBPAEmVVVvSvLsjJLjP9Nt+74k78go3nxrW/UluqqWMvormHtklBi/ZtW+ldFkL26t/dyadn4wyX9K8onW2sMDbEmrRpo/s7X25jHK+44CfM085Um6KVu+kNHI8ktaa1euqfeLSf5xkte21v7pBk8d5po5zZk3T+7W71r9P4Ik6X44fVv3cu2fMq3U+09rD9gF+c9l9CfYl0yvq8A86GLD1d3Lvat2iSfApFb+TPmOVdtWYsNbVyfMk6SLMe/uXn4tplTVmUn+Tvfy62JKknd2bXxbVV2w0U4DW4bvKEAfs4wZj8ooYX792oR55/87QVuwcCTNmTcP69YfP8H+j68pl6o6N8lf71sPWCj379ZfWLVNPAF660Zz/UCS5STvXbWrd0zJaK7Rs5J8qbX2ubUVWmu3ZzQ6LEkeOmmfgbnx2Kr6+ar6par66ap67AnK+Y4C9DHLmDFuW/erqm84QRlYCJ6uzbxZeerzgRPsv2FNudX//svW2q096gELoHtI6MMyGq35vlW7xBPglKrqnyR5ZJIzM/pcX5zRfJ0/3lq7elXRjcSUE9VZqfdtEVNgEfyjNa9fVlX/LaOHla/+xZnvKEAfs4wZJ22rtfaVqrolya6u7LoPLoVFIGnOvDmnW992gv0rwX71bzRPVedE9YAtrqrOy+hhOEny71trh1btFk+AcTw6o5HlK44keVH+KrasEFOAE7kqyceS/EFGD/jdndF0B6/K6JdyH6iqh61KXIknQB+zjBnj1tsVsYYFZ3oWALakqjoryX9O8k1J/ijJT29uj4CtqLX2g621yuiHxIdlNC/oLyV5b/eAT4CTaq29rrX2xtbaJ1trR1prN7TWLkvy8CT/K6Np5P7J5vYSAOhD0px5s/KbzrNPsH/lt55f6VHnRPWALap7qvvbknx3kk9k9KT4r64pJp4AY2ut3dZau6q19swkv5rke5L8s1VFxBSgl9baLUn+Q/fyiat2iSdAH7OMGWINdCTNmTcrc/1dcIL9+9aUW/3vu1fVOVnfevWALaiqTk/yW0n+XpJrk/yd1tqX1ykqngCT+vVu/fdXbdtITDlRnRPVAxbHp7r1vVdt8x0F6GOWMeOkbXUP/9y1Tj1YOJLmzJv/3q0ffoL9K9uvWtnQJcs+27cesPVUVWU0z/BTM/pz5ye01m4+QXHxBJjUTd36/FXbeseUJJ9McnuSe1bV1z2Yr5tm6lvXqQcsjt3devWD+HxHAfqYZcwYt63rWmtGmrPQJM2ZN+/u1k9eO49olyx7avfynSeo90NrD1hVF2f0VOebknx4el0FNsEvJHl6Rg/Zelxr7caTlBVPgEk9plt/ZtW2ldjwA10M+Zouxjy5e/mule2ttTuS/F738utiSpLvS3Jmkk+01g5ssM/AfHpKt96/apvvKEAfs4wZH07ypST37cqs9YMnaAsWjqQ5c6W1dlWS92c0IuP1VbX6Hn1Zkgcl+dMk//+aqq9LcmeSZ1fV41c2VtU9kvy/3ct/31o7PlTfgWFV1b9N8uNJbswoYf75k5UXT4ATqarvqKondtM9rd3395L86+7lm1btek9GU0I9KMlPrSp/WpI3JLlHkve21q5ec8h/261fXFV/a1W9C5L8m+7lvwmwJVXVQ6vqSWvjSVXtrKpXJfn+JMeTvHFln+8oQB+zjBmttWNJXtu9fGNV3X1VvSckeXaSO/JXz2uAhVWttc3uAwusqp6UVT9YJnlYkjOSXJ3kaLftE621H19V54IkH0myN8n/zOjPgx6Y5CFJvpzku1pr16zT1rMy+uG2Jflgkr9I8tgk90zy+0me1P0PANhiqurJ+avRm/81o6lZ1vNfW2tfS3KJJ8B6quoZSX4tyeGMHib850nunuRbktyvK/a61tqL1tR7aJIPZTSX51UZzVX8bUm+OcmBJJe01g6u095PZ/RD7Z1JPtCtn5DRg7R+tbX27OmeITArVfV9Sd6R0cjMT2Q0avP8JA/t1ncm+cettTevqec7CmxT854nqaq7JfndjL6rfKmrtzujv8SrJM9srf16YMFJmjOoVT+UnsyHWmuPWVPv/Ix+uHxykntlFKh/P8nLW2snfNhEVT0myb9M8sgkZ2WUWHtzRj/4+vIIW9SYsSRJfr219ow1dcUT4C6q6q8neVaS784oSX5ekuWM/pLlw0l+ubV2xUnqvjzJ387oB84vZPRLvZ85yTMWUlXfn+SFGSXSTsto1PovttZ+ZUqnBWyCqvobSV6Q5BFJ7ptRXDie0VRyH0zyhtba/zhBXd9RYBvaCnmSLnH+woymxrxfRs9ouTLJq1trf3SKvsNCkDQHAAAAAICOOc0BAAAAAKAjaQ4AAAAAAB1JcwAAAAAA6OzY7A4AAAAAALD5qurhfeu01j4+RF8201wmzc84fWfbuWPXZncDmGO33PnnN7fWzj9VOfEEOJVx40kipgAnd/TYLbnz+NEap6x4ApyKn3mAaenzM8+5u07bv+cbT+91/Koa+/hbxVwmzXfu2JVH3euHN7sbwBx73+df97lxyoknwKmMG08SMQU4uQ9/4bfHLiueAKfiZx5gWvr8zHP/+94tH/29b+p1/NP3fHrs428Vg8xpXlWnVdWLquqTVXV7VR2oqtdU1dlDtAcAAAAAwMa0JMs9/1tEQ400//kkP5nkHUlek+RB3euHVdUTWmuL+W4CAAAAAGxZLcelbqefNK+qb03y/CRvb639g1XbP5vk9Ul+MMn4f7cIAAAAAMDgRiPN22Z3Y9MNMT3LDyWpJK9bs/2XkxxJ8iMDtAkAAAAAwAaZnmWY6VkekWQ5yUdXb2yt3V5VV3X7AQAAAACYIy0tx5uR5kOMNL93kptba3ess+9gkvOq6oy1O6rquVW1v6r233n86ADdArYL8QSYJjEFmBbxBJgW8QQY0nJar2URDZE0X0qyXsI8SW5fVeYuWmuXttYuaq1ddMbpOwfoFrBdiCfANIkpwLSIJ8C0iCfAUFqS42m9lkU0xPQsR5L8tRPsO2tVGQAAAAAA5siijh7vY4ik+Y1J/mZVnbnOFC17M5q65c4B2gUAAAAAYEItMad5hpme5WPdcb999caqOivJQ5PsH6BNAAAAAAA2aLnnsoiGSJq/NaNfSrxwzfYfzWgu898aoE0AAAAAADag9ZzP3JzmY2qtXVNVb0zyvKp6e5LfTfKgJD+Z5ENJfnvabSbJsQM3DHFYYAZ2XLBvs7twF+JJP/N2/ebdsb27e5X/vbf/Ru82vvUXfrx3nW9/8jW9yv/Rp+/fu417v/2M3nX62rX/4OBt9HXLRXt7lZ/Hc2B+iSnDmrfP43aOJ33v9Vm5+cKze9c57+rbBujJxh3Z0+9hkle88Zd6tyGezI++8WTp0NGBenJXOw4e7lW+73kkszmXvp+nJDlyfv9xrUs39RvjO4s2Jmnn46/4j73buN9bf6x3ne++5M96lZ9VPOn7ft3rvQd6t9FLS44vZh68lyHmNE9Go8yvT/LcJE9KcnOSNyR5WWttUUftAwAAAABsWS2LO+VKH4MkzVtrx5O8plsAAAAAAJh7leOpze7EphtqpDkAAAAAAFtIS7JsehZJcwAAAAAARow0lzQHAAAAACCjkeaS5pLmAAAAAAB0lpukuaQ5AAAAAABGmnckzQEAAAAASEvleE7b7G5sOu8AAAAAAAB0jDQHAAAAACCJOc0TSXMAAAAAAGJO8xWS5gBsyLEDN2x2F7aWnu/Xkx75vb2b2Hfgw73r/Plb9vUq/8Dc1LuN7WrX/oO9yvtM0YuYsq1s53gyyQ+uszj/ex3sd68n83tddl3Q71zEk61t6dDRze7Cuo7t3T14G0f27Oxdp2/8XUr/81g61LtKb0fOP3uCOsPP7PxdP/GPe9e5+/n9k7pXX/vgXuXvfdNy7zYmsTSjdsZXOd7M6C1pDgAAAABAWpJlj8GUNAcAAAAAYMT0LJLmAAAAAAAkac30LImkOQAAAAAAnWUjzSXNAQAAAAAYzWl+3JzmkuYAAAAAACSJ6VkSSXMAAAAAADIaab5spLmkOQAAAAAAI8ebOc0lzQEAAAAASEuZ0zyS5gAAAAAAdJbNaS5pDgAAAADAaE5zI80lzQEAAAAASDc9iznNJc0BAObJjgv2zaSdYwdumEk7wOYRT4a3rc997+7+lbbx+7UdHdmzc7O7sK4j508wgvaivdPvyBQsHTraq/x5V9/Wu42bLzy7d52+JrkmSzctz6SdWeh7HWdh2Uhz7wAAAAAAAElryfF2Wq9lHFX14qq6rKquq6pWVdefovwjq+oDVfWVqrqlqt5XVQ/tcy4bOYaR5gAAAAAAJKksZ5DpWV6V5HCSTyS5+0l7UHVxksuTHEzysm7z85JcUVWPaq1dc6rGNnoMSXMAAAAAAEYPAh1z9HhP92utXZckVfWnSc45SdnXJ7kzyXe31g52dd6W5Nokr0nyt8dob0PHMD0LAAAAAABJkuM5rdcyjpWE+alU1f2TPCLJZSvJ7q7+wSSXJXlCVd1r6GNImgMAAAAAkJbKcuu3TNkjuvVH1tl3ZZJK8vChj2F6FgAAAAAAkmTs0eMDuXe3PrjOvpVte4c+hqQ5AAAAAABpSZb7z2l+XlXtX/X60tbapRN2Yalb37HOvtvXlBnsGJLmAAAAAAAkqRxP7ylXbm6tXTSlDhzp1meus++sNWUGO4akOQAAAAAAk440n6Ybu/V606esbFtv2pWpHsODQAEAAAAASJIc70abj7tM2ce69SXr7Ls4o7z+x4c+hqQ5AAAAAACbrrX2mST7kzylqlYe6Jnu309J8oettS+s2n5eVT2wqs6d9BjrMT0LAAAAAABprQaZnqWqnpbkPt3L85OcUVUv7V5/rrX2llXFX5Dkg0muqKo3dNuen9EA8H+65tDPS/LyJM9M8uYJj/F1JM0BYMHsuGBf7zrHDtwwQE/uapJ+LYK+7+1E12/v7t51FuVL4CTnvp0dm6DOJPeKmDKM7RxPZtGvRYonR/bs7F1n6dDRwdvY5TvK3Oh7/freH7MySb+WDg3QkTUm+XwskqWblnuVP3K+iTDmzfFh5jR/dpJHr9n2ym79oSRfS5q31j5cVY9J8rPd0pJ8OMlTWmt/Mk5jGz3Govy8BAAAAADABrQky9Ofpzyttcf0LP+RJI8fo9wrkrxiI8dYj6Q5AAAAAABJaqiR5luKpDkAAAAAAKOR5m36I823GklzAAAAAACSJMdjpLmkOQAAAAAAaSkjzSNpDgAAAABAZ9lIc0lzAAAAAACS1pLjRppLmgMAAAAAMGJ6FklzAAAAAACyMqe56VkkzQEAAAAASJIcj5HmkuYAAAAAAKTF9CyJpDkAzLVjB27oXWfHBfsG6AnzbMfBw73rHNu7e/A2ZmFe+zXJZ3duiSnbinjST99zT5Ije3b2Kr906GjvNmZhkn5N8n7NImkxi+u4a//B3m0M6cbvv7NX+fu/sX8bk9wjfd/XeTWrc7/5wrN7lT/v6tt6t7F003LvOrNo48j5/acP6dvOrOJv73gyeDgxPUsiaQ4AAAAAQGfZ9CzDJM2rqp1g122ttXOGaBMAAAAAgMm1lhw3PcugI82vSHLpmm1fHbA9AAAAAAA2wPQswybNr2ut/eaAxwcAAAAAYEpayoNAM/Cc5lV1RpIzWmu3DtkOAAAAAAAbZ07zZMix9v8wyZEkX6mqP6+qN1TVuQO2BwAAAADAhFqS5Va9lkU01Ejzjya5LMlnkuxK8sQkz0vy6Kp61Hojz6vquUmemyRnnf4NA3UL2A7EE2CaxBRgWsQTYFrEE2BI5jQfKGneWnvkmk2/UVVXJ/nXSV7QrdfWuTTdg0PPPfMb2xD9ArYH8QSYJjEFmBbxBJgW8QQYzAKPHu9jlr82+HdJ7kzypBm2CQAAAADAGFpGc5r3WRbRoA8CXa219tWqujHJebNqEwAAAACA8RlpPsOkeVWdlWRfkitn1SYAAAAAAONZeRDodjf1pHlV3bO19qV1dr2ya+89024TAAAAAICNkzQfZqT5S6vq4iQfTPL5JOckeWKSxyb5b0neMECbAAAL4dje3b3r7Dh4ePA6xw7c0LsNYHMtUjyZ5AfXWcStSfq16+DUuzGVNia5X+bRrM5j6dDRmbQzlLtfcVav8kf2LPduYxbv0ZE9OwdvI+l/LpP0a5L3a+lQ7yoTtNG/X33Pf7J7Zfj3eHbXcb7iSYsHgSbDJM0vT/I3kzw9yT2THE/y6SQvSfLa1trtA7QJAAAAAAAbNvWkeWvtXUneNe3jAgAAAAAwrOUYaT6zB4ECAAAAADDHmjnNE0lzAAAAAACStEiaJ5LmAAAAAAB0JM0lzQEAAAAASNJSkuaRNAcAAAAAoNMkzSXNAQAAAAAYWY6kuaQ5AAAAAABpzZzmiaQ5AAAAAAAd07NImgMAAAAAkCQeBJpE0hwAAAAAgI6R5pLm29aOC/b1Kn/swA0D9QTY6sQTmK4dBw/3rrNInysxBaZHPBFPhjTJ/TWvJvqs7N09QE9mZ+mm5V7lj5x/Wv82DvWukqVDR/tX6unInp0zqcP8mdfr2Ldfuw4O1JFOiznNE0lzAAAAAACSpI0eBrrdSZoDAAAAAJAkWY6R5pLmAAAAAACkxZzmiaQ5AAAAAABJkjKneSTNAQAAAADomNM86f/4YwAAAAAAFlJr1WsZR1W9oqraSZavjnGMy09S/6INn/gqRpoDAAAAAJDWBpvT/O1JPrPO9guT/LMk7xnzODcnedE626+bsF/rkjQHAAAAACBJBpnTvLV2dZKr126vql/q/vkrYx7qttbab06tYycgaQ4AAAAAQJLZzWleVWcn+cEkNyR5X496pyU5J8lXWhumt+Y0BwAAAAAgyTBzmp/AU5LsSvLm1trxMevsTXJrki8nubWq3l5VD9xIJ9ZjpDkAAAAAAGmZKBF+XlXtX/X60tbapWPUe3aSluRXx2zns0n+OKNpXo4neWSS5yV5fFV9Z2vtmh59PilJ8zmz44J9M2nnlov29iq/dOCGgXoCDEU8WQyzuo5wKmLKYhBTOJVZ3CPH9u7uX2eCdo7s2dmr/KziybGe7czr57ZvvE6SXfsPDtCTjZvkntzqDj/o9F7l7/uWz/duYxbva9/PeZIsHTo6eDuzaCNJjpzfbwKJ866+rXcbk+jbr6T/uU+ib7+WbloeqCfzb4L5Tm5urV3Up0JVfUuS70zyB621z47Vr9aeuWbT71TVu5NcnuS1Sb6nTx9ORtIcAAAAAICkZaNTrozr2d36TRs5SGvtiqr6oySPraqdrbX+v7VahznNAQAAAAAYaT2XnqpqR5J/lORLSd4xhR5fn+T0JPeYwrGSGGkOAAAAAEBnBiPN/16Sb0zyH1prd0zheN+c0Wxuh6dwrCRGmgMAAAAAMDsrU7P8yno7q2pPVT2wqpZWbTu3qr7ugQxV9aQk35Hk/a2126fVQSPNAQAAAABIkrQJplwZV1XdO8n/keSjrbVrTlDs1UmenuSxGT3kM92/X1tV70lyXUYjy789yY8kuTnJC6fZT0lzAAAAAABG05QPOz3LMzKaf7zvA0A/lWR/ku/NaGqXuyW5IckvJnlVa+3gFPsoaQ4AAAAAQLqs+XBJ89baq5K86hRlnpFRcn31tmuTPHWwjq0haQ4AAAAAQJJhp2fZKiTNAQAAAAAYkTSXNAcAAAAAIElq6DnNtwRJcwAAAAAARow0lzQHAAAAACBJi5HmkTRfCMcO3NC7ztLe3b3K77hgX+82jvVsI0l2HDzcr40Jzh04MfFkMSzSubC1zWtMueWivb3r7Np/sFf5RfocLtK5bDeT/P9zIlde3av4JD+EThRPLr6wV/mZxZPeNfqb6HPbs07f93dSM7uPe+r7XXPenHVzv2Gkk1yHI3t29q6zdOhovzbOP22CNnpX6e3mC8/uXWfppuUBenJXk1yTSfQ9l4mu4wTvV986fe/HSc2qnV6MNJc0BwAAAABghZHmkuYAAAAAAIwYaS5pDgAAAABAR9Jc0hwAAAAAgIwS5h4EKmkOAAAAAMBIM9Jc0hwAAAAAgI6kuaQ5AAAAAAAd07NImgMAAAAAMFJGmkuaAwAAAACQ7kGgm92JzSdpDgAAAABAkjI9SyTNAQAAAABYYaS5pDkAAAAAAB1Jc0nzbevKq3sVPzZBE24u2CbEEwZ0bO/uXuV3HDw8UE+YmRnElKWe9xWLY7vFlFn1f5LP4UyIJ4Oa5P46duCG/u30rtFf39gwSZ15iye3n9dv6oUje3b2buPI+af1rrN0qGf5m5Z7tzHJuSwdOtqzfO8mJtTvXPqeRzKbaz/JdZxXk7xffe06OHgTkuZJxrqLq+rFVXVZVV1XVa2qrj9F+UdW1Qeq6itVdUtVva+qHjqVHgMAAAAAMH0toznN+ywLaNxf2r4qyeEkn0hy95MVrKqLk1ye5GCSl3Wbn5fkiqp6VGvtmsm6CgAAAADAkMpI87GT5vdrrV2XJFX1p0nOOUnZ1ye5M8l3t9YOdnXeluTaJK9J8rcn7y4AAAAAAIORNB9vepaVhPmpVNX9kzwiyWUrCfOu/sEklyV5QlXda5KOAgAAAADA0Po/leHkHtGtP7LOviuTVJKHT7lNAAAAAACmoFq/ZRFN+0HU9+7W6z3HdWXb3vUqVtVzkzw3Sc46/Rum3C1gOxFPgGkSU4BpEU+AaRFPAIY17ZHmS936jnX23b6mzF201i5trV3UWrvojNN3TrlbwHYingDTJKYA0yKeANMingCDatVvWUDTHml+pFufuc6+s9aUAQAAAABgXrR4EGimnzS/sVuvNwXLyrb1pm4BAAAAAGCzSZpPfXqWj3XrS9bZd3FGb/nHp9wmAAAAAABT4EGgU06at9Y+k2R/kqdU1cpDQdP9+ylJ/rC19oVptgkAAAAAwJS0nssCGmt6lqp6WpL7dC/PT3JGVb20e/251tpbVhV/QZIPJrmiqt7QbXt+Rgn6f7rxLrNVHDtww2Z3AVgQ4kk/Oy7Y17vOvL7HR/b0e7DVLpPAMYYdBw/3rjOvn5FZEFPgxGYRTyb5DM7r53aSfvV1bO/u3nX6xoYkOXJ+vzGI95rgXhnS7muP9yq/dOjoBK3M5wNKJzmXvvfIZO/X4li6ablf+Qner5svPLt3nfOuvq13nVnoG092DdSPu1jQRHgf485p/uwkj16z7ZXd+kNJvpY0b619uKoek+Rnu6Ul+XCSp7TW/mRDvQUAAAAAYBCLPOVKH2MlzVtrj+lz0NbaR5I8fpIOAQAAAACwSVptdg823bgjzQEAAAAAWHRGmkuaAwAAAAAwYnoWSXMAAAAAAFZImkuaAwAAAACQxINAk0iaAwAAAACwQtJc0hwAAAAAgI6kuaQ5AAAAAAAjpmdJTtvsDgAAAAAAwLyQNAcAAAAAYKT1XMZUVe0Ey609jvHEqvpwVd1WVYer6rKq+uvj92I8pmcBAAAAACBpg0/PckWSS9ds++o4Favq+5P8TpI/SfLPkpyb5IVJ/riqLmqt3TitTkqa93HxhcO3cfDw8G0Am088YUzHDtzQu86OC/YN0JPNsWv/wc3uwtYgpjAmMWVrx5S+1+KWi/YO1JO7Wtq7u1+FCeLJJPfhJPf7ojjW95qkf3JgVvGkbzuTJDmW0v/9Wjo0QUNb2JE9Oze7C5tq6dDRXuVveNw5A/Xkrvb94diDcxfO0k3Lvev0vY/7XvfJ6/SuMrxhk7NGvCYAABlJSURBVObXtdZ+s2+lqrpbkjckOZDku1prt3bb35vk40lekeS50+qk6VkAAAAAABgZaHqWFVV1RlX1/e3So5PcO8mbVhLmSdJauyrJ5Ul+oEusT4WkOQAAAAAAqYymZ+mz9PQPkxxJ8pWq+vOqekNVnTtGvUd064+ss+/KJLuSPKB3b07A9CwAAAAAAIz0T4SfV1X7V72+tLW2dt7yJPloksuSfCajJPcTkzwvyaOr6lGrR5Cv497der359la27U3yZ716fgKS5gAAAAAATPog0Jtbaxed8tCtPXLNpt+oqquT/OskL+jWJ7LUre9YZ9/ta8psmOlZAAAAAAAYGXhO8zX+XZI7kzzpFOWOdOsz19l31poyGyZpDgAAAADAyAyT5q21rya5Mcl5pyh6Y7feu86+lW3rTd0yEUlzAAAAAACSDP4g0Lu2VXVWkn1JvniKoh/r1pess+/iJLck+Z8b681fkTQHAAAAAGBkgJHmVXXPE+x6ZUbP3XzPqrJ7quqBVbV6jvIPJTmU5DlVdc6qsg9J8pgkl3Wj1qfCg0ABAAAAABjSS6vq4iQfTPL5JOckeWKSxyb5b0nesKrsq5M8vdt3eTKaxqWqXpDkrUmuqKpfTrIryYuS3JTk5dPsrKQ5AAAAAADTerjnei5P8jczSobfM8nxJJ9O8pIkr22t3X7KrrV2WVUdTfLSJP8+yR1J/iDJv2itTW0+80TSHAAAAACAzkbnKV9Pa+1dSd41ZtlnJHnGCfb9lyT/ZWodOwFJcwAAAAAARoYZab6lSJoP7Mienb3K75rqHxIAi0Q8YUg7Ltg3eBvH9u7uXWfHwcMD9IRETOmr72fk2IEbBurJ1iCmDKPvfbU0wXs0iVnEk0mud3rWmeT+uOWivb3r9L4uE/Rrq9/rq80invS9hyex1f8/unTo6Eza6XstZtWvvnZfe3yzuzA18/oez2u/FiWeDDHSfKuRNAcAAAAAYETSXNIcAAAAAIAM+SDQLUXSHAAAAACAVLdsd5LmAAAAAACMGGkuaQ4AAAAAwIgHgUqaAwAAAACwQtJc0hwAAAAAgI6kuaQ5AAAAAABJmulZEklzAAAAAABWSJpLmgMAAAAAMGKkuaQ5AAAAAAArJM0lzfvYcfBw7zpL2T1AT+5qxwX7Bm/j2IEbBm8DthPxhCHN7Xu8d/h7eLsSU4avM4tzmVdiyvzoex/esmdn7zaWDh0dvM6xBbp2k7xffWP2vMasWcXFWcSgpQW6J4dyZE7jybxalPNIZnftZ6F3/J0gNhw5/7TedZZuWu5dZ2hGmkuaAwAAAACQjEaZS5pLmgMAAAAA0JE0lzQHAAAAACCpmJ4lkTQHAAAAAGCFpLmkOQAAAAAAI9VkzSXNAQAAAADwINCOpDkAAAAAAEnMaZ5ImgMAAAAAsELSXNIcAAAAAIARI80lzQEAAAAAWCFpntM2uwMAAAAAADAvjDQHAAAAACBppmdJJM17OXbghv6VetY51r+FyVx8Ya/ik9woE71fLIQdF+zb7C7MPfGkH/FkMew4eHizu8BqV17dq7iY0s8k/y8U6/rZjjGl7z2ya5I29u7uXWcW12Kiz0fPeDKJWZz7In23nuj+GqAfX2eC6zjJuTAfJvnc3nLR3l7llw4d7d3GvJrXczmyZ2fvOksZ/nN73tW3Dd7GTEiajzc9S1W9uKouq6rrqqpV1fUnKfvmrsx6yz+cWs8BAAAAAJiaymikeZ9lEY37S9tXJTmc5BNJ7j5mnaets+2jY9YFAAAAAGDW2oJmwnsYN2l+v9badUlSVX+a5JxTVWit/eZGOgYAAAAAwGwt6ujxPsZKmq8kzPuoqkryDUluba0t960PAAAAAMAMtZjTPGPOaT6hL3fL0ap6f1U9csC2AAAAAADYoFrutyyiIR5E/YUkP5/k40luS/KQJC9MckVVPbG19oH1KlXVc5M8N0nOOv0bBugWsF2IJ8A0iSnAtIgnwLSIJ8CgjDSfftK8tfYv12x6Z1X9dpKrkvzHJN98gnqXJrk0Sc498xtdGmBi4gkwTWIKMC3iCTAt4gkwJHOaDzs9y9e01j6d5G1J7l9VD5hFmwAAAAAA9NCStNZvWUAzSZp3ru/W582wTQAAAAAAxlSt37KIhpjT/ERWpmX54gzbBAAAAABgXAuaCO9jqknzqjo7yfHW2u1rtj8syVOSXNta+1/TbBMAAAAAgI2rLO7o8T7GSppX1dOS3Kd7eX6SM6rqpd3rz7XW3tL9+5uTvLeq3pnk00luS/KQJM9Kcjzdk50BAAAAAJgzCzxPeR/jjjR/dpJHr9n2ym79oSQrSfMvJPlAkscm+b+S7ExyKMlbk7y6tfbJDfX2ZC6+sFfxHQcPD9SRjbnlor296xx+0Om96+y+9niv8rsO9m4Cti7xpBfxhHEdO3BD7zo7Ltg3QE9mrGdMOTZQN9bq++eG2z2m9L1/F+LenXPbMqbMaTzpa6J48rRv6l1nO8eTbfn56Bzbu3uzu0DnyJ6dvessHTo6eBuHHzd8PJnkZ8RZ3LuzuCaTtDNJG5PU6XtdJrkms3qPh2ak+Zg/L7XWHjNmuS8kedpGOgQAAAAAwCaRNM9pm90BAAAAAADmQ7V+yymPV/WAqvqZqrqyqm6qqq9U1VVV9ZLuGZnjHOPyqmonWC7a6DmvNdUHgQIAAAAAsEW1JMtTH2r+rCQ/keTdSX4ryVczmt77Z5M8taoubq2NM0/NzUletM7266bV0RWS5gAAAAAAjEx/epbfyeh5l19ete0Xq+rTSV6S0fM0f2GM49zWWvvNqfduHaZnAQAAAAAgyfSnZ2mt7V+TMF/x1m794LH7VnVaVe2qqhq3ziQkzQEAAAAAGGmt3zK5fd36i2OW35vk1iRfTnJrVb29qh64kQ6ciOlZAAAAAABIMt7o8TXOq6r9q15f2lq79KRtVJ2e5KeSHEvy22O08dkkf5zk6iTHkzwyyfOSPL6qvrO1dk3vXp+EpDkAAAAAAKP5zPsnzW9urV3Us87rklyS5F+11j51ym619sw1m36nqt6d5PIkr03yPT3bPylJcwAAAAAAUklqY1OunLqNqldmNEr80tbaqyc9Tmvtiqr6oySPraqdrbWj0+qjOc0BAAAAABhZ7rn0UFWvSPLSJL+W5Mem0Nvrk5ye5B5TONbXGGkOAAAAAECS4Uaadwnzlyf59STPaW0qDX1zRvOiH57Csb5m2ybNjx24YbO7sL6L9vaucvv5PX+lkyTX9is+t+8XzIG5/XyIJ7Al7TjY/7veJJ+rY30riCmD23HBvt51Fun8mT7xpF/xSc59ks/tJHVm0caxvbv7tTHB/TULR/bs7F1n6VD/v+af1/Pf6vpev8MPOr13G7c/qP/1PnLzWb3KL/X8PCWT3bvzapLP1Dya5JocOX+SST36tbPr4ARN9DHZnOanVFUvyyhh/pYkz2qtrfs/96rak+TcJJ9vrR3ptp2b5NbW2vE1ZZ+U5DuSvLe1dvs0+7ttk+YAAAAAAAyrqn4iyU8n+XySDyT54apaXeSLrbX3d/9+dZKnJ3lsRg/5TPfv11bVe5Jcl9Hv7L89yY8kuTnJC6fdZ0lzAAAAAACStGT607M8olt/U0ZTs6z1oSTvX2f7ik8l2Z/ke5N8Y5K7JbkhyS8meVVrberj7yXNAQAAAABIktSUc+attWckecakZVtr1yZ56nR7dXKS5gAAAAAAjAz0INCtRNIcAAAAAICkJTXB87cXjaQ5AAAAAAAjRppLmgMAAAAA0JEzlzQHAAAAAGCkjDSXNAcAAAAAoCNpLmkOAAAAAEBGU7N4EKikOQAAAAAASaWZniWS5gAAAAAArJA0X5yk+Y6Dh3uVPzZQPzZq1/6DvessHdo9QE9gcsf29rsn+35+hyaewPzY6vEkmSCm9DznZLIvdMcO3NCr/LzGlB0X7Bu8jVmZ5Nqn53Xc7rZ6TBFPhjWreDLRZ72nI3t2Dt7Grv6XcaLPVN/7a2mS9/fKq3tXOXbxhb3Kz1s8OXL+aYO3sXTT8PM77PvDW/tX+sNJWrptkkq9LB062rvOLD7rs2hjknOfxCzi7yT3fd/P467eLUxA0nxxkuYAAAAAAGyAOc2TSJoDAAAAANAxp7mkOQAAAAAAKyTNJc0BAAAAAEiSJmkeSXMAAAAAAJLRnOaS5pLmAAAAAAB0PAhU0hwAAAAAgBEPApU0BwAAAABghaS5pDkAAAAAABnNab4saS5pDgAAAABAkmakeSTNAQAAAABYIWkuaQ4AAAAAQEfSfPsmzXdcsK93nWN7d/dv5+Dh3nXmsY1M8H6xfR3b7A7MmHjSk3hCD4sQT265aG+v8kfOP61/Ixee3bvKeVf3i0OTXIubJ+jX0k3LvcofufCC3m3c670HeteZhUner3sdFFP72OoxRTzpZ17jySy+b01yLuddfVuv8n3vx0nt6ll+kvtrou/zE7Sz3UwSg/p+bicxk3gyp+e+dOho7zqTvF9948mRPTt7tzGvJnmPlw4N0JGNMKd5km2cNAcAAAAAYLWWtOF/iTPvJM0BAAAAABgxPUsm+Hs9AAAAAABYTEaaAwAAAABgTvOOpDkAAAAAACOmZ5E0BwAAAACgI2kuaQ4AAAAAQJI0SfNImgMAAAAAkHRzmi9vdi82naQ5AAAAAAAjRppLmgMAAAAA0JE0lzQHAAAAACBJWrIsaS5p3sOOg4c3uwuwJfisnJr3CMazCJ+VXfsP9is/UD82w71mcP0W6v1674HN7sLC2+oxRTwZ1kK9XzOIJ7v63Y4zM6vP+VaPJ9v5/zniST8zeb/mNJ5sWy1pzZzmp41TqKoeUFU/U1VXVtVNVfWVqrqqql5SVWevU/5bquqdVfUXVXVbVV1RVY+bfvcBAAAAAJia5dZvWUDjjjR/VpKfSPLuJL+V5KtJHpvkZ5M8taoubq0dTZKqul+SDyc5luTfJvlykh9N8ntV9Xdbax+Y7ikAAAAAADAV5jQfO2n+O0le3Vr78qptv1hVn07ykiTPTvIL3fZXJ7l7koe31q5Kkqr6jSR/luSNVfXA1rzzAAAAAABzpbVk2fQsY03P0lrbvyZhvuKt3frBSdJN1fLkJJevJMy7+rcmeVOSByR5xIZ6DAAAAADAMFrrtyygjT4IdF+3/mK3vjDJmUk+sk7ZK7v1I5J8dIPtAgAAAAAwZc1I88mT5lV1epKfymju8t/uNt+7W6/33NuVbXtPcLznJnlukpx1+jdM2i0A8QSYKjEFmBbxBJgW8QQYzuKOHu9jrOlZTuB1SS5J8rLW2qe6bUvd+o51yt++psxdtNYuba1d1Fq76IzTd26gW8B2J54A0ySmANMingDTIp4Ag2lJllu/ZQFNlDSvqlcmeV6SS1trr16160i3PnOdametKQMAAAAAwDxpy/2WMVTVaVX1oqr6ZFXdXlUHquo13TMyB6/fV++keVW9IslLk/xakh9bs/vGbr3eFCwr29abugUAAAAAgE3UkrTl1msZ088neW2S/5Hk+UkuS/KTSd5TVePkqDdav5dec5p3CfOXJ/n1JM9p7esmuLkmo6lZLlmn+sXden/PPgIAAAAAMLTWxh49Pq6q+taMEt1vb639g1XbP5vk9Ul+MH/1zMyp15/E2Fn4qnpZRgnztyR5Vmtf/+611m5N8p4kj6mqh6yqe06S5yT5dJKPbrTTAAAAAABM3wAjzX8oSWX0jMzVfjmjqbx/ZOD6vY010ryqfiLJTyf5fJIPJPnhqlpd5Iuttfd3/35xkscn+f2q+vkktyT50YymZ3nSOqPTAQAAAADYZMdzLLcsf2nah31EkuWsGUzdWru9qq7q9g9Zv7caJ4ddVW9O8vSTFPlQa+0xq8o/KMnPJXl0kjOSfCLJK1prHxirU1U3JfncOrvOS3LzOMdg4bj229PJrvt9Wmvnn+oAJ4knpzo+i8t1375OdO3HiieJ7yisy7XfnsQThuDab09+5mEIrvv2NY3vKH+R5FDPdi9I8qlVry9trV266pjXJPlrrbVvXKe9tyV5SpIzW2t3nqBPG6o/ibFGmrfWnpHkGeMetLV2bZK/P1mXkhNdxKra31q7aNLjsnW59tvTNK77yf6n4L7anlz37WvImOK+2r5c++1JPGEIrv325GcehuC6b19Tiin3mFZ/VlnK6DmY67l9VZkTJb03Wr+3qT9ZFAAAAAAAOkeSnHmCfWetKjNU/d4kzQEAAAAAGMqNSc6rqvUS33uT3HyKqVU2Wr+3rZY0v/TURVhQrv32NPR1d19tT6779jXktXdfbV+u/fYknjAE13578jMPQ3Ddt695vfYfyygP/e2rN1bVWUkemmT/wPV7G+tBoAAAAAAA0FdV/a0kf5LkHa21f7Bq+/OTvD7J01prv9ltu1+Su7XWPjlJ/an1WdIcAAAAAIChVNUbkjwvyTuS/G6SByX5ySR/nORxrbXlrtz1Se7TWqtJ6k+tv5LmAAAAAAAMpapOT/LCJM9Nct8kNyd5a5KXtdZuXVXu+qyfNB+r/tT6K2kOAAAAAAAjc/8g0Ko6rapeVFWfrKrbq+pAVb2mqs7e7L4xHVX14qq6rKquq6rW/UbpZOUfWVUfqKqvVNUtVfW+qnrojLrLFFTVA6rqZ6rqyqq6qbuWV1XVS9b7bFfVt1TVO6vqL6rqtqq6oqoeN0G74smCE0+2JzGFIYgn25N4wlDElO1HPGEo4sn2tFkxZTub+5HmVfUfMpqf5h1J3pvRfDXPT3JFkidMe74aZq+qWpLDST6R5OFJbmmt3fcEZS9OcnmSg0l+odv8vCR/LcmjWmvXDN1fNq6qfi7JTyR5d5Irk3w1yWOTPDXJ1Ukubq0d7creL8lHkxxL8rokX07yo0kenOTvttY+0KNd8WTBiSfbk5jCEMST7Uk8YShiyvYjnjAU8WR72qyYsq211uZ2SfKtSZaT/Oc125+fpCX54c3uo2Uq1/lvrPr3nya5/iRlP5rkliR7V23b2237/c0+F8vY1/yiJOeus/1nu8/281Zte1uS40keumrbOUk+l+RT6X75N0ab4sk2WMST7bmIKZYhFvFkey7iiWWoRUzZfot4YhlqEU+257IZMWW7L/M+PcsPJamMfiuy2i8nOZLkR2beI6autXbdOOWq6v5JHpHkstbawVX1Dya5LMkTqupew/SSaWqt7W+tfXmdXW/t1g9Oku5PjJ6c5PLW2lWr6t+a5E1JHpDRPTEO8WQbEE+2JzGFIYgn25N4wlDElO1HPGEo4sn2tEkxZVub96T5IzL6LelHV29srd2e5Kq4yNvNyvX+yDr7rszoy8HDZ9cdBrCvW3+xW1+Y5Myc+Jon48cB8YTVxJPtQUxhFsST7UE8YVbElMUnnjAr4sn2MGRM2dbmPWl+7yQ3t9buWGffwSTnVdUZM+4Tm+fe3frgOvtWtu2dUV+Ysqo6PclPZTTn1m93m6d5zcUTVhNPFpyYwgyJJwtOPGHGxJQFJp4wY+LJgptBTNnW5j1pvpRkvWCfJLevKsP2sHKt17sn3A9b3+uSXJLkZa21T3XbpnnNxRNWE08Wn5jCrIgni088YZbElMUmnjBL4sniGzqmbGvznjQ/ktGfFKznrFVl2B5WrvV694T7YQurqldm9ATvS1trr161a5rXXDxhNfFkgYkpzJh4ssDEEzaBmLKgxBM2gXiywGYUU7a1eU+a35jRnw+td6H3ZvRnR3fOuE9snhu79Xp/RrKybb0/P2GOVdUrkrw0ya8l+bE1u6d5zcUTVhNPFpSYwiYQTxaUeMImEVMWkHjCJhFPFtQMY8q2Nu9J849l1MdvX72xqs5K8tAk+zejU2yaj3XrS9bZd3GSluTjs+sOG9UF+pcn+fUkz2mttTVFrsnoT4pOdM2T8eOAeMJq4skCElPYJOLJAhJP2ERiyoIRT9hE4skCmnFM2dbmPWn+1ow+xC9cs/1HM5p/57dm3iM2TWvtMxl9sJ9SVSsPNkj376ck+cPW2hc2q3/0U1UvyyjQvyXJs1pry2vLtNZuTfKeJI+pqoesqntOkuck+XTWPBn+JMQTvkY8WTxiCptFPFk84gmbSUxZLOIJm+l/t3f/KFLDcRiHP0Gwsre0sbGwsVM7sfMMnkC08RxqsR5A8ABiIywibqEHsLRUtLATtbP4WSTF4i6Cf2Zn3TwPvMVAhmRI5iV8M5Pok5NnC52yatPBCxLHyzRNO8336HlSPasuVHeq19W1ww4Q/i/TNN2szi0vb1enq3vL63djjMf7lr1Svaw+VDv73nO2ujrGeHMkG81fmabpVvWwet/8pOefv8efxhjPl2XPNxf69+pB9aX5pO9idWOMsfsb69UnJ5w+WSedwibok3XSJ2yKTlkffcKm6JN12lanrNoY41inOlXdrd42/73gY3W/OrPtbZN/to/3mq+GH5a9Q5a/XL2ovlVfq93q0rY/h/zWPn/0i31+YL83n+g9rT43P7DiVXX9D9arT0549Mk6o1NkQ8eVPllh9IlsKjplfdEnsqnok3VmW52y5hz7X5oDAAAAAMBROe73NAcAAAAAgCNjaA4AAAAAAAtDcwAAAAAAWBiaAwAAAADAwtAcAAAAAAAWhuYAAAAAALAwNAcAAAAAgIWhOQAAAAAALAzNAQAAAABg8QOvazjde4C65wAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1800x864 with 10 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = plt.figure(figsize=(25,12))\n", "\n", "grid = AxesGrid(fig, 111,\n", "                nrows_ncols=(1, len(to_print)),\n", "                axes_pad=0.15,\n", "                share_all=True,\n", "                label_mode=\"L\",\n", "                cbar_location=\"right\",\n", "                cbar_mode=\"single\")\n", "\n", "for (n, val), ax in zip(trajectory_by_inds.items(),grid):\n", "    ax.title.set_text(str(n))\n", "    im = ax.imshow(val, vmin=0, vmax=12)\n", "\n", "grid.cbar_axes[0].colorbar(im)\n", "\n", "# for cax in grid.cbar_axes:\n", "#     cax.toggle_label(False)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6.0\n", "12.0\n", "12.0\n", "12.0\n"]}], "source": ["current_max = 0\n", "for v in trajectory_by_inds.values():\n", "    print(np.max(v))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 2}