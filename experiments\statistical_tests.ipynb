{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from scipy.stats import ttest_ind\n", "\n", "columns = ['random', 'transition', 'rnd', 'count', 'bootq', 'novelty']"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Multistep maze\n", "ms_results = {}\n", "ms_fname = Path('multistep_results.csv')\n", "ms_df = pd.read_csv(ms_fname)\n", "\n", "ms_target = ms_df['novelty']\n", "for col in columns:\n", "    _, p = ttest_ind(ms_df[col], ms_target, equal_var=False)\n", "    ms_results[col] = {\n", "        'p': p, \n", "        'avg': np.average(ms_df[col]),\n", "        'stderr': np.std(ms_df[col]) / np.sqrt(len(ms_df[col]))\n", "    }"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'random': {'p': 0.002482974675691094,\n", "  'avg': 1863.3,\n", "  'stderr': 308.34766254992104},\n", " 'transition': {'p': 0.00040336902865244624,\n", "  'avg': 1018.0,\n", "  'stderr': 79.30876370238033},\n", " 'rnd': {'p': 0.023609164753095133, 'avg': 938.4, 'stderr': 135.8843037293123},\n", " 'count': {'p': 0.2302303596580086, 'avg': 658.8, 'stderr': 71.73294919351915},\n", " 'bootq': {'p': 0.004626713513578689,\n", "  'avg': 1669.1,\n", "  'stderr': 291.2621997444914},\n", " 'novelty': {'p': 1.0, 'avg': 524.6, 'stderr': 73.24086291135572}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ms_results"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Multistep maze ablation\n", "msa_columns = ['model_free', 'model_based']\n", "msa_results = {}\n", "msa_fname = Path('multistep_ablation_results.csv')\n", "msa_df = pd.read_csv(msa_fname)\n", "\n", "for col in msa_columns:\n", "    _, p = ttest_ind(msa_df[col], ms_target, equal_var=False)\n", "    msa_results[col] = {\n", "        'p': p,\n", "        'avg': np.average(msa_df[col]),\n", "        'stderr': np.std(msa_df[col]) / np.sqrt(len(msa_df[col]))\n", "    }\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'model_free': {'p': 0.2510409924240079,\n", "  'avg': 758.6,\n", "  'stderr': 169.0844877568608},\n", " 'model_based': {'p': 0.5703366696757379,\n", "  'avg': 584.1,\n", "  'stderr': 64.52076409962919}}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["msa_results"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# A<PERSON><PERSON>bot\n", "ab_results = {}\n", "ab_fname = Path('acrobot_results.csv')\n", "ab_df = pd.read_csv(ab_fname)\n", "\n", "ab_target = ab_df['novelty']\n", "for col in columns:\n", "    _, p = ttest_ind(ab_df[col], ab_target, equal_var=False)\n", "    ab_results[col] = {\n", "        'p': p, \n", "        'avg': np.average(ab_df[col]),\n", "        'stderr': np.std(ab_df[col]) / np.sqrt(len(ab_df[col]))\n", "    }"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'random': {'p': 0.007722260834351773,\n", "  'avg': 1713.3,\n", "  'stderr': 316.25044031589897},\n", " 'transition': {'p': 0.04981337034511851,\n", "  'avg': 932.8,\n", "  'stderr': 141.53591770289265},\n", " 'rnd': {'p': 0.00422161518906239, 'avg': 953.8, 'stderr': 85.98206789790531},\n", " 'count': {'p': 0.050094682037533325,\n", "  'avg': 1007.0,\n", "  'stderr': 174.8056063174176},\n", " 'bootq': {'p': 0.8459466348300362, 'avg': 592.5, 'stderr': 43.65163227188647},\n", " 'novelty': {'p': 1.0, 'avg': 576.0, 'stderr': 66.12715024859305}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ab_results"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Combined results\n", "# Normalize multistep\n", "ms_df_norm = ms_df / ms_results['novelty']['avg']\n", "ab_df_norm = ab_df / ab_results['novelty']['avg']\n", "combined_norm_df = pd.concat((ms_df_norm, ab_df_norm))\n", "\n", "comb_results = {}\n", "\n", "comb_target = combined_norm_df['novelty']\n", "for col in columns:\n", "    _, p = ttest_ind(combined_norm_df[col], comb_target, equal_var=False)\n", "    comb_results[col] = {\n", "        'p': p, \n", "        'avg': np.average(combined_norm_df[col]),\n", "        'stderr': np.std(combined_norm_df[col]) / np.sqrt(len(combined_norm_df[col]))\n", "    }"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'random': {'p': 3.1148325955165556e-05,\n", "  'avg': 3.2631640972486977,\n", "  'stderr': 0.40730844077036693},\n", " 'transition': {'p': 0.00012689241859539297,\n", "  'avg': 1.779985279789893,\n", "  'stderr': 0.1486516500996209},\n", " 'rnd': {'p': 0.00034684486832287076,\n", "  'avg': 1.7223471189689499,\n", "  'stderr': 0.15021614891475193},\n", " 'count': {'p': 0.01927372550127504,\n", "  'avg': 1.5020389211886305,\n", "  'stderr': 0.17530261420385926},\n", " 'bootq': {'p': 0.009894107481240156,\n", "  'avg': 2.105154026083365,\n", "  'stderr': 0.3693823574569017},\n", " 'novelty': {'p': 1.0, 'avg': 1.0, 'stderr': 0.09037657938483847}}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["comb_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}