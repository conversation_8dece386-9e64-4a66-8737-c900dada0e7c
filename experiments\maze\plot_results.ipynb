{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 2,
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import json\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "\n",
    "from definitions import ROOT_DIR\n",
    "from nsrl.helper.plot import get_visdom_data\n",
    "plt.rcParams.update({'font.size': 18})"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 19,
   "metadata": {},
   "outputs": [],
   "source": [
    "\n",
    "def plot_baseline(plot_fname, ax1, ax2, legends, keys, plot=True):\n",
    "    with open(plot_fname, 'r') as f:\n",
    "        baseline = json.load(f)\n",
    "        exp_factor_baseline = np.array([l for l in baseline['exploration_factors'] if l])\n",
    "        avg_exp_factor_baseline = np.average(exp_factor_baseline, axis=0)\n",
    "\n",
    "        ratio_visited_baseline = np.array([l for l in baseline['ratios_visited'] if l])\n",
    "        avg_ratio_visited_baseline = np.average(ratio_visited_baseline, axis=0)\n",
    "\n",
    "    x = np.arange(0, avg_exp_factor_baseline.shape[0])\n",
    "    if plot:\n",
    "        ax1.title.set_text('Exploration factor')\n",
    "        b1, = ax1.plot(x, avg_exp_factor_baseline, color='blue')\n",
    "        y_min_baseline = avg_exp_factor_baseline - np.std(exp_factor_baseline, axis=0)\n",
    "        y_max_baseline = avg_exp_factor_baseline + np.std(exp_factor_baseline, axis=0)\n",
    "        ax1.fill_between(x, y_min_baseline, y_max_baseline, color='blue', alpha=0.2)\n",
    "\n",
    "        ax2.title.set_text('Ratio of states visited')\n",
    "        b2, = ax2.plot(x, avg_ratio_visited_baseline, color='blue')\n",
    "        y_min_baseline = avg_ratio_visited_baseline - np.std(ratio_visited_baseline, axis=0)\n",
    "        y_max_baseline = avg_ratio_visited_baseline + np.std(ratio_visited_baseline, axis=0)\n",
    "        ax2.fill_between(x, y_min_baseline, y_max_baseline, color='blue', alpha=0.2)\n",
    "\n",
    "        legends.append(b1)\n",
    "        keys.append('Random Baseline')\n",
    "    \n",
    "    return legends, keys, x\n",
    "\n",
    "def find_match(substring, strings):\n",
    "    for s in strings:\n",
    "        if substring in s.lower():\n",
    "            return substring\n",
    "    return None\n",
    "\n",
    "def exploration_plots(x, results, key, fig, ax1, ax2, legends, keys, color='orange'):\n",
    "    key, title, titles = key\n",
    "    mf = results[key]\n",
    "\n",
    "    mf_plots = {k: [] for k in list(mf.values())[0].keys()}\n",
    "    for fname, res in mf.items():\n",
    "        for k in mf_plots.keys():\n",
    "            \n",
    "            mf_plots[k].append(res[k][1]['y'][:1000])\n",
    "\n",
    "    try:\n",
    "        exp_fac_plots = mf_plots['Exploration Factor for ep 0'] if 'Exploration Factor for ep 0' in mf_plots else mf_plots['Exploration Factor']\n",
    "        vis_rat_plots = mf_plots['Ratio of states visited for ep 0'] if 'Ratio of states visited for ep 0' in mf_plots else mf_plots['Ratio of states visited']   \n",
    "    except Exception:\n",
    "        print(mf)\n",
    "        print(mf_plots.keys())\n",
    "    \n",
    "    explr_fac = np.array(exp_fac_plots)\n",
    "    visited_ratios = np.array(vis_rat_plots)\n",
    "\n",
    "    return plot_means_with_std(x, explr_fac, visited_ratios, title, fig, ax1, ax2, legends, keys, color)\n",
    "\n",
    "def plot_means_with_std(x, explr_fac, visited_ratios, title, fig, ax1, ax2, legends, keys, color='orange'):\n",
    "    \n",
    "    avg_mf_exploration_factor = np.average(explr_fac, axis=0)\n",
    "\n",
    "    mf1, = ax1.plot(x, avg_mf_exploration_factor, color=color)\n",
    "\n",
    "    avg_mf_ratios_visited = np.average(visited_ratios, axis=0)\n",
    "    mf2, = ax2.plot(x, avg_mf_ratios_visited, color=color)\n",
    "    \n",
    "#     y_mins_ef = explr_fac.min(axis=0)\n",
    "#     y_max_ef = explr_fac.max(axis=0)\n",
    "    y_mins_ef = avg_mf_exploration_factor - np.std(explr_fac, axis=0)\n",
    "    y_max_ef = avg_mf_exploration_factor + np.std(explr_fac, axis=0)\n",
    "    ax1.fill_between(x, y_mins_ef, y_max_ef, color=color, alpha=0.2)\n",
    "    \n",
    "    y_mins_rv = avg_mf_ratios_visited - np.std(visited_ratios, axis=0)\n",
    "    y_max_rv = avg_mf_ratios_visited + np.std(visited_ratios, axis=0)\n",
    "    ax2.fill_between(x, y_mins_rv, y_max_rv, color=color, alpha=0.2)\n",
    "    \n",
    "    legends.append(mf1)\n",
    "    keys.append(title)\n",
    "    \n",
    "    return mf1, legends, keys\n",
    "\n",
    "def parse_visdom_plot_dir(plot_dir, titles, trials=1):\n",
    "    \"\"\"\n",
    "    Parses a .visdom directory that includes saved plots.\n",
    "    :param plot_dir: directory path to plot\n",
    "    \"\"\"\n",
    "    results = {}\n",
    "    for fname in os.listdir(plot_dir)[:trials]:\n",
    "        if fname.endswith('.json'):\n",
    "            with open(os.path.join(plot_dir, fname)) as json_file:\n",
    "                data = json.load(json_file)\n",
    "                found = get_visdom_data(data, titles)\n",
    "                results[fname] = found\n",
    "    return results\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 14,
   "metadata": {},
   "outputs": [],
   "source": [
    "# WALLLESS EXPERIMENTS\n",
    "size_maze = 21\n",
    "n_steps = 1000\n",
    "trials = 3\n",
    "plot_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "old_titles = ['Exploration Factor for ep 0', 'Exploration Factor', 'Ratio of states visited', 'Ratio of states visited for ep 0']\n",
    "new_titles = ['Average exploration factor over 1 episodes', 'Average ratio of states visited over 1 episodes']\n",
    "experiments = [('simple maze count reward with q argmax mf', 'Count w/ Q-argmax', old_titles),\n",
    "               ('walls_q_argmax', 'Novelty w/ Q-argmax', new_titles),\n",
    "               ('walls_1_step', 'Novelty w/ Planning (d=1)', new_titles),\n",
    "#                'simple maze novelty reward with 3 step q planning'\n",
    "#                ('simple maze novelty reward with 3 step reward planning', 'Novelty w/ MCTS (d=3)'),\n",
    "               ('walls_5_step', 'Novelty w/ Planning (d=5)', new_titles),\n",
    "#                'simple maze novelty reward with 5 step reward planning'\n",
    "              ]\n",
    "colors = ['orange', 'purple', 'green', 'red', 'brown', 'cyan']\n",
    "results = {}\n",
    "for exp, title, titles in experiments:\n",
    "    results[exp] = parse_visdom_plot_dir(os.path.join(plot_dir, exp), titles, trials=trials)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 20,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Exploration Factor for ep 0 [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [1.0, 1.0, 0.8666666666666666, 0.9, 0.8400000000000001, 0.8, 0.7714285714285715, 0.7, 0.6222222222222222, 0.6199999999999999, 0.5818181818181818, 0.55, 0.5230769230769231, 0.5142857142857142, 0.5333333333333333, 0.525, 0.5176470588235295, 0.5, 0.5157894736842106, 0.5200000000000001, 0.5238095238095238, 0.5272727272727272, 0.5217391304347825, 0.5166666666666667, 0.52, 0.523076923076923, 0.5259259259259259, 0.5285714285714286, 0.5172413793103449, 0.5133333333333333, 0.5096774193548387, 0.5, 0.49696969696969695, 0.5, 0.5085714285714286, 0.5055555555555555, 0.5081081081081081, 0.5105263157894736, 0.517948717948718, 0.515, 0.5121951219512195, 0.5142857142857143, 0.5116279069767442, 0.5045454545454546, 0.5111111111111112, 0.517391304347826, 0.5148936170212767, 0.5041666666666667, 0.5061224489795919, 0.508, 0.5137254901960785, 0.5115384615384615, 0.5132075471698113, 0.5148148148148148, 0.5163636363636364, 0.5178571428571429, 0.5192982456140351, 0.5172413793103449, 0.5186440677966101, 0.52, 0.5114754098360655, 0.5032258064516129, 0.49523809523809526, 0.490625, 0.49230769230769234, 0.49696969696969695, 0.49253731343283585, 0.4911764705882353, 0.48405797101449277, 0.47714285714285715, 0.476056338028169, 0.4805555555555555, 0.48219178082191777, 0.4891891891891892, 0.49066666666666664, 0.48684210526315785, 0.48311688311688317, 0.48461538461538456, 0.48607594936708864, 0.485, 0.4814814814814815, 0.48048780487804876, 0.48192771084337344, 0.48571428571428577, 0.48235294117647065, 0.48139534883720925, 0.4850574712643678, 0.48409090909090907, 0.48764044943820223, 0.48888888888888893, 0.49010989010989015, 0.48695652173913045, 0.4860215053763441, 0.4851063829787233, 0.4821052631578947, 0.4833333333333333, 0.4783505154639175, 0.47551020408163264, 0.47474747474747475, 0.476, 0.47326732673267324, 0.47058823529411764, 0.4660194174757281, 0.4634615384615385, 0.4647619047619048, 0.4641509433962264, 0.4654205607476635, 0.46296296296296297, 0.46605504587155966, 0.46545454545454545, 0.46486486486486484, 0.4642857142857143, 0.46371681415929206, 0.4649122807017544, 0.4608695652173913, 0.45862068965517244, 0.45811965811965816, 0.4576271186440678, 0.45546218487394957, 0.45499999999999996, 0.4561983471074381, 0.4557377049180328, 0.45528455284552843, 0.4532258064516129, 0.44959999999999994, 0.4492063492063492, 0.44724409448818897, 0.4453125, 0.44341085271317826, 0.44153846153846155, 0.43969465648854966, 0.43787878787878787, 0.437593984962406, 0.43731343283582086, 0.43555555555555553, 0.43382352941176466, 0.435036496350365, 0.43478260869565216, 0.4316546762589928, 0.42857142857142855, 0.4297872340425532, 0.4295774647887324, 0.4293706293706293, 0.4305555555555555, 0.4289655172413793, 0.4273972602739726, 0.4258503401360544, 0.4256756756756757, 0.42684563758389266, 0.4253333333333334, 0.42251655629139073, 0.4223684210526315, 0.4235294117647059, 0.4220779220779221, 0.4193548387096774, 0.41923076923076924, 0.4165605095541401, 0.41645569620253164, 0.41509433962264153, 0.4125, 0.4111801242236025, 0.40864197530864194, 0.40613496932515336, 0.4036585365853658, 0.4012121212121212, 0.3987951807228916, 0.39640718562874255, 0.3952380952380953, 0.3940828402366864, 0.3952941176470589, 0.39649122807017545, 0.39767441860465114, 0.3953757225433526, 0.39425287356321836, 0.3942857142857143, 0.3931818181818182, 0.39322033898305087, 0.3921348314606742, 0.3910614525139665, 0.3911111111111111, 0.39226519337016574, 0.39230769230769236, 0.3901639344262295, 0.38913043478260867, 0.3902702702702703, 0.3903225806451613, 0.3893048128342246, 0.38723404255319144, 0.38518518518518513, 0.38421052631578945, 0.38219895287958117, 0.38020833333333337, 0.37823834196891193, 0.37835051546391746, 0.3784615384615384, 0.376530612244898, 0.3756345177664975, 0.3757575757575758, 0.3748743718592965, 0.37300000000000005, 0.3711442786069652, 0.37128712871287134, 0.3694581280788177, 0.3686274509803921, 0.3687804878048781, 0.36893203883495146, 0.3690821256038648, 0.36826923076923074, 0.36746411483253594, 0.3666666666666667, 0.36587677725118484, 0.3650943396226415, 0.3643192488262911, 0.3635514018691589, 0.36372093023255814, 0.36203703703703705, 0.36036866359447, 0.35963302752293586, 0.35799086757990867, 0.3572727272727273, 0.35656108597285063, 0.35765765765765767, 0.3569506726457399, 0.35535714285714287, 0.3546666666666667, 0.35398230088495575, 0.3541850220264317, 0.3543859649122807, 0.3537117903930131, 0.35304347826086957, 0.35151515151515145, 0.35086206896551725, 0.3502145922746781, 0.34957264957264955, 0.3497872340425532, 0.3483050847457627, 0.34683544303797464, 0.346218487394958, 0.3456066945606694, 0.34500000000000003, 0.34439834024896265, 0.34628099173553717, 0.3465020576131687, 0.34754098360655733, 0.3477551020408163, 0.34796747967479674, 0.34655870445344134, 0.34596774193548385, 0.3461847389558233, 0.3456, 0.3450199203187251, 0.34365079365079365, 0.3422924901185771, 0.34173228346456697, 0.3411764705882353, 0.33984375, 0.33852140077821014, 0.3372093023255814, 0.33590733590733596, 0.33461538461538465, 0.33333333333333337, 0.33282442748091606, 0.33307984790874523, 0.3325757575757576, 0.3313207547169811, 0.331578947368421, 0.33108614232209743, 0.32985074626865674, 0.32936802973977697, 0.3296296296296296, 0.32988929889298896, 0.32941176470588235, 0.3304029304029304, 0.32992700729927005, 0.3287272727272727, 0.327536231884058, 0.32707581227436827, 0.3273381294964029, 0.3268817204301075, 0.3264285714285714, 0.3274021352313167, 0.3269503546099291, 0.32720848056537105, 0.328169014084507, 0.3270175438596491, 0.3265734265734266, 0.3261324041811847, 0.325, 0.3245674740484429, 0.32413793103448274, 0.3237113402061856, 0.323972602739726, 0.32354948805460754, 0.32448979591836735, 0.32406779661016943, 0.3243243243243243, 0.32525252525252524, 0.32416107382550335, 0.3230769230769231, 0.32266666666666666, 0.3222591362126246, 0.3218543046357616, 0.3214521452145215, 0.3203947368421053, 0.32, 0.3196078431372549, 0.3192182410423453, 0.32012987012987015, 0.31974110032362457, 0.31870967741935485, 0.3189710610932476, 0.317948717948718, 0.3169329073482428, 0.3159235668789809, 0.31492063492063493, 0.3139240506329114, 0.31293375394321765, 0.31257861635220124, 0.3115987460815047, 0.31062500000000004, 0.3096573208722741, 0.3086956521739131, 0.3077399380804954, 0.30740740740740746, 0.30646153846153845, 0.305521472392638, 0.3045871559633027, 0.3042682926829268, 0.30334346504559273, 0.30242424242424243, 0.30151057401812686, 0.3006024096385542, 0.3003003003003003, 0.30000000000000004, 0.2991044776119403, 0.29821428571428565, 0.29792284866468843, 0.29704142011834317, 0.296165191740413, 0.29647058823529415, 0.29560117302052785, 0.2947368421052632, 0.29387755102040813, 0.29360465116279066, 0.2927536231884058, 0.29190751445086704, 0.29106628242074933, 0.29022988505747127, 0.28997134670487107, 0.2897142857142857, 0.28888888888888886, 0.2886363636363637, 0.28838526912181306, 0.28757062146892653, 0.2867605633802818, 0.2859550561797753, 0.28515406162464985, 0.2843575418994414, 0.2835654596100279, 0.28277777777777774, 0.28254847645429365, 0.281767955801105, 0.28099173553719003, 0.2802197802197802, 0.27945205479452057, 0.2786885245901639, 0.2779291553133515, 0.27771739130434786, 0.2769647696476965, 0.2762162162162162, 0.27601078167115906, 0.2758064516129032, 0.2750670241286863, 0.27433155080213906, 0.27359999999999995, 0.2728723404255319, 0.2726790450928382, 0.2719576719576719, 0.2712401055408971, 0.2710526315789474, 0.27034120734908135, 0.27015706806282724, 0.2694516971279374, 0.26927083333333335, 0.26961038961038963, 0.2704663212435233, 0.2708010335917313, 0.27113402061855674, 0.2714652956298201, 0.27179487179487183, 0.2716112531969309, 0.27193877551020407, 0.2727735368956743, 0.2720812182741117, 0.2713924050632911, 0.27070707070707073, 0.2700251889168766, 0.2698492462311558, 0.2701754385964912, 0.27, 0.26932668329177056, 0.26865671641791045, 0.2679900744416873, 0.26782178217821784, 0.26814814814814814, 0.26748768472906403, 0.2673218673218673, 0.2671568627450981, 0.26699266503667485, 0.26682926829268294, 0.26666666666666666, 0.26650485436893206, 0.26634382566585957, 0.26618357487922706, 0.26554216867469876, 0.26538461538461544, 0.26522781774580334, 0.26507177033492824, 0.26587112171837707, 0.2661904761904762, 0.2665083135391924, 0.2663507109004739, 0.266193853427896, 0.265566037735849, 0.26494117647058824, 0.26478873239436623, 0.26463700234192034, 0.26448598130841117, 0.2643356643356643, 0.26372093023255816, 0.26310904872389795, 0.26296296296296295, 0.26235565819861434, 0.26175115207373273, 0.2616091954022989, 0.26146788990825687, 0.2608695652173913, 0.26073059360730594, 0.2605922551252847, 0.26045454545454544, 0.2603174603174603, 0.26018099547511314, 0.2600451467268623, 0.25990990990990986, 0.2597752808988764, 0.2596412556053812, 0.2595078299776286, 0.2589285714285714, 0.25835189309576834, 0.25822222222222224, 0.25853658536585367, 0.2588495575221239, 0.25916114790286976, 0.2594713656387665, 0.25978021978021976, 0.25921052631578945, 0.2590809628008753, 0.25895196506550217, 0.25838779956427016, 0.25826086956521743, 0.2577006507592191, 0.25757575757575757, 0.257451403887689, 0.25818965517241377, 0.25806451612903225, 0.25793991416309014, 0.25867237687366174, 0.2581196581196581, 0.2579957356076759, 0.257872340425532, 0.25774946921443737, 0.2572033898305085, 0.2566596194503171, 0.25654008438818565, 0.256421052631579, 0.25672268907563023, 0.2566037735849057, 0.2560669456066945, 0.2559498956158664, 0.25625, 0.2557172557172557, 0.2556016597510373, 0.25548654244306424, 0.25578512396694214, 0.2552577319587629, 0.25473251028806587, 0.2546201232032854, 0.2549180327868853, 0.25480572597137013, 0.25428571428571434, 0.2537678207739308, 0.2532520325203252, 0.2527383367139959, 0.25263157894736843, 0.2529292929292929, 0.2536290322580645, 0.2535211267605634, 0.25381526104417673, 0.2545090180360721, 0.2548, 0.254690618762475, 0.2549800796812749, 0.25526838966202786, 0.25555555555555554, 0.25623762376237624, 0.25652173913043474, 0.25719921104536486, 0.2574803149606299, 0.2577603143418467, 0.2572549019607843, 0.25753424657534246, 0.2578125, 0.2573099415204678, 0.25719844357976657, 0.2574757281553398, 0.25813953488372093, 0.2584139264990329, 0.25907335907335904, 0.2585741811175338, 0.25846153846153846, 0.25873320537428024, 0.2590038314176245, 0.25889101338432124, 0.2587786259541985, 0.25866666666666666, 0.2585551330798479, 0.25806451612903225, 0.2575757575757576, 0.2574669187145558, 0.2577358490566038, 0.25725047080979285, 0.2571428571428572, 0.25666041275797374, 0.2565543071161049, 0.256822429906542, 0.257089552238806, 0.25698324022346364, 0.25687732342007435, 0.25677179962894253, 0.25629629629629636, 0.25582255083179295, 0.255719557195572, 0.2559852670349908, 0.25588235294117645, 0.25577981651376147, 0.2556776556776557, 0.2552102376599635, 0.2551094890510949, 0.25500910746812383, 0.2545454545454545, 0.25444646098003626, 0.2539855072463768, 0.25424954792043397, 0.2545126353790614, 0.2544144144144144, 0.2546762589928057, 0.2545780969479354, 0.25412186379928314, 0.2536672629695885, 0.2532142857142857, 0.2531194295900178, 0.25338078291814947, 0.25328596802841924, 0.2531914893617021, 0.2527433628318584, 0.25229681978798585, 0.2518518518518518, 0.25140845070422535, 0.25131810193321613, 0.251578947368421, 0.25148861646234677, 0.25139860139860143, 0.2513089005235602, 0.25121951219512195, 0.25113043478260866, 0.25069444444444444, 0.25025996533795497, 0.24982698961937713, 0.24974093264248703, 0.24965517241379312, 0.24956970740103274, 0.24948453608247423, 0.2490566037735849, 0.24897260273972602, 0.24888888888888888, 0.24846416382252562, 0.24872231686541735, 0.24863945578231297, 0.24821731748726653, 0.248135593220339, 0.24805414551607444, 0.2483108108108108, 0.24822934232715008, 0.24814814814814815, 0.2480672268907563, 0.2476510067114094, 0.247571189279732, 0.24749163879598662, 0.24707846410684473, 0.24700000000000003, 0.24792013311148087, 0.2478405315614618, 0.24809286898839136, 0.24834437086092714, 0.24826446280991732, 0.2485148514851485, 0.24810543657331138, 0.2480263157894737, 0.24761904761904763, 0.24721311475409835, 0.2471358428805237, 0.24705882352941183, 0.24730831973898862, 0.24723127035830622, 0.24715447154471543, 0.24740259740259743, 0.24700162074554294, 0.24692556634304202, 0.24684975767366718, 0.24774193548387097, 0.24766505636070849, 0.247588424437299, 0.24719101123595505, 0.2467948717948718, 0.24672, 0.24664536741214058, 0.24657097288676236, 0.2461783439490446, 0.24610492845786966, 0.24571428571428572, 0.2456418383518225, 0.2455696202531646, 0.24518167456556084, 0.24542586750788647, 0.24535433070866142, 0.2449685534591195, 0.24458398744113025, 0.24420062695924766, 0.24381846635367763, 0.24343750000000003, 0.24305772230889233, 0.2426791277258567, 0.24230171073094864, 0.24192546583850927, 0.24155038759689923, 0.2414860681114551, 0.241112828438949, 0.24135802469135798, 0.24129429892141757, 0.24123076923076922, 0.24116743471582183, 0.2411042944785276, 0.24073506891271051, 0.24036697247706423, 0.24000000000000005, 0.23963414634146343, 0.23926940639269406, 0.23890577507598784, 0.2385432473444613, 0.2381818181818182, 0.23812405446293497, 0.2377643504531722, 0.23740573152337857, 0.23704819277108435, 0.23669172932330826, 0.2363363363363363, 0.2359820089955023, 0.2359281437125748, 0.23557548579970106, 0.23522388059701496, 0.23487332339791353, 0.23452380952380952, 0.23417533432392273, 0.2338278931750742, 0.23348148148148148, 0.2331360946745562, 0.23279172821270314, 0.23244837758112094, 0.2321060382916053, 0.23176470588235296, 0.23142437591776796, 0.23108504398826982, 0.23103953147877015, 0.2307017543859649, 0.23065693430656933, 0.2306122448979592, 0.2302765647743814, 0.22994186046511628, 0.22960812772133526, 0.22927536231884055, 0.229232995658466, 0.22919075144508674, 0.22886002886002882, 0.22853025936599422, 0.22848920863309355, 0.22844827586206895, 0.2284074605451937, 0.22836676217765045, 0.22832618025751072, 0.2282857142857143, 0.22796005706134093, 0.22763532763532765, 0.22759601706970128, 0.2272727272727273, 0.22695035460992904, 0.22662889518413598, 0.2268741159830269, 0.2268361581920904, 0.22708039492242596, 0.22732394366197184, 0.22728551336146272, 0.22696629213483144, 0.22692847124824683, 0.226890756302521, 0.22657342657342658, 0.22681564245810057, 0.2264993026499303, 0.22618384401114205, 0.22642559109874827, 0.22666666666666666, 0.22662968099861303, 0.2268698060941828, 0.2268326417704011, 0.22651933701657462, 0.2267586206896552, 0.22672176308539943, 0.2264099037138927, 0.2260989010989011, 0.22578875171467763, 0.22547945205479453, 0.22544459644322848, 0.22513661202185792, 0.22482946793997272, 0.22452316076294282, 0.22421768707482995, 0.22391304347826088, 0.2238805970149254, 0.22411924119241192, 0.22381596752368066, 0.22378378378378377, 0.2234817813765182, 0.22345013477088949, 0.2231493943472409, 0.2228494623655914, 0.22281879194630871, 0.2225201072386059, 0.22222222222222224, 0.22192513368983957, 0.2216288384512684, 0.22160000000000002, 0.2213049267643142, 0.22101063829787235, 0.2207171314741036, 0.2204244031830239, 0.2201324503311258, 0.21984126984126987, 0.21981505944517835, 0.21952506596306068, 0.21923583662714097, 0.2189473684210526, 0.21865965834428383, 0.2183727034120735, 0.218348623853211, 0.2183246073298429, 0.21830065359477122, 0.21801566579634465, 0.21799217731421122, 0.21822916666666664, 0.2179453836150845, 0.21792207792207793, 0.21789883268482488, 0.2178756476683938, 0.21785252263906857, 0.21782945736434106, 0.21754838709677418, 0.21726804123711338, 0.216988416988417, 0.21670951156812338, 0.21643132220795894, 0.21615384615384614, 0.21587708066581307, 0.21585677749360613, 0.215581098339719, 0.21556122448979592, 0.21554140127388535, 0.21603053435114505, 0.2162642947903431, 0.21624365482233499, 0.2162230671736375, 0.2159493670886076, 0.21567635903919088, 0.2154040404040404, 0.21563682219419925, 0.2158690176322418, 0.2161006289308176, 0.21608040201005027, 0.21606022584692597, 0.21578947368421053, 0.2160200250312891, 0.21625, 0.21598002496878901, 0.21596009975062347, 0.21618929016189292, 0.21616915422885574, 0.21590062111801242, 0.21563275434243176, 0.215365551425031, 0.2150990099009901, 0.21508034610630405, 0.2150617283950617, 0.21528976572133168, 0.21576354679802953, 0.21599015990159903, 0.21597051597051595, 0.21644171779141103, 0.21617647058823533, 0.21591187270501835, 0.2156479217603912, 0.21538461538461542, 0.21512195121951216, 0.21485992691839217, 0.21484184914841847, 0.21458080194410695, 0.21432038834951456, 0.21406060606060606, 0.21428571428571427, 0.2145102781136638, 0.21449275362318837, 0.21447527141133893, 0.21421686746987953, 0.21419975932611313, 0.2144230769230769, 0.21440576230492198, 0.2143884892086331, 0.21437125748502991, 0.21411483253588517, 0.2138590203106332, 0.21360381861575178, 0.21334922526817643, 0.2130952380952381, 0.21284185493460167, 0.2125890736342043, 0.21233689205219455, 0.21232227488151656, 0.2123076923076923, 0.21205673758865248, 0.21180637544273911, 0.21155660377358493, 0.21154299175500593, 0.2117647058823529, 0.21198589894242068, 0.2124413145539906, 0.21242672919109026, 0.2126463700234192, 0.2128654970760234, 0.21285046728971962, 0.21306884480746793, 0.21328671328671328, 0.21327124563445868, 0.21325581395348836, 0.21347270615563296, 0.21345707656612528, 0.21344148319814601, 0.2131944444444444, 0.21294797687861272, 0.21293302540415704, 0.2126874279123414, 0.21290322580645166, 0.2126582278481013, 0.2126436781609195, 0.21308840413318025, 0.21330275229357798, 0.21328751431844215, 0.2137299771167048, 0.21417142857142854, 0.2146118721461187, 0.2143671607753706, 0.21457858769931662, 0.2150170648464164, 0.21522727272727274, 0.21543700340522137, 0.21564625850340136, 0.21562853907134766, 0.21561085972850677, 0.21559322033898304, 0.2155756207674943, 0.21578354002254793, 0.215990990990991, 0.21619797525309337, 0.21617977528089888, 0.2159371492704826, 0.21591928251121076, 0.21612541993281073, 0.21655480984340042, 0.21653631284916203, 0.21651785714285715, 0.21672240802675585, 0.21670378619153677, 0.21668520578420467, 0.21688888888888885, 0.21753607103218647, 0.217960088691796, 0.21816168327796234, 0.21836283185840707, 0.2183425414364641, 0.21810154525386313, 0.21808158765159869, 0.21784140969162996, 0.2178217821782178, 0.21780219780219778, 0.21756311745334794, 0.21776315789473685, 0.21774370208105145, 0.21772428884026257, 0.21792349726775956, 0.21790393013100434, 0.21810250817884405, 0.21830065359477127, 0.21828073993471162, 0.21826086956521742, 0.2184581976112921, 0.21843817787418654, 0.2184182015167931, 0.21861471861471862, 0.21837837837837842, 0.21835853131749458, 0.21833872707659116, 0.21810344827586206, 0.2182992465016146, 0.2182795698924731, 0.2182599355531686, 0.21802575107296135, 0.2177920685959271, 0.21777301927194861, 0.21775401069518718, 0.2177350427350427, 0.2179295624332978, 0.21812366737739874, 0.21810436634717786, 0.2178723404255319, 0.21806588735387886, 0.21825902335456476, 0.21866383881230117, 0.21885593220338984, 0.21883597883597883, 0.21902748414376322, 0.21900739176346357, 0.2191983122362869, 0.21938883034773443, 0.21936842105263157, 0.21934805467928492, 0.219327731092437, 0.2197271773347324, 0.22012578616352202, 0.2201047120418848, 0.2200836820083682, 0.22048066875653083, 0.22066805845511483, 0.2204379562043796, 0.22020833333333328, 0.22018730489073884, 0.22037422037422041, 0.2201453790238837, 0.22012448132780085, 0.22031088082901557, 0.22028985507246376, 0.22006204756980355, 0.21983471074380168, 0.21960784313725493, 0.21938144329896908, 0.2191555097837281, 0.2191358024691358, 0.21891058581706066, 0.21909650924024637, 0.2192820512820513, 0.2192622950819672, 0.2194472876151484, 0.21922290388548057, 0.21899897854954037, 0.21918367346938777, 0.21896024464831804, 0.21873727087576372, 0.2185147507629705, 0.2184959349593496, 0.21847715736040607, 0.21845841784989856, 0.21843971631205675, 0.2186234817813765, 0.21860465116279068, 0.21858585858585858, 0.21856710393541876, 0.21875, 0.21893252769385702, 0.21911468812877266, 0.2192964824120603, 0.2190763052208835, 0.21885656970912737, 0.21863727454909823, 0.21841841841841844, 0.2182], 'name': 'baseline', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}, {'x': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0, 1000.0], 'y': [1.0, 1.0, 1.0, 1.0, 0.8, 0.8333333333333334, 0.7142857142857143, 0.625, 0.5555555555555556, 0.5, 0.45454545454545453, 0.5, 0.5384615384615384, 0.5714285714285714, 0.6, 0.625, 0.5882352941176471, 0.6111111111111112, 0.5789473684210527, 0.55, 0.5714285714285714, 0.5454545454545454, 0.5217391304347826, 0.5, 0.52, 0.5, 0.48148148148148145, 0.4642857142857143, 0.4827586206896552, 0.5, 0.5161290322580645, 0.53125, 0.5151515151515151, 0.5, 0.5142857142857142, 0.5, 0.4864864864864865, 0.47368421052631576, 0.46153846153846156, 0.475, 0.4878048780487805, 0.5, 0.5116279069767442, 0.5227272727272727, 0.5333333333333333, 0.5217391304347826, 0.5106382978723404, 0.5, 0.5102040816326531, 0.5, 0.49019607843137253, 0.5, 0.49056603773584906, 0.5, 0.509090909090909, 0.5, 0.49122807017543857, 0.4827586206896552, 0.4915254237288136, 0.5, 0.5081967213114754, 0.5161290322580645, 0.5238095238095238, 0.53125, 0.5384615384615384, 0.5454545454545454, 0.5373134328358209, 0.5294117647058824, 0.5362318840579711, 0.5428571428571428, 0.5492957746478874, 0.5555555555555556, 0.5616438356164384, 0.5540540540540541, 0.5466666666666666, 0.5526315789473685, 0.5454545454545454, 0.5384615384615384, 0.5443037974683544, 0.55, 0.5555555555555556, 0.5609756097560976, 0.5542168674698795, 0.5476190476190477, 0.5411764705882353, 0.5348837209302325, 0.5287356321839081, 0.5227272727272727, 0.5168539325842697, 0.5111111111111111, 0.5054945054945055, 0.5, 0.4946236559139785, 0.5, 0.5052631578947369, 0.5104166666666666, 0.5154639175257731, 0.5204081632653061, 0.5252525252525253, 0.53, 0.5346534653465347, 0.5392156862745098, 0.5436893203883495, 0.5480769230769231, 0.5523809523809524, 0.5566037735849056, 0.5514018691588785, 0.5462962962962963, 0.5412844036697247, 0.5363636363636364, 0.5315315315315315, 0.5267857142857143, 0.5221238938053098, 0.5175438596491229, 0.5130434782608696, 0.5086206896551724, 0.5042735042735043, 0.5, 0.4957983193277311, 0.49166666666666664, 0.48760330578512395, 0.48360655737704916, 0.4796747967479675, 0.47580645161290325, 0.472, 0.46825396825396826, 0.4645669291338583, 0.4609375, 0.4573643410852713, 0.45384615384615384, 0.45038167938931295, 0.44696969696969696, 0.44360902255639095, 0.44029850746268656, 0.43703703703703706, 0.4338235294117647, 0.4306569343065693, 0.427536231884058, 0.4244604316546763, 0.42142857142857143, 0.41843971631205673, 0.4154929577464789, 0.4125874125874126, 0.4097222222222222, 0.4068965517241379, 0.4041095890410959, 0.4013605442176871, 0.39864864864864863, 0.3959731543624161, 0.3933333333333333, 0.39072847682119205, 0.3881578947368421, 0.38562091503267976, 0.38311688311688313, 0.38064516129032255, 0.3782051282051282, 0.37579617834394907, 0.37341772151898733, 0.3710691823899371, 0.36875, 0.36645962732919257, 0.36419753086419754, 0.3619631901840491, 0.3597560975609756, 0.3575757575757576, 0.35542168674698793, 0.3532934131736527, 0.35119047619047616, 0.34911242603550297, 0.34705882352941175, 0.34502923976608185, 0.3430232558139535, 0.34104046242774566, 0.3390804597701149, 0.33714285714285713, 0.3352272727272727, 0.3333333333333333, 0.33146067415730335, 0.329608938547486, 0.3277777777777778, 0.3259668508287293, 0.3241758241758242, 0.3224043715846995, 0.32065217391304346, 0.31891891891891894, 0.3172043010752688, 0.3155080213903743, 0.31382978723404253, 0.31216931216931215, 0.3105263157894737, 0.3089005235602094, 0.3072916666666667, 0.30569948186528495, 0.30412371134020616, 0.30256410256410254, 0.3010204081632653, 0.29949238578680204, 0.29797979797979796, 0.2964824120603015, 0.295, 0.2935323383084577, 0.29207920792079206, 0.29064039408866993, 0.28921568627450983, 0.28780487804878047, 0.28640776699029125, 0.28502415458937197, 0.28365384615384615, 0.2822966507177033, 0.28095238095238095, 0.2796208530805687, 0.2783018867924528, 0.27699530516431925, 0.2757009345794392, 0.2744186046511628, 0.27314814814814814, 0.271889400921659, 0.2706422018348624, 0.2694063926940639, 0.2681818181818182, 0.2669683257918552, 0.26576576576576577, 0.2645739910313901, 0.26339285714285715, 0.26222222222222225, 0.2610619469026549, 0.2599118942731278, 0.25877192982456143, 0.2576419213973799, 0.2565217391304348, 0.2554112554112554, 0.2543103448275862, 0.2532188841201717, 0.25213675213675213, 0.251063829787234, 0.25, 0.2489451476793249, 0.24789915966386555, 0.24686192468619247, 0.24583333333333332, 0.24481327800829875, 0.24380165289256198, 0.24279835390946503, 0.24180327868852458, 0.24081632653061225, 0.23983739837398374, 0.2388663967611336, 0.23790322580645162, 0.23694779116465864, 0.236, 0.2350597609561753, 0.23412698412698413, 0.233201581027668, 0.23228346456692914, 0.23137254901960785, 0.23046875, 0.22957198443579765, 0.22868217054263565, 0.2277992277992278, 0.22692307692307692, 0.2260536398467433, 0.22519083969465647, 0.22433460076045628, 0.22348484848484848, 0.22264150943396227, 0.22180451127819548, 0.2209737827715356, 0.22014925373134328, 0.21933085501858737, 0.21851851851851853, 0.2177121771217712, 0.21691176470588236, 0.21611721611721613, 0.21532846715328466, 0.21454545454545454, 0.213768115942029, 0.21299638989169675, 0.21223021582733814, 0.2114695340501792, 0.21071428571428572, 0.2099644128113879, 0.20921985815602837, 0.20848056537102475, 0.20774647887323944, 0.20701754385964913, 0.2062937062937063, 0.20557491289198607, 0.2048611111111111, 0.2041522491349481, 0.20344827586206896, 0.2027491408934708, 0.20205479452054795, 0.20136518771331058, 0.20068027210884354, 0.2, 0.19932432432432431, 0.19865319865319866, 0.19798657718120805, 0.19732441471571907, 0.19666666666666666, 0.19601328903654486, 0.19536423841059603, 0.19471947194719472, 0.19407894736842105, 0.19344262295081968, 0.19281045751633988, 0.19218241042345277, 0.19155844155844157, 0.19093851132686085, 0.19032258064516128, 0.18971061093247588, 0.1891025641025641, 0.18849840255591055, 0.18789808917197454, 0.1873015873015873, 0.18670886075949367, 0.1861198738170347, 0.18553459119496854, 0.18495297805642633, 0.184375, 0.1838006230529595, 0.18322981366459629, 0.1826625386996904, 0.18209876543209877, 0.18153846153846154, 0.18098159509202455, 0.18042813455657492, 0.1798780487804878, 0.17933130699088146, 0.1787878787878788, 0.1782477341389728, 0.17771084337349397, 0.17717717717717718, 0.17664670658682635, 0.1761194029850746, 0.17559523809523808, 0.17507418397626112, 0.17455621301775148, 0.17404129793510326, 0.17352941176470588, 0.17302052785923755, 0.17251461988304093, 0.17201166180758018, 0.17151162790697674, 0.17101449275362318, 0.17052023121387283, 0.17002881844380405, 0.16954022988505746, 0.16905444126074498, 0.16857142857142857, 0.16809116809116809, 0.16761363636363635, 0.1671388101983003, 0.16666666666666666, 0.16619718309859155, 0.16573033707865167, 0.16526610644257703, 0.164804469273743, 0.16434540389972144, 0.1638888888888889, 0.1634349030470914, 0.16298342541436464, 0.162534435261708, 0.1620879120879121, 0.16164383561643836, 0.16120218579234974, 0.16076294277929154, 0.16032608695652173, 0.15989159891598917, 0.15945945945945947, 0.15902964959568733, 0.1586021505376344, 0.1581769436997319, 0.15775401069518716, 0.15733333333333333, 0.15691489361702127, 0.15649867374005305, 0.15608465608465608, 0.15567282321899736, 0.15526315789473685, 0.15485564304461943, 0.1544502617801047, 0.15404699738903394, 0.15364583333333334, 0.15324675324675324, 0.15284974093264247, 0.1524547803617571, 0.15206185567010308, 0.15167095115681234, 0.15128205128205127, 0.15089514066496162, 0.15051020408163265, 0.15012722646310434, 0.14974619289340102, 0.14936708860759493, 0.14898989898989898, 0.1486146095717884, 0.14824120603015076, 0.14786967418546365, 0.1475, 0.14713216957605985, 0.14676616915422885, 0.14640198511166252, 0.14603960396039603, 0.145679012345679, 0.14532019704433496, 0.14496314496314497, 0.14460784313725492, 0.14425427872860636, 0.14390243902439023, 0.1435523114355231, 0.14320388349514562, 0.14285714285714285, 0.14251207729468598, 0.14216867469879518, 0.14182692307692307, 0.14148681055155876, 0.14114832535885166, 0.14081145584725538, 0.14047619047619048, 0.14014251781472684, 0.13981042654028436, 0.13947990543735225, 0.1391509433962264, 0.1388235294117647, 0.13849765258215962, 0.13817330210772832, 0.1378504672897196, 0.13752913752913754, 0.1372093023255814, 0.1368909512761021, 0.13657407407407407, 0.13625866050808313, 0.1359447004608295, 0.135632183908046, 0.1353211009174312, 0.13501144164759726, 0.13470319634703196, 0.13439635535307518, 0.1340909090909091, 0.13378684807256236, 0.1334841628959276, 0.13318284424379231, 0.13288288288288289, 0.13258426966292136, 0.13228699551569506, 0.1319910514541387, 0.13169642857142858, 0.13140311804008908, 0.13111111111111112, 0.13082039911308205, 0.13053097345132744, 0.13024282560706402, 0.1299559471365639, 0.12967032967032968, 0.12938596491228072, 0.12910284463894967, 0.12882096069868995, 0.12854030501089325, 0.1282608695652174, 0.1279826464208243, 0.1277056277056277, 0.12742980561555076, 0.1271551724137931, 0.12688172043010754, 0.12660944206008584, 0.12633832976445397, 0.12606837606837606, 0.1257995735607676, 0.125531914893617, 0.12526539278131635, 0.125, 0.12473572938689217, 0.12447257383966245, 0.12421052631578948, 0.12394957983193278, 0.12368972746331237, 0.12343096234309624, 0.12317327766179541, 0.12291666666666666, 0.12266112266112267, 0.12240663900414937, 0.12215320910973085, 0.12190082644628099, 0.12164948453608247, 0.12139917695473251, 0.12114989733059549, 0.12090163934426229, 0.12065439672801637, 0.12040816326530612, 0.12016293279022404, 0.11991869918699187, 0.11967545638945233, 0.1194331983805668, 0.1191919191919192, 0.11895161290322581, 0.11871227364185111, 0.11847389558232932, 0.11823647294589178, 0.118, 0.11776447105788423, 0.11752988047808766, 0.1172962226640159, 0.11706349206349206, 0.11683168316831684, 0.116600790513834, 0.11637080867850098, 0.11614173228346457, 0.11591355599214145, 0.11568627450980393, 0.11545988258317025, 0.115234375, 0.11500974658869395, 0.11478599221789883, 0.1145631067961165, 0.11434108527131782, 0.11411992263056092, 0.1138996138996139, 0.11368015414258188, 0.11346153846153846, 0.11324376199616124, 0.11302681992337164, 0.11281070745697896, 0.11259541984732824, 0.11238095238095239, 0.11216730038022814, 0.11195445920303605, 0.11174242424242424, 0.11153119092627599, 0.11132075471698114, 0.1111111111111111, 0.11090225563909774, 0.11069418386491557, 0.1104868913857678, 0.1102803738317757, 0.11007462686567164, 0.10986964618249534, 0.10966542750929369, 0.10946196660482375, 0.10925925925925926, 0.10905730129390019, 0.1088560885608856, 0.10865561694290976, 0.10845588235294118, 0.10825688073394496, 0.10805860805860806, 0.10786106032906764, 0.10766423357664233, 0.10746812386156648, 0.10727272727272727, 0.10707803992740472, 0.1068840579710145, 0.10669077757685352, 0.10649819494584838, 0.1063063063063063, 0.10611510791366907, 0.1059245960502693, 0.1057347670250896, 0.10554561717352415, 0.10535714285714286, 0.1051693404634581, 0.10498220640569395, 0.10479573712255773, 0.10460992907801418, 0.10442477876106195, 0.10424028268551237, 0.10405643738977072, 0.10387323943661972, 0.10369068541300527, 0.10350877192982456, 0.10332749562171628, 0.10314685314685315, 0.10296684118673648, 0.10278745644599303, 0.10260869565217391, 0.10243055555555555, 0.1022530329289428, 0.10207612456747404, 0.10189982728842832, 0.10172413793103448, 0.10154905335628227, 0.1013745704467354, 0.10120068610634649, 0.10102739726027397, 0.10085470085470086, 0.10068259385665529, 0.10051107325383304, 0.10034013605442177, 0.100169779286927, 0.1, 0.09983079526226735, 0.09966216216216216, 0.09949409780775717, 0.09932659932659933, 0.09915966386554621, 0.09899328859060402, 0.09882747068676717, 0.09866220735785954, 0.09849749582637729, 0.09833333333333333, 0.09816971713810316, 0.09800664451827243, 0.0978441127694859, 0.09768211920529801, 0.09752066115702479, 0.09735973597359736, 0.09719934102141681, 0.09703947368421052, 0.09688013136288999, 0.09672131147540984, 0.09656301145662848, 0.09640522875816994, 0.09624796084828711, 0.09609120521172639, 0.0959349593495935, 0.09577922077922078, 0.09562398703403566, 0.09546925566343042, 0.09531502423263329, 0.09516129032258064, 0.09500805152979067, 0.09485530546623794, 0.09470304975922954, 0.09455128205128205, 0.0944, 0.09424920127795527, 0.09409888357256778, 0.09394904458598727, 0.09379968203497616, 0.09365079365079365, 0.09350237717908082, 0.09335443037974683, 0.09320695102685624, 0.09305993690851735, 0.09291338582677165, 0.09276729559748427, 0.09262166405023547, 0.09247648902821316, 0.09233176838810642, 0.0921875, 0.09204368174726989, 0.09190031152647975, 0.09175738724727839, 0.09161490683229814, 0.09147286821705426, 0.0913312693498452, 0.09119010819165378, 0.09104938271604938, 0.09090909090909091, 0.09076923076923077, 0.09062980030721966, 0.09049079754601227, 0.0903522205206738, 0.09021406727828746, 0.0900763358778626, 0.0899390243902439, 0.0898021308980213, 0.08966565349544073, 0.08952959028831563, 0.0893939393939394, 0.08925869894099848, 0.0891238670694864, 0.0889894419306184, 0.08885542168674698, 0.0887218045112782, 0.08858858858858859, 0.08845577211394302, 0.08832335329341318, 0.08819133034379671, 0.0880597014925373, 0.08792846497764531, 0.08779761904761904, 0.08766716196136701, 0.08753709198813056, 0.0874074074074074, 0.08727810650887574, 0.08714918759231906, 0.08702064896755163, 0.08689248895434462, 0.08676470588235294, 0.08663729809104258, 0.08651026392961877, 0.08638360175695461, 0.08625730994152046, 0.08613138686131387, 0.08600583090379009, 0.0858806404657933, 0.08575581395348837, 0.08563134978229318, 0.08550724637681159, 0.085383502170767, 0.08526011560693642, 0.08513708513708514, 0.08501440922190202, 0.08489208633093526, 0.08477011494252873, 0.08464849354375897, 0.08452722063037249, 0.0844062947067239, 0.08428571428571428, 0.08416547788873038, 0.08404558404558404, 0.08392603129445235, 0.08380681818181818, 0.08368794326241134, 0.08356940509915015, 0.08345120226308345, 0.08333333333333333, 0.08321579689703808, 0.08309859154929577, 0.0829817158931083, 0.08286516853932584, 0.08274894810659186, 0.08263305322128851, 0.08251748251748252, 0.0824022346368715, 0.08228730822873083, 0.08217270194986072, 0.08344923504867872, 0.08472222222222223, 0.08599167822468794, 0.08725761772853186, 0.08852005532503458, 0.08977900552486189, 0.0910344827586207, 0.09228650137741047, 0.09353507565337002, 0.09478021978021978, 0.09602194787379972, 0.09726027397260274, 0.09849521203830369, 0.09972677595628415, 0.1009549795361528, 0.10217983651226158, 0.10340136054421768, 0.10461956521739131, 0.1044776119402985, 0.1043360433604336, 0.10419485791610285, 0.10405405405405406, 0.1039136302294197, 0.10377358490566038, 0.10497981157469717, 0.10618279569892473, 0.10604026845637583, 0.10589812332439678, 0.10575635876840696, 0.10561497326203209, 0.1068090787716956, 0.10666666666666667, 0.10652463382157124, 0.10638297872340426, 0.10624169986719788, 0.10742705570291777, 0.10728476821192053, 0.10714285714285714, 0.10700132100396301, 0.10686015831134564, 0.1067193675889328, 0.10789473684210527, 0.10906701708278581, 0.11023622047244094, 0.11140235910878113, 0.112565445026178, 0.11241830065359477, 0.1122715404699739, 0.1121251629726206, 0.11197916666666667, 0.11183355006501951, 0.11168831168831168, 0.11154345006485085, 0.11139896373056994, 0.111254851228978, 0.1111111111111111, 0.11096774193548387, 0.11082474226804123, 0.11068211068211069, 0.11053984575835475, 0.1116816431322208, 0.11153846153846154, 0.11139564660691421, 0.11125319693094629, 0.1111111111111111, 0.11096938775510204, 0.11082802547770701, 0.11068702290076336, 0.11054637865311309, 0.11040609137055837, 0.11153358681875793, 0.11139240506329114, 0.1125158027812895, 0.11363636363636363, 0.11475409836065574, 0.11586901763224182, 0.1169811320754717, 0.11809045226130653, 0.11794228356336262, 0.11779448621553884, 0.11889862327909888, 0.12, 0.12109862671660425, 0.12219451371571072, 0.1232876712328767, 0.12437810945273632, 0.12546583850931678, 0.12655086848635236, 0.12763320941759604, 0.12871287128712872, 0.12855377008652658, 0.12839506172839507, 0.12946979038224415, 0.12931034482758622, 0.12915129151291513, 0.128992628992629, 0.13006134969325153, 0.12990196078431374, 0.12974296205630356, 0.1295843520782396, 0.13064713064713065, 0.13048780487804879, 0.13032886723507917, 0.13017031630170317, 0.13122721749696234, 0.13106796116504854, 0.13090909090909092, 0.13075060532687652, 0.13180169286577992, 0.1316425120772947, 0.13148371531966224, 0.13132530120481928, 0.13237063778580024, 0.13221153846153846, 0.13205282112845138, 0.13189448441247004, 0.13293413173652693, 0.13277511961722488, 0.13261648745519714, 0.1324582338902148, 0.13230035756853398, 0.13214285714285715, 0.13317479191438764, 0.1330166270783848, 0.132858837485172, 0.13270142180094788, 0.13254437869822486, 0.13356973995271867, 0.1334120425029516, 0.1332547169811321, 0.1330977620730271, 0.13294117647058823, 0.13396004700352526, 0.13380281690140844, 0.1336459554513482, 0.13348946135831383, 0.13333333333333333, 0.13317757009345793, 0.13302217036172695, 0.13403263403263405, 0.13387660069848661, 0.13372093023255813, 0.13472706155632985, 0.1357308584686775, 0.13673232908458866, 0.13773148148148148, 0.1375722543352601, 0.13856812933025403, 0.1395617070357555, 0.14055299539170507, 0.14154200230149597, 0.1425287356321839, 0.14351320321469574, 0.1444954128440367, 0.145475372279496, 0.14530892448512586, 0.14514285714285713, 0.1461187214611872, 0.1459521094640821, 0.14578587699316628, 0.14562002275312855, 0.14659090909090908, 0.14642451759364358, 0.14625850340136054, 0.14609286523216308, 0.14592760180995476, 0.14689265536723164, 0.14672686230248308, 0.14656144306651633, 0.1463963963963964, 0.14623172103487064, 0.14719101123595504, 0.14702581369248036, 0.1468609865470852, 0.14669652855543114, 0.1465324384787472, 0.14748603351955308, 0.1484375, 0.14827201783723523, 0.14810690423162584, 0.14905450500556172, 0.15, 0.1509433962264151, 0.15188470066518847, 0.15282392026578073, 0.15376106194690264, 0.15469613259668508, 0.1545253863134658, 0.15545755237045203, 0.15638766519823788, 0.1573157315731573, 0.15824175824175823, 0.15916575192096596, 0.1600877192982456, 0.15991237677984665, 0.15973741794310722, 0.15956284153005465, 0.15938864628820962, 0.15921483097055616, 0.15904139433551198, 0.15995647442872687, 0.15978260869565217, 0.15960912052117263, 0.1594360086767896, 0.15926327193932827, 0.16017316017316016, 0.16108108108108107, 0.16090712742980562, 0.16073354908306364, 0.16056034482758622, 0.1603875134553283, 0.16021505376344086, 0.16004296455424274, 0.15987124463519314, 0.15969989281886388, 0.15952890792291222, 0.15935828877005348, 0.15918803418803418, 0.15901814300960512, 0.15884861407249468, 0.15867944621938232, 0.15851063829787235, 0.15834218916046758, 0.1592356687898089, 0.15906680805938495, 0.15889830508474576, 0.15978835978835979, 0.160676532769556, 0.16050686378035903, 0.16033755274261605, 0.1601685985247629, 0.16, 0.1598317560462671, 0.16071428571428573, 0.16054564533053514, 0.16037735849056603, 0.16020942408376965, 0.16108786610878661, 0.16091954022988506, 0.16075156576200417, 0.16058394160583941, 0.16041666666666668, 0.16129032258064516, 0.16112266112266113, 0.16095534787123572, 0.1607883817427386, 0.16062176165803108, 0.16045548654244307, 0.16132368148914167, 0.16115702479338842, 0.1609907120743034, 0.16082474226804125, 0.16065911431513905, 0.16049382716049382, 0.16032887975334018, 0.1601642710472279, 0.16102564102564101, 0.16086065573770492, 0.16069600818833163, 0.1605316973415133, 0.16036772216547499, 0.16122448979591836, 0.1620795107033639, 0.16191446028513237, 0.1617497456765005, 0.16158536585365854, 0.16142131979695432, 0.1612576064908722, 0.16109422492401215, 0.16093117408906882, 0.16076845298281092, 0.1606060606060606, 0.16044399596367306, 0.16028225806451613, 0.16012084592145015, 0.15995975855130784, 0.15979899497487438, 0.15963855421686746, 0.15947843530591777, 0.1593186372745491, 0.15915915915915915, 0.159], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n",
      "Ratio of states visited for ep 0 [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [0.002770083102493075, 0.00554016620498615, 0.007202216066481995, 0.00997229916897507, 0.011634349030470914, 0.01329639889196676, 0.014958448753462602, 0.015512465373961217, 0.015512465373961217, 0.017174515235457065, 0.01772853185595568, 0.018282548476454295, 0.01883656509695291, 0.019944598337950138, 0.0221606648199446, 0.023268698060941825, 0.024376731301939056, 0.02493074792243767, 0.027146814404432135, 0.02880886426592798, 0.03047091412742382, 0.03213296398891967, 0.0332409972299169, 0.03434903047091413, 0.036011080332409975, 0.037673130193905814, 0.03933518005540167, 0.040997229916897505, 0.04155124653739613, 0.04265927977839336, 0.04376731301939059, 0.0443213296398892, 0.04542936288088643, 0.04709141274238228, 0.04930747922437673, 0.05041551246537396, 0.05207756232686981, 0.05373961218836565, 0.05595567867036011, 0.05706371191135734, 0.058171745152354584, 0.05983379501385042, 0.06094182825484765, 0.06149584487534626, 0.06371191135734072, 0.0659279778393352, 0.06703601108033241, 0.06703601108033241, 0.06869806094182826, 0.0703601108033241, 0.07257617728531855, 0.07368421052631578, 0.07534626038781164, 0.07700831024930747, 0.07867036011080333, 0.08033240997229918, 0.08199445983379502, 0.08310249307479226, 0.08476454293628809, 0.08642659279778395, 0.08642659279778395, 0.08642659279778395, 0.08642659279778395, 0.08698060941828255, 0.08864265927977841, 0.09085872576177284, 0.09141274238227146, 0.09252077562326869, 0.09252077562326869, 0.09252077562326869, 0.09362880886426592, 0.09584487534626039, 0.09750692520775624, 0.1002770083102493, 0.10193905817174516, 0.10249307479224376, 0.1030470914127424, 0.10470914127423825, 0.10637119113573408, 0.10747922437673132, 0.10803324099722993, 0.10914127423822714, 0.110803324099723, 0.11301939058171744, 0.11357340720221606, 0.1146814404432133, 0.11689750692520776, 0.11800554016620497, 0.12022160664819943, 0.1218836565096953, 0.12354570637119114, 0.12409972299168974, 0.12520775623268698, 0.12631578947368421, 0.12686980609418283, 0.12853185595567868, 0.12853185595567868, 0.12908587257617726, 0.1301939058171745, 0.1318559556786704, 0.132409972299169, 0.1329639889196676, 0.1329639889196676, 0.1335180055401662, 0.13518005540166206, 0.13628808864265926, 0.1379501385041551, 0.13850415512465375, 0.1407202216066482, 0.14182825484764544, 0.14293628808864267, 0.1440443213296399, 0.1451523545706371, 0.14681440443213298, 0.14681440443213298, 0.1473684210526316, 0.14847645429362882, 0.14958448753462605, 0.15013850415512467, 0.15124653739612187, 0.15290858725761775, 0.15401662049861495, 0.15512465373961218, 0.1556786703601108, 0.1556786703601108, 0.15678670360110802, 0.15734072022160664, 0.15789473684210525, 0.1584487534626039, 0.1590027700831025, 0.15955678670360113, 0.16011080332409972, 0.16121883656509697, 0.16232686980609418, 0.1628808864265928, 0.16343490304709146, 0.16509695290858728, 0.1662049861495845, 0.1662049861495845, 0.1662049861495845, 0.16786703601108033, 0.16897506925207756, 0.17008310249307476, 0.1717451523545706, 0.17229916897506922, 0.1728531855955679, 0.17340720221606648, 0.17451523545706374, 0.17617728531855956, 0.17673130193905817, 0.17673130193905817, 0.1778393351800554, 0.17950138504155125, 0.18005540166204986, 0.18005540166204986, 0.18116343490304707, 0.18116343490304707, 0.18227146814404432, 0.1828254847645429, 0.1828254847645429, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18393351800554014, 0.18448753462603878, 0.18614958448753463, 0.18781163434903045, 0.18947368421052632, 0.18947368421052632, 0.19002770083102494, 0.19113573407202217, 0.19168975069252076, 0.19279778393351804, 0.19335180055401663, 0.19390581717451524, 0.19501385041551247, 0.1966759002770083, 0.19778393351800555, 0.19778393351800555, 0.19833795013850417, 0.2, 0.20110803324099727, 0.20166204986149588, 0.20166204986149588, 0.20166204986149588, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.2033240997229917, 0.20443213296398893, 0.20443213296398893, 0.20498614958448752, 0.20609418282548475, 0.20664819944598337, 0.20664819944598337, 0.20664819944598337, 0.20775623268698062, 0.20775623268698062, 0.20831024930747924, 0.20941828254847644, 0.21052631578947367, 0.2116343490304709, 0.21218836565096955, 0.21274238227146816, 0.21329639889196678, 0.21385041551246536, 0.21440443213296398, 0.2149584487534626, 0.2155124653739612, 0.21662049861495847, 0.21662049861495847, 0.21662049861495847, 0.21717451523545708, 0.21717451523545708, 0.2177285318559557, 0.21828254847645429, 0.21994459833795013, 0.22049861495844877, 0.22049861495844877, 0.22105263157894733, 0.221606648199446, 0.2227146814404432, 0.22382271468144044, 0.22437673130193905, 0.22493074792243767, 0.22493074792243767, 0.22548476454293626, 0.22603878116343487, 0.2265927977839335, 0.22770083102493074, 0.22770083102493074, 0.22770083102493074, 0.22825484764542942, 0.22880886426592797, 0.22936288088642662, 0.22991689750692518, 0.23213296398891967, 0.2332409972299169, 0.23490304709141271, 0.23601108033240997, 0.2371191135734072, 0.2371191135734072, 0.23767313019390582, 0.23878116343490302, 0.23933518005540164, 0.23988919667590025, 0.23988919667590025, 0.23988919667590025, 0.2404432132963989, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24155124653739612, 0.24265927977839336, 0.24321329639889194, 0.24321329639889194, 0.24432132963988917, 0.24487534626038782, 0.24487534626038782, 0.24542936288088643, 0.24653739612188366, 0.24764542936288086, 0.24819944598337948, 0.24986149584487535, 0.25041551246537397, 0.25041551246537397, 0.25041551246537397, 0.25096952908587256, 0.2520775623268698, 0.25263157894736843, 0.253185595567867, 0.2548476454293629, 0.2554016620498615, 0.2565096952908587, 0.2581717451523546, 0.2581717451523546, 0.2587257617728532, 0.2592797783933518, 0.2592797783933518, 0.2598337950138504, 0.26038781163434904, 0.26094182825484763, 0.26204986149584486, 0.26260387811634345, 0.2642659279778393, 0.2648199445983379, 0.2659279778393352, 0.267590027700831, 0.267590027700831, 0.267590027700831, 0.26814404432132966, 0.26869806094182824, 0.2692520775623269, 0.2698060941828255, 0.2698060941828255, 0.2703601108033241, 0.2709141274238227, 0.2714681440443213, 0.27313019390581716, 0.2736842105263158, 0.2736842105263158, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.27590027700831027, 0.27590027700831027, 0.27590027700831027, 0.27590027700831027, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.2770083102493075, 0.2775623268698061, 0.2775623268698061, 0.2775623268698061, 0.27811634349030473, 0.27811634349030473, 0.27811634349030473, 0.27922437673130196, 0.27922437673130196, 0.27922437673130196, 0.27922437673130196, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.2803324099722992, 0.28088642659279783, 0.28088642659279783, 0.2814404432132964, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28310249307479224, 0.28310249307479224, 0.28310249307479224, 0.2836565096952909, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28476454293628806, 0.28476454293628806, 0.28476454293628806, 0.28531855955678675, 0.28531855955678675, 0.28587257617728534, 0.28587257617728534, 0.28642659279778393, 0.28753462603878116, 0.289196675900277, 0.29030470914127426, 0.2914127423822715, 0.2925207756232687, 0.2936288088642659, 0.29418282548476454, 0.2952908587257618, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29750692520775623, 0.29861495844875346, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.2997229916897507, 0.30083102493074787, 0.30083102493074787, 0.3013850415512465, 0.30193905817174516, 0.30249307479224374, 0.3030470914127424, 0.303601108033241, 0.3041551246537396, 0.3047091412742382, 0.30526315789473685, 0.30526315789473685, 0.3058171745152355, 0.3063711911357341, 0.30692520775623267, 0.30858725761772854, 0.30969529085872577, 0.310803324099723, 0.3113573407202216, 0.31191135734072023, 0.31191135734072023, 0.31191135734072023, 0.3124653739612188, 0.31301939058171746, 0.31357340720221605, 0.31412742382271464, 0.31412742382271464, 0.31412742382271464, 0.31468144044321333, 0.31468144044321333, 0.31468144044321333, 0.3152354570637119, 0.3157894736842105, 0.3157894736842105, 0.31634349030470915, 0.3168975069252078, 0.3174515235457064, 0.318005540166205, 0.3185595567867036, 0.31911357340720226, 0.31966759002770084, 0.32022160664819943, 0.3207756232686981, 0.3213296398891967, 0.3213296398891967, 0.3213296398891967, 0.3218836565096953, 0.3229916897506925, 0.3240997229916897, 0.32520775623268694, 0.3263157894736842, 0.32742382271468146, 0.32742382271468146, 0.3279778393351801, 0.3285318559556787, 0.3285318559556787, 0.3290858725761773, 0.3290858725761773, 0.3296398891966759, 0.3301939058171745, 0.3318559556786703, 0.33240997229916897, 0.3329639889196676, 0.3346260387811634, 0.3346260387811634, 0.33518005540166207, 0.33573407202216066, 0.3362880886426593, 0.3362880886426593, 0.3362880886426593, 0.33684210526315794, 0.33739612188365653, 0.33850415512465376, 0.33905817174515235, 0.33905817174515235, 0.339612188365651, 0.34072022160664817, 0.34072022160664817, 0.34127423822714686, 0.3418282548476454, 0.3429362880886426, 0.3429362880886426, 0.3429362880886426, 0.34349030470914127, 0.3445983379501385, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34570637119113573, 0.34681440443213296, 0.34847645429362883, 0.3490304709141275, 0.3501385041551247, 0.3518005540166205, 0.35290858725761776, 0.35346260387811634, 0.3545706371191136, 0.3556786703601108, 0.35678670360110804, 0.3584487534626039, 0.3595567867036011, 0.36121883656509696, 0.3623268698060942, 0.3634349030470914, 0.3634349030470914, 0.36454293628808865, 0.3656509695290858, 0.3656509695290858, 0.36620498614958447, 0.3673130193905817, 0.36897506925207757, 0.3700831024930748, 0.3717451523545707, 0.3717451523545707, 0.37229916897506926, 0.3734072022160665, 0.37451523545706367, 0.37506925207756237, 0.3756232686980609, 0.37617728531855954, 0.37673130193905824, 0.37673130193905824, 0.37673130193905824, 0.37728531855955677, 0.378393351800554, 0.378393351800554, 0.37894736842105264, 0.37894736842105264, 0.3795013850415513, 0.38060941828254846, 0.3817174515235457, 0.38227146814404434, 0.3828254847645429, 0.38337950138504157, 0.38337950138504157, 0.38337950138504157, 0.3839335180055402, 0.3850415512465374, 0.385595567867036, 0.3861495844875346, 0.38670360110803326, 0.38670360110803326, 0.38725761772853184, 0.3878116343490305, 0.3878116343490305, 0.3883656509695291, 0.3883656509695291, 0.3894736842105263, 0.39058171745152354, 0.3911357340720222, 0.3922437673130194, 0.39279778393351805, 0.39279778393351805, 0.39279778393351805, 0.39279778393351805, 0.39335180055401664, 0.39445983379501387, 0.39501385041551246, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.39612188365650974, 0.397229916897507, 0.3977839335180055, 0.39833795013850415, 0.39889196675900274, 0.3994459833795014, 0.4, 0.4, 0.4, 0.4, 0.40055401662049855, 0.4011080332409972, 0.4016620498614959, 0.4022160664819944, 0.4022160664819944, 0.40277008310249307, 0.40332409972299166, 0.40332409972299166, 0.4044321329639889, 0.40498614958448753, 0.40498614958448753, 0.4055401662049861, 0.40609418282548476, 0.407202216066482, 0.40775623268698064, 0.40831024930747917, 0.40886426592797787, 0.40886426592797787, 0.4094182825484764, 0.40997229916897504, 0.40997229916897504, 0.41052631578947363, 0.41274238227146814, 0.4132963988919668, 0.414404432132964, 0.41551246537396125, 0.41606648199445984, 0.417174515235457, 0.417174515235457, 0.4177285318559557, 0.4177285318559557, 0.4177285318559557, 0.41828254847645424, 0.4188365650969529, 0.4199445983379501, 0.4204986149584487, 0.42105263157894735, 0.4221606648199446, 0.4221606648199446, 0.4227146814404432, 0.4232686980609418, 0.4254847645429363, 0.42603878116343485, 0.42659279778393344, 0.42659279778393344, 0.42659279778393344, 0.4271468144044322, 0.4277008310249307, 0.4282548476454293, 0.4282548476454293, 0.42880886426592796, 0.42880886426592796, 0.4293628808864266, 0.4299168975069252, 0.4299168975069252, 0.4310249307479224, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.4321329639889197, 0.4321329639889197, 0.4332409972299168, 0.4337950138504155, 0.43434903047091417, 0.4349030470914127, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.43711911357340716, 0.43711911357340716, 0.4376731301939058, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.43878116343490303, 0.4393351800554017, 0.4393351800554017, 0.4393351800554017, 0.43988919667590026, 0.4404432132963989, 0.4409972299168975, 0.44155124653739614, 0.44210526315789467, 0.44265927977839337, 0.44265927977839337, 0.44265927977839337, 0.443213296398892, 0.443213296398892, 0.443213296398892, 0.443213296398892, 0.44432132963988913, 0.4448753462603879, 0.445983379501385, 0.4470914127423823, 0.4476454293628809, 0.4476454293628809, 0.4481994459833795, 0.4487534626038781, 0.4487534626038781, 0.44986149584487534, 0.44986149584487534, 0.44986149584487534, 0.4509695290858726, 0.45207756232686985, 0.4526315789473684, 0.4537396121883656, 0.45429362880886426, 0.45429362880886426, 0.4554016620498615, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45706371191135736, 0.4581717451523545, 0.4581717451523545, 0.45872576177285324, 0.45872576177285324, 0.4592797783933518, 0.4592797783933518, 0.4592797783933518, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.46149584487534623, 0.4620498614958449, 0.46260387811634357, 0.46260387811634357, 0.4631578947368421, 0.46426592797783933, 0.46426592797783933, 0.464819944598338, 0.46537396121883656, 0.4659279778393352, 0.4664819944598338, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.467590027700831, 0.467590027700831, 0.46814404432132967, 0.4686980609418283, 0.4703601108033242, 0.4714681440443214, 0.47202216066481995, 0.47257617728531864, 0.47257617728531864, 0.47257617728531864, 0.47257617728531864, 0.4736842105263158, 0.47479224376731305, 0.4759002770083103, 0.47645429362880887, 0.4770083102493075, 0.4770083102493075, 0.4781163434903048, 0.4792243767313019, 0.4792243767313019, 0.4797783933518006, 0.4808864265927978, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.481994459833795, 0.4825484764542936, 0.4836565096952909, 0.4853185595567867, 0.4864265927977839, 0.4869806094182826, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.489196675900277, 0.489196675900277, 0.489196675900277, 0.489196675900277, 0.4903047091412742, 0.4914127423822715, 0.4919667590027701, 0.49252077562326874, 0.49252077562326874, 0.4930747922437673, 0.49418282548476455, 0.4947368421052632, 0.49529085872576173, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.49639889196675896, 0.4969529085872576, 0.4969529085872576, 0.4969529085872576, 0.4969529085872576, 0.4975069252077563, 0.4986149584487535, 0.4997229916897507, 0.5013850415512465, 0.5019390581717451, 0.5030470914127423, 0.5041551246537396, 0.5047091412742382, 0.5058171745152354, 0.5069252077562327, 0.5074792243767312, 0.50803324099723, 0.5091412742382271, 0.5096952908587257, 0.5102493074792245, 0.5102493074792245, 0.5102493074792245, 0.510803324099723, 0.510803324099723, 0.5119113573407201, 0.5119113573407201, 0.5124653739612188, 0.5141274238227147, 0.5152354570637119, 0.5157894736842106, 0.5174515235457064, 0.5191135734072022, 0.5207756232686981, 0.5207756232686981, 0.5218836565096953, 0.5235457063711911, 0.5246537396121884, 0.5257617728531856, 0.5268698060941829, 0.5274238227146815, 0.5279778393351802, 0.5285318559556786, 0.5290858725761773, 0.5301939058171745, 0.5313019390581717, 0.532409972299169, 0.5329639889196676, 0.5329639889196676, 0.5335180055401662, 0.5346260387811634, 0.5362880886426593, 0.5368421052631579, 0.5373961218836565, 0.5385041551246538, 0.5390581717451524, 0.539612188365651, 0.5407202216066482, 0.5429362880886427, 0.5445983379501385, 0.5457063711911357, 0.546814404432133, 0.5473684210526316, 0.5473684210526316, 0.5479224376731302, 0.5479224376731302, 0.5484764542936288, 0.5490304709141275, 0.5490304709141275, 0.5501385041551247, 0.5506925207756233, 0.5512465373961218, 0.5523545706371191, 0.5529085872576178, 0.554016620498615, 0.5551246537396122, 0.5556786703601108, 0.5562326869806093, 0.5573407202216066, 0.5578947368421052, 0.5584487534626038, 0.5595567867036011, 0.5595567867036011, 0.5601108033240998, 0.5606648199445983, 0.5606648199445983, 0.5617728531855957, 0.5623268698060941, 0.5628808864265927, 0.5628808864265927, 0.5628808864265927, 0.5634349030470913, 0.56398891966759, 0.5645429362880886, 0.5656509695290859, 0.5667590027700831, 0.5673130193905817, 0.5673130193905817, 0.5684210526315789, 0.5695290858725761, 0.571191135734072, 0.5722991689750693, 0.5728531855955679, 0.573961218836565, 0.5745152354570637, 0.575623268698061, 0.5767313019390581, 0.5772853185595569, 0.5778393351800555, 0.578393351800554, 0.5800554016620498, 0.5817174515235457, 0.5822714681440443, 0.5828254847645429, 0.5844875346260388, 0.585595567867036, 0.585595567867036, 0.585595567867036, 0.5861495844875346, 0.5872576177285318, 0.5872576177285318, 0.5878116343490305, 0.5889196675900277, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.590027700831025, 0.590027700831025, 0.5911357340720221, 0.5922437673130194, 0.592797783933518, 0.5939058171745153, 0.5939058171745153, 0.5939058171745153, 0.5950138504155125, 0.5950138504155125, 0.5950138504155125, 0.5950138504155125, 0.5955678670360111, 0.5961218836565096, 0.5966759002770083, 0.5972299168975069, 0.5983379501385042, 0.5988919667590029, 0.5994459833795014, 0.6, 0.6011080332409973, 0.6022160664819945, 0.6033240997229917, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989], 'name': 'baseline', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}, {'x': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0, 1000.0], 'y': [0.002770083102493075, 0.00554016620498615, 0.008310249307479225, 0.0110803324099723, 0.0110803324099723, 0.013850415512465374, 0.013850415512465374, 0.013850415512465374, 0.013850415512465374, 0.013850415512465374, 0.013850415512465374, 0.01662049861495845, 0.019390581717451522, 0.0221606648199446, 0.024930747922437674, 0.027700831024930747, 0.027700831024930747, 0.030470914127423823, 0.030470914127423823, 0.030470914127423823, 0.0332409972299169, 0.0332409972299169, 0.0332409972299169, 0.0332409972299169, 0.036011080332409975, 0.036011080332409975, 0.036011080332409975, 0.036011080332409975, 0.038781163434903045, 0.04155124653739612, 0.0443213296398892, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04986149584487535, 0.04986149584487535, 0.04986149584487535, 0.04986149584487535, 0.04986149584487535, 0.05263157894736842, 0.055401662049861494, 0.05817174515235457, 0.060941828254847646, 0.06371191135734072, 0.0664819944598338, 0.0664819944598338, 0.0664819944598338, 0.0664819944598338, 0.06925207756232687, 0.06925207756232687, 0.06925207756232687, 0.07202216066481995, 0.07202216066481995, 0.07479224376731301, 0.07756232686980609, 0.07756232686980609, 0.07756232686980609, 0.07756232686980609, 0.08033240997229917, 0.08310249307479224, 0.08587257617728532, 0.0886426592797784, 0.09141274238227147, 0.09418282548476455, 0.09695290858725762, 0.0997229916897507, 0.0997229916897507, 0.0997229916897507, 0.10249307479224377, 0.10526315789473684, 0.10803324099722991, 0.11080332409972299, 0.11357340720221606, 0.11357340720221606, 0.11357340720221606, 0.11634349030470914, 0.11634349030470914, 0.11634349030470914, 0.11911357340720222, 0.12188365650969529, 0.12465373961218837, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.12742382271468145, 0.13019390581717452, 0.1329639889196676, 0.13573407202216067, 0.13850415512465375, 0.14127423822714683, 0.1440443213296399, 0.14681440443213298, 0.14958448753462603, 0.1523545706371191, 0.15512465373961218, 0.15789473684210525, 0.16066481994459833, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.16620498614958448, 0.16897506925207756, 0.17174515235457063, 0.1745152354570637, 0.1772853185595568, 0.18005540166204986, 0.18282548476454294, 0.18559556786703602, 0.1883656509695291, 0.19113573407202217, 0.19390581717451524, 0.19667590027700832, 0.1994459833795014, 0.20221606648199447, 0.20498614958448755, 0.2077562326869806, 0.21052631578947367, 0.21329639889196675, 0.21329639889196675, 0.21329639889196675, 0.21329639889196675, 0.21329639889196675, 0.21329639889196675, 0.21329639889196675, 0.21606648199445982, 0.2188365650969529, 0.2188365650969529, 0.2188365650969529, 0.2188365650969529, 0.2188365650969529, 0.22160664819944598, 0.22160664819944598, 0.22160664819944598, 0.22160664819944598, 0.22160664819944598, 0.22437673130193905, 0.22437673130193905, 0.22437673130193905, 0.22437673130193905, 0.22437673130193905, 0.22437673130193905, 0.22714681440443213, 0.2299168975069252, 0.23268698060941828, 0.23545706371191136, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.23822714681440443, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.2409972299168975, 0.24376731301939059, 0.24376731301939059, 0.24653739612188366, 0.24930747922437674, 0.2520775623268698, 0.2548476454293629, 0.25761772853185594, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.2631578947368421, 0.2659279778393352, 0.26869806094182824, 0.27146814404432135, 0.2742382271468144, 0.2770083102493075, 0.27977839335180055, 0.28254847645429365, 0.2853185595567867, 0.2880886426592798, 0.2880886426592798, 0.2880886426592798, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29362880886426596, 0.29362880886426596, 0.29362880886426596, 0.29362880886426596, 0.296398891966759, 0.296398891966759, 0.296398891966759, 0.296398891966759, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.30193905817174516, 0.30193905817174516, 0.30193905817174516, 0.30193905817174516, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.31024930747922436, 0.31024930747922436, 0.31024930747922436, 0.31024930747922436, 0.31024930747922436, 0.31301939058171746, 0.31301939058171746, 0.31301939058171746, 0.31301939058171746, 0.31301939058171746, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3185595567867036, 0.3185595567867036, 0.3185595567867036, 0.32132963988919666, 0.32409972299168976, 0.3268698060941828, 0.3296398891966759, 0.3296398891966759, 0.33240997229916897, 0.33518005540166207, 0.3379501385041551, 0.3407202216066482, 0.34349030470914127, 0.3462603878116344, 0.3490304709141274, 0.3518005540166205, 0.3518005540166205, 0.3518005540166205, 0.3545706371191136, 0.3545706371191136, 0.3545706371191136, 0.3545706371191136, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3601108033240997, 0.3601108033240997, 0.3601108033240997, 0.3601108033240997, 0.3601108033240997, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3656509695290859, 0.3684210526315789, 0.3684210526315789, 0.3684210526315789, 0.37119113573407203, 0.3739612188365651, 0.3767313019390582, 0.37950138504155123, 0.38227146814404434, 0.3850415512465374, 0.3878116343490305, 0.3878116343490305, 0.39058171745152354, 0.39335180055401664, 0.3961218836565097, 0.3988919667590028, 0.40166204986149584, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.4099722991689751, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.4155124653739612, 0.4155124653739612, 0.4155124653739612, 0.4182825484764543, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4293628808864266, 0.4293628808864266, 0.4293628808864266, 0.4293628808864266, 0.4293628808864266, 0.4293628808864266, 0.43213296398891965, 0.43213296398891965, 0.43213296398891965, 0.43213296398891965, 0.43213296398891965, 0.43213296398891965, 0.43213296398891965, 0.43213296398891965, 0.43490304709141275, 0.43490304709141275, 0.43490304709141275, 0.43490304709141275, 0.43490304709141275, 0.4376731301939058, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989, 0.4404432132963989], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n",
      "Exploration Factor for ep 0 [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [1.0, 1.0, 0.8666666666666666, 0.9, 0.8400000000000001, 0.8, 0.7714285714285715, 0.7, 0.6222222222222222, 0.6199999999999999, 0.5818181818181818, 0.55, 0.5230769230769231, 0.5142857142857142, 0.5333333333333333, 0.525, 0.5176470588235295, 0.5, 0.5157894736842106, 0.5200000000000001, 0.5238095238095238, 0.5272727272727272, 0.5217391304347825, 0.5166666666666667, 0.52, 0.523076923076923, 0.5259259259259259, 0.5285714285714286, 0.5172413793103449, 0.5133333333333333, 0.5096774193548387, 0.5, 0.49696969696969695, 0.5, 0.5085714285714286, 0.5055555555555555, 0.5081081081081081, 0.5105263157894736, 0.517948717948718, 0.515, 0.5121951219512195, 0.5142857142857143, 0.5116279069767442, 0.5045454545454546, 0.5111111111111112, 0.517391304347826, 0.5148936170212767, 0.5041666666666667, 0.5061224489795919, 0.508, 0.5137254901960785, 0.5115384615384615, 0.5132075471698113, 0.5148148148148148, 0.5163636363636364, 0.5178571428571429, 0.5192982456140351, 0.5172413793103449, 0.5186440677966101, 0.52, 0.5114754098360655, 0.5032258064516129, 0.49523809523809526, 0.490625, 0.49230769230769234, 0.49696969696969695, 0.49253731343283585, 0.4911764705882353, 0.48405797101449277, 0.47714285714285715, 0.476056338028169, 0.4805555555555555, 0.48219178082191777, 0.4891891891891892, 0.49066666666666664, 0.48684210526315785, 0.48311688311688317, 0.48461538461538456, 0.48607594936708864, 0.485, 0.4814814814814815, 0.48048780487804876, 0.48192771084337344, 0.48571428571428577, 0.48235294117647065, 0.48139534883720925, 0.4850574712643678, 0.48409090909090907, 0.48764044943820223, 0.48888888888888893, 0.49010989010989015, 0.48695652173913045, 0.4860215053763441, 0.4851063829787233, 0.4821052631578947, 0.4833333333333333, 0.4783505154639175, 0.47551020408163264, 0.47474747474747475, 0.476, 0.47326732673267324, 0.47058823529411764, 0.4660194174757281, 0.4634615384615385, 0.4647619047619048, 0.4641509433962264, 0.4654205607476635, 0.46296296296296297, 0.46605504587155966, 0.46545454545454545, 0.46486486486486484, 0.4642857142857143, 0.46371681415929206, 0.4649122807017544, 0.4608695652173913, 0.45862068965517244, 0.45811965811965816, 0.4576271186440678, 0.45546218487394957, 0.45499999999999996, 0.4561983471074381, 0.4557377049180328, 0.45528455284552843, 0.4532258064516129, 0.44959999999999994, 0.4492063492063492, 0.44724409448818897, 0.4453125, 0.44341085271317826, 0.44153846153846155, 0.43969465648854966, 0.43787878787878787, 0.437593984962406, 0.43731343283582086, 0.43555555555555553, 0.43382352941176466, 0.435036496350365, 0.43478260869565216, 0.4316546762589928, 0.42857142857142855, 0.4297872340425532, 0.4295774647887324, 0.4293706293706293, 0.4305555555555555, 0.4289655172413793, 0.4273972602739726, 0.4258503401360544, 0.4256756756756757, 0.42684563758389266, 0.4253333333333334, 0.42251655629139073, 0.4223684210526315, 0.4235294117647059, 0.4220779220779221, 0.4193548387096774, 0.41923076923076924, 0.4165605095541401, 0.41645569620253164, 0.41509433962264153, 0.4125, 0.4111801242236025, 0.40864197530864194, 0.40613496932515336, 0.4036585365853658, 0.4012121212121212, 0.3987951807228916, 0.39640718562874255, 0.3952380952380953, 0.3940828402366864, 0.3952941176470589, 0.39649122807017545, 0.39767441860465114, 0.3953757225433526, 0.39425287356321836, 0.3942857142857143, 0.3931818181818182, 0.39322033898305087, 0.3921348314606742, 0.3910614525139665, 0.3911111111111111, 0.39226519337016574, 0.39230769230769236, 0.3901639344262295, 0.38913043478260867, 0.3902702702702703, 0.3903225806451613, 0.3893048128342246, 0.38723404255319144, 0.38518518518518513, 0.38421052631578945, 0.38219895287958117, 0.38020833333333337, 0.37823834196891193, 0.37835051546391746, 0.3784615384615384, 0.376530612244898, 0.3756345177664975, 0.3757575757575758, 0.3748743718592965, 0.37300000000000005, 0.3711442786069652, 0.37128712871287134, 0.3694581280788177, 0.3686274509803921, 0.3687804878048781, 0.36893203883495146, 0.3690821256038648, 0.36826923076923074, 0.36746411483253594, 0.3666666666666667, 0.36587677725118484, 0.3650943396226415, 0.3643192488262911, 0.3635514018691589, 0.36372093023255814, 0.36203703703703705, 0.36036866359447, 0.35963302752293586, 0.35799086757990867, 0.3572727272727273, 0.35656108597285063, 0.35765765765765767, 0.3569506726457399, 0.35535714285714287, 0.3546666666666667, 0.35398230088495575, 0.3541850220264317, 0.3543859649122807, 0.3537117903930131, 0.35304347826086957, 0.35151515151515145, 0.35086206896551725, 0.3502145922746781, 0.34957264957264955, 0.3497872340425532, 0.3483050847457627, 0.34683544303797464, 0.346218487394958, 0.3456066945606694, 0.34500000000000003, 0.34439834024896265, 0.34628099173553717, 0.3465020576131687, 0.34754098360655733, 0.3477551020408163, 0.34796747967479674, 0.34655870445344134, 0.34596774193548385, 0.3461847389558233, 0.3456, 0.3450199203187251, 0.34365079365079365, 0.3422924901185771, 0.34173228346456697, 0.3411764705882353, 0.33984375, 0.33852140077821014, 0.3372093023255814, 0.33590733590733596, 0.33461538461538465, 0.33333333333333337, 0.33282442748091606, 0.33307984790874523, 0.3325757575757576, 0.3313207547169811, 0.331578947368421, 0.33108614232209743, 0.32985074626865674, 0.32936802973977697, 0.3296296296296296, 0.32988929889298896, 0.32941176470588235, 0.3304029304029304, 0.32992700729927005, 0.3287272727272727, 0.327536231884058, 0.32707581227436827, 0.3273381294964029, 0.3268817204301075, 0.3264285714285714, 0.3274021352313167, 0.3269503546099291, 0.32720848056537105, 0.328169014084507, 0.3270175438596491, 0.3265734265734266, 0.3261324041811847, 0.325, 0.3245674740484429, 0.32413793103448274, 0.3237113402061856, 0.323972602739726, 0.32354948805460754, 0.32448979591836735, 0.32406779661016943, 0.3243243243243243, 0.32525252525252524, 0.32416107382550335, 0.3230769230769231, 0.32266666666666666, 0.3222591362126246, 0.3218543046357616, 0.3214521452145215, 0.3203947368421053, 0.32, 0.3196078431372549, 0.3192182410423453, 0.32012987012987015, 0.31974110032362457, 0.31870967741935485, 0.3189710610932476, 0.317948717948718, 0.3169329073482428, 0.3159235668789809, 0.31492063492063493, 0.3139240506329114, 0.31293375394321765, 0.31257861635220124, 0.3115987460815047, 0.31062500000000004, 0.3096573208722741, 0.3086956521739131, 0.3077399380804954, 0.30740740740740746, 0.30646153846153845, 0.305521472392638, 0.3045871559633027, 0.3042682926829268, 0.30334346504559273, 0.30242424242424243, 0.30151057401812686, 0.3006024096385542, 0.3003003003003003, 0.30000000000000004, 0.2991044776119403, 0.29821428571428565, 0.29792284866468843, 0.29704142011834317, 0.296165191740413, 0.29647058823529415, 0.29560117302052785, 0.2947368421052632, 0.29387755102040813, 0.29360465116279066, 0.2927536231884058, 0.29190751445086704, 0.29106628242074933, 0.29022988505747127, 0.28997134670487107, 0.2897142857142857, 0.28888888888888886, 0.2886363636363637, 0.28838526912181306, 0.28757062146892653, 0.2867605633802818, 0.2859550561797753, 0.28515406162464985, 0.2843575418994414, 0.2835654596100279, 0.28277777777777774, 0.28254847645429365, 0.281767955801105, 0.28099173553719003, 0.2802197802197802, 0.27945205479452057, 0.2786885245901639, 0.2779291553133515, 0.27771739130434786, 0.2769647696476965, 0.2762162162162162, 0.27601078167115906, 0.2758064516129032, 0.2750670241286863, 0.27433155080213906, 0.27359999999999995, 0.2728723404255319, 0.2726790450928382, 0.2719576719576719, 0.2712401055408971, 0.2710526315789474, 0.27034120734908135, 0.27015706806282724, 0.2694516971279374, 0.26927083333333335, 0.26961038961038963, 0.2704663212435233, 0.2708010335917313, 0.27113402061855674, 0.2714652956298201, 0.27179487179487183, 0.2716112531969309, 0.27193877551020407, 0.2727735368956743, 0.2720812182741117, 0.2713924050632911, 0.27070707070707073, 0.2700251889168766, 0.2698492462311558, 0.2701754385964912, 0.27, 0.26932668329177056, 0.26865671641791045, 0.2679900744416873, 0.26782178217821784, 0.26814814814814814, 0.26748768472906403, 0.2673218673218673, 0.2671568627450981, 0.26699266503667485, 0.26682926829268294, 0.26666666666666666, 0.26650485436893206, 0.26634382566585957, 0.26618357487922706, 0.26554216867469876, 0.26538461538461544, 0.26522781774580334, 0.26507177033492824, 0.26587112171837707, 0.2661904761904762, 0.2665083135391924, 0.2663507109004739, 0.266193853427896, 0.265566037735849, 0.26494117647058824, 0.26478873239436623, 0.26463700234192034, 0.26448598130841117, 0.2643356643356643, 0.26372093023255816, 0.26310904872389795, 0.26296296296296295, 0.26235565819861434, 0.26175115207373273, 0.2616091954022989, 0.26146788990825687, 0.2608695652173913, 0.26073059360730594, 0.2605922551252847, 0.26045454545454544, 0.2603174603174603, 0.26018099547511314, 0.2600451467268623, 0.25990990990990986, 0.2597752808988764, 0.2596412556053812, 0.2595078299776286, 0.2589285714285714, 0.25835189309576834, 0.25822222222222224, 0.25853658536585367, 0.2588495575221239, 0.25916114790286976, 0.2594713656387665, 0.25978021978021976, 0.25921052631578945, 0.2590809628008753, 0.25895196506550217, 0.25838779956427016, 0.25826086956521743, 0.2577006507592191, 0.25757575757575757, 0.257451403887689, 0.25818965517241377, 0.25806451612903225, 0.25793991416309014, 0.25867237687366174, 0.2581196581196581, 0.2579957356076759, 0.257872340425532, 0.25774946921443737, 0.2572033898305085, 0.2566596194503171, 0.25654008438818565, 0.256421052631579, 0.25672268907563023, 0.2566037735849057, 0.2560669456066945, 0.2559498956158664, 0.25625, 0.2557172557172557, 0.2556016597510373, 0.25548654244306424, 0.25578512396694214, 0.2552577319587629, 0.25473251028806587, 0.2546201232032854, 0.2549180327868853, 0.25480572597137013, 0.25428571428571434, 0.2537678207739308, 0.2532520325203252, 0.2527383367139959, 0.25263157894736843, 0.2529292929292929, 0.2536290322580645, 0.2535211267605634, 0.25381526104417673, 0.2545090180360721, 0.2548, 0.254690618762475, 0.2549800796812749, 0.25526838966202786, 0.25555555555555554, 0.25623762376237624, 0.25652173913043474, 0.25719921104536486, 0.2574803149606299, 0.2577603143418467, 0.2572549019607843, 0.25753424657534246, 0.2578125, 0.2573099415204678, 0.25719844357976657, 0.2574757281553398, 0.25813953488372093, 0.2584139264990329, 0.25907335907335904, 0.2585741811175338, 0.25846153846153846, 0.25873320537428024, 0.2590038314176245, 0.25889101338432124, 0.2587786259541985, 0.25866666666666666, 0.2585551330798479, 0.25806451612903225, 0.2575757575757576, 0.2574669187145558, 0.2577358490566038, 0.25725047080979285, 0.2571428571428572, 0.25666041275797374, 0.2565543071161049, 0.256822429906542, 0.257089552238806, 0.25698324022346364, 0.25687732342007435, 0.25677179962894253, 0.25629629629629636, 0.25582255083179295, 0.255719557195572, 0.2559852670349908, 0.25588235294117645, 0.25577981651376147, 0.2556776556776557, 0.2552102376599635, 0.2551094890510949, 0.25500910746812383, 0.2545454545454545, 0.25444646098003626, 0.2539855072463768, 0.25424954792043397, 0.2545126353790614, 0.2544144144144144, 0.2546762589928057, 0.2545780969479354, 0.25412186379928314, 0.2536672629695885, 0.2532142857142857, 0.2531194295900178, 0.25338078291814947, 0.25328596802841924, 0.2531914893617021, 0.2527433628318584, 0.25229681978798585, 0.2518518518518518, 0.25140845070422535, 0.25131810193321613, 0.251578947368421, 0.25148861646234677, 0.25139860139860143, 0.2513089005235602, 0.25121951219512195, 0.25113043478260866, 0.25069444444444444, 0.25025996533795497, 0.24982698961937713, 0.24974093264248703, 0.24965517241379312, 0.24956970740103274, 0.24948453608247423, 0.2490566037735849, 0.24897260273972602, 0.24888888888888888, 0.24846416382252562, 0.24872231686541735, 0.24863945578231297, 0.24821731748726653, 0.248135593220339, 0.24805414551607444, 0.2483108108108108, 0.24822934232715008, 0.24814814814814815, 0.2480672268907563, 0.2476510067114094, 0.247571189279732, 0.24749163879598662, 0.24707846410684473, 0.24700000000000003, 0.24792013311148087, 0.2478405315614618, 0.24809286898839136, 0.24834437086092714, 0.24826446280991732, 0.2485148514851485, 0.24810543657331138, 0.2480263157894737, 0.24761904761904763, 0.24721311475409835, 0.2471358428805237, 0.24705882352941183, 0.24730831973898862, 0.24723127035830622, 0.24715447154471543, 0.24740259740259743, 0.24700162074554294, 0.24692556634304202, 0.24684975767366718, 0.24774193548387097, 0.24766505636070849, 0.247588424437299, 0.24719101123595505, 0.2467948717948718, 0.24672, 0.24664536741214058, 0.24657097288676236, 0.2461783439490446, 0.24610492845786966, 0.24571428571428572, 0.2456418383518225, 0.2455696202531646, 0.24518167456556084, 0.24542586750788647, 0.24535433070866142, 0.2449685534591195, 0.24458398744113025, 0.24420062695924766, 0.24381846635367763, 0.24343750000000003, 0.24305772230889233, 0.2426791277258567, 0.24230171073094864, 0.24192546583850927, 0.24155038759689923, 0.2414860681114551, 0.241112828438949, 0.24135802469135798, 0.24129429892141757, 0.24123076923076922, 0.24116743471582183, 0.2411042944785276, 0.24073506891271051, 0.24036697247706423, 0.24000000000000005, 0.23963414634146343, 0.23926940639269406, 0.23890577507598784, 0.2385432473444613, 0.2381818181818182, 0.23812405446293497, 0.2377643504531722, 0.23740573152337857, 0.23704819277108435, 0.23669172932330826, 0.2363363363363363, 0.2359820089955023, 0.2359281437125748, 0.23557548579970106, 0.23522388059701496, 0.23487332339791353, 0.23452380952380952, 0.23417533432392273, 0.2338278931750742, 0.23348148148148148, 0.2331360946745562, 0.23279172821270314, 0.23244837758112094, 0.2321060382916053, 0.23176470588235296, 0.23142437591776796, 0.23108504398826982, 0.23103953147877015, 0.2307017543859649, 0.23065693430656933, 0.2306122448979592, 0.2302765647743814, 0.22994186046511628, 0.22960812772133526, 0.22927536231884055, 0.229232995658466, 0.22919075144508674, 0.22886002886002882, 0.22853025936599422, 0.22848920863309355, 0.22844827586206895, 0.2284074605451937, 0.22836676217765045, 0.22832618025751072, 0.2282857142857143, 0.22796005706134093, 0.22763532763532765, 0.22759601706970128, 0.2272727272727273, 0.22695035460992904, 0.22662889518413598, 0.2268741159830269, 0.2268361581920904, 0.22708039492242596, 0.22732394366197184, 0.22728551336146272, 0.22696629213483144, 0.22692847124824683, 0.226890756302521, 0.22657342657342658, 0.22681564245810057, 0.2264993026499303, 0.22618384401114205, 0.22642559109874827, 0.22666666666666666, 0.22662968099861303, 0.2268698060941828, 0.2268326417704011, 0.22651933701657462, 0.2267586206896552, 0.22672176308539943, 0.2264099037138927, 0.2260989010989011, 0.22578875171467763, 0.22547945205479453, 0.22544459644322848, 0.22513661202185792, 0.22482946793997272, 0.22452316076294282, 0.22421768707482995, 0.22391304347826088, 0.2238805970149254, 0.22411924119241192, 0.22381596752368066, 0.22378378378378377, 0.2234817813765182, 0.22345013477088949, 0.2231493943472409, 0.2228494623655914, 0.22281879194630871, 0.2225201072386059, 0.22222222222222224, 0.22192513368983957, 0.2216288384512684, 0.22160000000000002, 0.2213049267643142, 0.22101063829787235, 0.2207171314741036, 0.2204244031830239, 0.2201324503311258, 0.21984126984126987, 0.21981505944517835, 0.21952506596306068, 0.21923583662714097, 0.2189473684210526, 0.21865965834428383, 0.2183727034120735, 0.218348623853211, 0.2183246073298429, 0.21830065359477122, 0.21801566579634465, 0.21799217731421122, 0.21822916666666664, 0.2179453836150845, 0.21792207792207793, 0.21789883268482488, 0.2178756476683938, 0.21785252263906857, 0.21782945736434106, 0.21754838709677418, 0.21726804123711338, 0.216988416988417, 0.21670951156812338, 0.21643132220795894, 0.21615384615384614, 0.21587708066581307, 0.21585677749360613, 0.215581098339719, 0.21556122448979592, 0.21554140127388535, 0.21603053435114505, 0.2162642947903431, 0.21624365482233499, 0.2162230671736375, 0.2159493670886076, 0.21567635903919088, 0.2154040404040404, 0.21563682219419925, 0.2158690176322418, 0.2161006289308176, 0.21608040201005027, 0.21606022584692597, 0.21578947368421053, 0.2160200250312891, 0.21625, 0.21598002496878901, 0.21596009975062347, 0.21618929016189292, 0.21616915422885574, 0.21590062111801242, 0.21563275434243176, 0.215365551425031, 0.2150990099009901, 0.21508034610630405, 0.2150617283950617, 0.21528976572133168, 0.21576354679802953, 0.21599015990159903, 0.21597051597051595, 0.21644171779141103, 0.21617647058823533, 0.21591187270501835, 0.2156479217603912, 0.21538461538461542, 0.21512195121951216, 0.21485992691839217, 0.21484184914841847, 0.21458080194410695, 0.21432038834951456, 0.21406060606060606, 0.21428571428571427, 0.2145102781136638, 0.21449275362318837, 0.21447527141133893, 0.21421686746987953, 0.21419975932611313, 0.2144230769230769, 0.21440576230492198, 0.2143884892086331, 0.21437125748502991, 0.21411483253588517, 0.2138590203106332, 0.21360381861575178, 0.21334922526817643, 0.2130952380952381, 0.21284185493460167, 0.2125890736342043, 0.21233689205219455, 0.21232227488151656, 0.2123076923076923, 0.21205673758865248, 0.21180637544273911, 0.21155660377358493, 0.21154299175500593, 0.2117647058823529, 0.21198589894242068, 0.2124413145539906, 0.21242672919109026, 0.2126463700234192, 0.2128654970760234, 0.21285046728971962, 0.21306884480746793, 0.21328671328671328, 0.21327124563445868, 0.21325581395348836, 0.21347270615563296, 0.21345707656612528, 0.21344148319814601, 0.2131944444444444, 0.21294797687861272, 0.21293302540415704, 0.2126874279123414, 0.21290322580645166, 0.2126582278481013, 0.2126436781609195, 0.21308840413318025, 0.21330275229357798, 0.21328751431844215, 0.2137299771167048, 0.21417142857142854, 0.2146118721461187, 0.2143671607753706, 0.21457858769931662, 0.2150170648464164, 0.21522727272727274, 0.21543700340522137, 0.21564625850340136, 0.21562853907134766, 0.21561085972850677, 0.21559322033898304, 0.2155756207674943, 0.21578354002254793, 0.215990990990991, 0.21619797525309337, 0.21617977528089888, 0.2159371492704826, 0.21591928251121076, 0.21612541993281073, 0.21655480984340042, 0.21653631284916203, 0.21651785714285715, 0.21672240802675585, 0.21670378619153677, 0.21668520578420467, 0.21688888888888885, 0.21753607103218647, 0.217960088691796, 0.21816168327796234, 0.21836283185840707, 0.2183425414364641, 0.21810154525386313, 0.21808158765159869, 0.21784140969162996, 0.2178217821782178, 0.21780219780219778, 0.21756311745334794, 0.21776315789473685, 0.21774370208105145, 0.21772428884026257, 0.21792349726775956, 0.21790393013100434, 0.21810250817884405, 0.21830065359477127, 0.21828073993471162, 0.21826086956521742, 0.2184581976112921, 0.21843817787418654, 0.2184182015167931, 0.21861471861471862, 0.21837837837837842, 0.21835853131749458, 0.21833872707659116, 0.21810344827586206, 0.2182992465016146, 0.2182795698924731, 0.2182599355531686, 0.21802575107296135, 0.2177920685959271, 0.21777301927194861, 0.21775401069518718, 0.2177350427350427, 0.2179295624332978, 0.21812366737739874, 0.21810436634717786, 0.2178723404255319, 0.21806588735387886, 0.21825902335456476, 0.21866383881230117, 0.21885593220338984, 0.21883597883597883, 0.21902748414376322, 0.21900739176346357, 0.2191983122362869, 0.21938883034773443, 0.21936842105263157, 0.21934805467928492, 0.219327731092437, 0.2197271773347324, 0.22012578616352202, 0.2201047120418848, 0.2200836820083682, 0.22048066875653083, 0.22066805845511483, 0.2204379562043796, 0.22020833333333328, 0.22018730489073884, 0.22037422037422041, 0.2201453790238837, 0.22012448132780085, 0.22031088082901557, 0.22028985507246376, 0.22006204756980355, 0.21983471074380168, 0.21960784313725493, 0.21938144329896908, 0.2191555097837281, 0.2191358024691358, 0.21891058581706066, 0.21909650924024637, 0.2192820512820513, 0.2192622950819672, 0.2194472876151484, 0.21922290388548057, 0.21899897854954037, 0.21918367346938777, 0.21896024464831804, 0.21873727087576372, 0.2185147507629705, 0.2184959349593496, 0.21847715736040607, 0.21845841784989856, 0.21843971631205675, 0.2186234817813765, 0.21860465116279068, 0.21858585858585858, 0.21856710393541876, 0.21875, 0.21893252769385702, 0.21911468812877266, 0.2192964824120603, 0.2190763052208835, 0.21885656970912737, 0.21863727454909823, 0.21841841841841844, 0.2182], 'name': 'baseline', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}, {'x': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0, 1000.0], 'y': [1.0, 1.0, 0.6666666666666666, 0.75, 0.6, 0.5, 0.42857142857142855, 0.5, 0.5555555555555556, 0.6, 0.6363636363636364, 0.5833333333333334, 0.6153846153846154, 0.5714285714285714, 0.6, 0.625, 0.5882352941176471, 0.5555555555555556, 0.5263157894736842, 0.5, 0.47619047619047616, 0.45454545454545453, 0.43478260869565216, 0.4166666666666667, 0.4, 0.38461538461538464, 0.37037037037037035, 0.39285714285714285, 0.41379310344827586, 0.43333333333333335, 0.45161290322580644, 0.4375, 0.42424242424242425, 0.4411764705882353, 0.45714285714285713, 0.4444444444444444, 0.43243243243243246, 0.42105263157894735, 0.41025641025641024, 0.4, 0.3902439024390244, 0.40476190476190477, 0.3953488372093023, 0.38636363636363635, 0.37777777777777777, 0.3695652173913043, 0.3617021276595745, 0.3541666666666667, 0.3469387755102041, 0.34, 0.3333333333333333, 0.3269230769230769, 0.32075471698113206, 0.3333333333333333, 0.34545454545454546, 0.35714285714285715, 0.3684210526315789, 0.3793103448275862, 0.3728813559322034, 0.36666666666666664, 0.36065573770491804, 0.3548387096774194, 0.3492063492063492, 0.34375, 0.3384615384615385, 0.3333333333333333, 0.3283582089552239, 0.3235294117647059, 0.3333333333333333, 0.34285714285714286, 0.352112676056338, 0.3611111111111111, 0.3698630136986301, 0.36486486486486486, 0.36, 0.35526315789473684, 0.36363636363636365, 0.358974358974359, 0.35443037974683544, 0.35, 0.345679012345679, 0.35365853658536583, 0.3493975903614458, 0.34523809523809523, 0.3411764705882353, 0.3488372093023256, 0.3448275862068966, 0.3409090909090909, 0.33707865168539325, 0.3333333333333333, 0.34065934065934067, 0.33695652173913043, 0.3333333333333333, 0.32978723404255317, 0.3263157894736842, 0.3333333333333333, 0.32989690721649484, 0.32653061224489793, 0.32323232323232326, 0.32, 0.32673267326732675, 0.3333333333333333, 0.3300970873786408, 0.3269230769230769, 0.3238095238095238, 0.32075471698113206, 0.3177570093457944, 0.3148148148148148, 0.3119266055045872, 0.3090909090909091, 0.3063063063063063, 0.30357142857142855, 0.3008849557522124, 0.2982456140350877, 0.2956521739130435, 0.29310344827586204, 0.29914529914529914, 0.3050847457627119, 0.31092436974789917, 0.31666666666666665, 0.32231404958677684, 0.319672131147541, 0.3252032520325203, 0.3225806451612903, 0.32, 0.31746031746031744, 0.31496062992125984, 0.3125, 0.3178294573643411, 0.3230769230769231, 0.3282442748091603, 0.3333333333333333, 0.3383458646616541, 0.34328358208955223, 0.34814814814814815, 0.35294117647058826, 0.35766423357664234, 0.36231884057971014, 0.3597122302158273, 0.35714285714285715, 0.3617021276595745, 0.3591549295774648, 0.35664335664335667, 0.3541666666666667, 0.35172413793103446, 0.3561643835616438, 0.35374149659863946, 0.35135135135135137, 0.348993288590604, 0.35333333333333333, 0.3509933774834437, 0.34868421052631576, 0.3464052287581699, 0.34415584415584416, 0.3419354838709677, 0.34615384615384615, 0.34394904458598724, 0.34177215189873417, 0.33962264150943394, 0.34375, 0.3416149068322981, 0.3395061728395062, 0.3374233128834356, 0.3353658536585366, 0.3393939393939394, 0.3433734939759036, 0.3413173652694611, 0.3392857142857143, 0.33727810650887574, 0.3411764705882353, 0.3391812865497076, 0.3372093023255814, 0.3352601156069364, 0.3390804597701149, 0.33714285714285713, 0.3352272727272727, 0.3333333333333333, 0.33146067415730335, 0.329608938547486, 0.3277777777777778, 0.3259668508287293, 0.3241758241758242, 0.32786885245901637, 0.33152173913043476, 0.32972972972972975, 0.3333333333333333, 0.33689839572192515, 0.3404255319148936, 0.3439153439153439, 0.3473684210526316, 0.34554973821989526, 0.34375, 0.34196891191709844, 0.3402061855670103, 0.3435897435897436, 0.34183673469387754, 0.3401015228426396, 0.3383838383838384, 0.3417085427135678, 0.34, 0.3383084577114428, 0.33663366336633666, 0.3399014778325123, 0.3382352941176471, 0.33658536585365856, 0.33495145631067963, 0.3333333333333333, 0.33653846153846156, 0.3349282296650718, 0.3333333333333333, 0.33175355450236965, 0.330188679245283, 0.3333333333333333, 0.3317757009345794, 0.3302325581395349, 0.3287037037037037, 0.3271889400921659, 0.3302752293577982, 0.3333333333333333, 0.33181818181818185, 0.33031674208144796, 0.32882882882882886, 0.3273542600896861, 0.32589285714285715, 0.3244444444444444, 0.3230088495575221, 0.32158590308370044, 0.32456140350877194, 0.32751091703056767, 0.33043478260869563, 0.3333333333333333, 0.33620689655172414, 0.33905579399141633, 0.3418803418803419, 0.3446808510638298, 0.3474576271186441, 0.350210970464135, 0.35294117647058826, 0.35564853556485354, 0.35833333333333334, 0.36099585062240663, 0.36363636363636365, 0.3662551440329218, 0.36475409836065575, 0.363265306122449, 0.3617886178861789, 0.3643724696356275, 0.3629032258064516, 0.3614457831325301, 0.36, 0.35856573705179284, 0.3611111111111111, 0.35968379446640314, 0.35826771653543305, 0.3568627450980392, 0.35546875, 0.3540856031128405, 0.35658914728682173, 0.3552123552123552, 0.35384615384615387, 0.3524904214559387, 0.3511450381679389, 0.34980988593155893, 0.3484848484848485, 0.35094339622641507, 0.34962406015037595, 0.34831460674157305, 0.34701492537313433, 0.34572490706319703, 0.34444444444444444, 0.34317343173431736, 0.34191176470588236, 0.34065934065934067, 0.34306569343065696, 0.3418181818181818, 0.34057971014492755, 0.33935018050541516, 0.3381294964028777, 0.33691756272401435, 0.3357142857142857, 0.33451957295373663, 0.3333333333333333, 0.3321554770318021, 0.33098591549295775, 0.3298245614035088, 0.32867132867132864, 0.32752613240418116, 0.3298611111111111, 0.33217993079584773, 0.33448275862068966, 0.33676975945017185, 0.339041095890411, 0.3378839590443686, 0.336734693877551, 0.33559322033898303, 0.3344594594594595, 0.3333333333333333, 0.33221476510067116, 0.3311036789297659, 0.33, 0.3289036544850498, 0.32781456953642385, 0.32673267326732675, 0.3256578947368421, 0.32459016393442625, 0.3235294117647059, 0.32247557003257327, 0.32142857142857145, 0.32038834951456313, 0.3193548387096774, 0.3183279742765273, 0.3173076923076923, 0.31629392971246006, 0.31528662420382164, 0.3142857142857143, 0.31329113924050633, 0.31230283911671924, 0.3113207547169811, 0.3103448275862069, 0.3125, 0.3146417445482866, 0.3167701863354037, 0.3188854489164087, 0.32098765432098764, 0.3230769230769231, 0.3220858895705521, 0.3211009174311927, 0.3201219512195122, 0.3191489361702128, 0.3181818181818182, 0.31722054380664655, 0.31626506024096385, 0.3153153153153153, 0.3143712574850299, 0.31343283582089554, 0.3125, 0.3115727002967359, 0.3106508875739645, 0.30973451327433627, 0.3088235294117647, 0.30791788856304986, 0.30701754385964913, 0.30612244897959184, 0.30523255813953487, 0.30434782608695654, 0.30346820809248554, 0.30547550432276654, 0.3074712643678161, 0.30945558739255014, 0.31142857142857144, 0.31339031339031337, 0.3153409090909091, 0.31728045325779036, 0.3192090395480226, 0.3211267605633803, 0.3202247191011236, 0.31932773109243695, 0.31843575418994413, 0.31754874651810583, 0.31666666666666665, 0.3157894736842105, 0.3149171270718232, 0.3140495867768595, 0.3131868131868132, 0.31232876712328766, 0.3114754098360656, 0.3106267029972752, 0.30978260869565216, 0.3089430894308943, 0.3081081081081081, 0.30727762803234504, 0.3064516129032258, 0.30563002680965146, 0.3048128342245989, 0.304, 0.30319148936170215, 0.30238726790450926, 0.30158730158730157, 0.3007915567282322, 0.3, 0.2992125984251969, 0.29842931937172773, 0.29765013054830286, 0.296875, 0.2961038961038961, 0.29533678756476683, 0.29457364341085274, 0.29381443298969073, 0.2930591259640103, 0.2923076923076923, 0.2915601023017903, 0.29081632653061223, 0.2900763358778626, 0.2893401015228426, 0.28860759493670884, 0.2878787878787879, 0.2871536523929471, 0.2864321608040201, 0.2882205513784461, 0.29, 0.2892768079800499, 0.2885572139303483, 0.2878411910669975, 0.2871287128712871, 0.28641975308641976, 0.2881773399014778, 0.28746928746928746, 0.2867647058823529, 0.2860635696821516, 0.28536585365853656, 0.2846715328467153, 0.28398058252427183, 0.28329297820823246, 0.2826086956521739, 0.2819277108433735, 0.28125, 0.2805755395683453, 0.2799043062200957, 0.27923627684964203, 0.2785714285714286, 0.27790973871733965, 0.2772511848341232, 0.2789598108747045, 0.2806603773584906, 0.2823529411764706, 0.284037558685446, 0.2857142857142857, 0.28738317757009346, 0.289044289044289, 0.29069767441860467, 0.2923433874709977, 0.29398148148148145, 0.2956120092378753, 0.29723502304147464, 0.2988505747126437, 0.30045871559633025, 0.30205949656750575, 0.3013698630136986, 0.30068337129840544, 0.3, 0.29931972789115646, 0.2986425339366516, 0.2979683972911964, 0.2972972972972973, 0.2966292134831461, 0.29596412556053814, 0.2953020134228188, 0.29464285714285715, 0.29398663697104677, 0.29333333333333333, 0.2926829268292683, 0.2920353982300885, 0.2913907284768212, 0.2907488986784141, 0.29010989010989013, 0.2894736842105263, 0.2888402625820569, 0.28820960698689957, 0.2875816993464052, 0.28695652173913044, 0.28633405639913234, 0.2857142857142857, 0.28509719222462204, 0.28448275862068967, 0.2838709677419355, 0.2832618025751073, 0.2826552462526767, 0.2841880341880342, 0.2857142857142857, 0.2872340425531915, 0.28874734607218683, 0.2902542372881356, 0.28964059196617337, 0.2890295358649789, 0.28842105263157897, 0.28781512605042014, 0.28721174004192873, 0.28661087866108786, 0.2860125260960334, 0.28541666666666665, 0.28482328482328484, 0.2842323651452282, 0.2836438923395445, 0.28512396694214875, 0.2865979381443299, 0.2880658436213992, 0.28952772073921973, 0.29098360655737704, 0.2903885480572597, 0.2897959183673469, 0.2892057026476578, 0.2886178861788618, 0.2880324543610548, 0.2874493927125506, 0.2868686868686869, 0.2862903225806452, 0.2857142857142857, 0.285140562248996, 0.2845691382765531, 0.284, 0.2834331337325349, 0.28286852589641437, 0.2823061630218688, 0.28174603174603174, 0.2811881188118812, 0.28063241106719367, 0.28007889546351084, 0.281496062992126, 0.28094302554027506, 0.2803921568627451, 0.27984344422700586, 0.279296875, 0.2787524366471735, 0.2782101167315175, 0.27766990291262134, 0.2771317829457364, 0.2765957446808511, 0.27606177606177607, 0.27552986512524086, 0.275, 0.2744721689059501, 0.2739463601532567, 0.2734225621414914, 0.2729007633587786, 0.2723809523809524, 0.2718631178707224, 0.2713472485768501, 0.2708333333333333, 0.27032136105860116, 0.269811320754717, 0.2693032015065913, 0.26879699248120303, 0.2682926829268293, 0.26779026217228463, 0.2672897196261682, 0.26865671641791045, 0.27001862197392923, 0.2695167286245353, 0.2690166975881262, 0.26851851851851855, 0.2680221811460259, 0.26752767527675275, 0.26703499079189685, 0.2665441176470588, 0.26605504587155965, 0.26556776556776557, 0.26508226691042047, 0.2645985401459854, 0.2641165755919854, 0.2636363636363636, 0.2631578947368421, 0.26268115942028986, 0.26220614828209765, 0.26173285198555957, 0.26126126126126126, 0.2607913669064748, 0.26032315978456017, 0.25985663082437277, 0.259391771019678, 0.25892857142857145, 0.25846702317290554, 0.2580071174377224, 0.25754884547069273, 0.2570921985815603, 0.25663716814159293, 0.25618374558303886, 0.25573192239858905, 0.25528169014084506, 0.2548330404217926, 0.2543859649122807, 0.2539404553415061, 0.2534965034965035, 0.2530541012216405, 0.25261324041811845, 0.25217391304347825, 0.2517361111111111, 0.2512998266897747, 0.2508650519031142, 0.2504317789291883, 0.25, 0.2495697074010327, 0.24914089347079038, 0.24871355060034306, 0.2482876712328767, 0.24786324786324787, 0.24744027303754265, 0.24701873935264054, 0.2465986394557823, 0.2461799660441426, 0.2457627118644068, 0.24534686971235195, 0.24493243243243243, 0.24451939291736932, 0.2441077441077441, 0.24369747899159663, 0.24328859060402686, 0.24288107202680068, 0.24247491638795987, 0.24207011686143573, 0.24166666666666667, 0.24126455906821964, 0.24086378737541528, 0.24046434494195687, 0.24006622516556292, 0.2396694214876033, 0.23927392739273928, 0.23887973640856672, 0.23848684210526316, 0.23809523809523808, 0.23770491803278687, 0.23731587561374795, 0.2369281045751634, 0.2365415986949429, 0.23615635179153094, 0.23577235772357724, 0.2353896103896104, 0.23500810372771475, 0.23462783171521034, 0.23424878836833601, 0.23387096774193547, 0.23349436392914655, 0.23311897106109325, 0.23274478330658105, 0.23237179487179488, 0.232, 0.23162939297124602, 0.23125996810207336, 0.23089171974522293, 0.23052464228934816, 0.23015873015873015, 0.22979397781299524, 0.22943037974683544, 0.22906793048973143, 0.22870662460567823, 0.2283464566929134, 0.2279874213836478, 0.22762951334379905, 0.22727272727272727, 0.2269170579029734, 0.2265625, 0.22620904836193448, 0.22585669781931464, 0.2255054432348367, 0.2251552795031056, 0.2248062015503876, 0.22445820433436534, 0.2241112828438949, 0.22376543209876543, 0.22342064714946072, 0.2230769230769231, 0.2227342549923195, 0.2223926380368098, 0.222052067381317, 0.2217125382262997, 0.22137404580152673, 0.22103658536585366, 0.2207001522070015, 0.22036474164133737, 0.22003034901365706, 0.2196969696969697, 0.21936459909228442, 0.2190332326283988, 0.2187028657616893, 0.21987951807228914, 0.22105263157894736, 0.2222222222222222, 0.22338830584707647, 0.2245508982035928, 0.2257100149476831, 0.22686567164179106, 0.22652757078986588, 0.2261904761904762, 0.22585438335809807, 0.22551928783382788, 0.22518518518518518, 0.22633136094674555, 0.2259970457902511, 0.22566371681415928, 0.22533136966126657, 0.225, 0.2261380323054332, 0.22580645161290322, 0.22547584187408493, 0.22514619883040934, 0.22481751824817517, 0.22448979591836735, 0.22416302765647744, 0.22529069767441862, 0.22641509433962265, 0.22753623188405797, 0.22865412445730826, 0.22976878612716764, 0.23088023088023088, 0.23198847262247838, 0.23309352517985613, 0.23275862068965517, 0.23242467718794835, 0.23209169054441262, 0.23319027181688126, 0.23285714285714285, 0.23252496433666192, 0.23219373219373218, 0.23186344238975817, 0.23295454545454544, 0.2326241134751773, 0.23229461756373937, 0.23196605374823195, 0.2330508474576271, 0.23272214386459802, 0.2323943661971831, 0.2320675105485232, 0.23314606741573032, 0.23281907433380084, 0.23249299719887956, 0.23216783216783216, 0.23324022346368714, 0.23291492329149233, 0.23259052924791088, 0.23226703755215578, 0.23194444444444445, 0.231622746185853, 0.23130193905817176, 0.23098201936376211, 0.23204419889502761, 0.2317241379310345, 0.23140495867768596, 0.2310866574965612, 0.23076923076923078, 0.23182441700960219, 0.23150684931506849, 0.23119015047879618, 0.23087431693989072, 0.2305593451568895, 0.23160762942779292, 0.23129251700680273, 0.23097826086956522, 0.23066485753052918, 0.23035230352303523, 0.23004059539918809, 0.22972972972972974, 0.22941970310391363, 0.23045822102425875, 0.23014804845222073, 0.22983870967741934, 0.2295302013422819, 0.2292225201072386, 0.23025435073627845, 0.23128342245989306, 0.23097463284379172, 0.23066666666666666, 0.2303595206391478, 0.2300531914893617, 0.2297476759628154, 0.23076923076923078, 0.2304635761589404, 0.23015873015873015, 0.22985468956406868, 0.23087071240105542, 0.230566534914361, 0.23026315789473684, 0.22996057818659657, 0.22965879265091863, 0.22935779816513763, 0.22905759162303665, 0.23006535947712417, 0.2297650130548303, 0.22946544980443284, 0.22916666666666666, 0.22886866059817945, 0.22857142857142856, 0.22827496757457846, 0.22797927461139897, 0.2276843467011643, 0.22739018087855298, 0.2270967741935484, 0.2268041237113402, 0.22651222651222652, 0.2262210796915167, 0.22593068035943517, 0.22564102564102564, 0.22535211267605634, 0.22506393861892582, 0.2247765006385696, 0.22448979591836735, 0.22420382165605096, 0.22391857506361323, 0.22363405336721728, 0.2233502538071066, 0.22306717363751585, 0.22278481012658227, 0.2225031605562579, 0.2222222222222222, 0.22194199243379573, 0.2216624685138539, 0.22264150943396227, 0.22236180904522612, 0.22208281053952322, 0.22180451127819548, 0.22152690863579474, 0.22125, 0.2209737827715356, 0.22069825436408977, 0.22166874221668742, 0.22263681592039802, 0.2236024844720497, 0.22456575682382135, 0.2255266418835192, 0.2264851485148515, 0.22744128553770088, 0.22839506172839505, 0.22934648581997533, 0.23029556650246305, 0.23124231242312424, 0.2321867321867322, 0.2331288343558282, 0.23284313725490197, 0.23255813953488372, 0.23227383863080683, 0.23321123321123322, 0.2329268292682927, 0.23264311814859928, 0.23236009732360097, 0.23207776427703525, 0.23300970873786409, 0.23272727272727273, 0.2324455205811138, 0.23216444981862153, 0.2318840579710145, 0.2316043425814234, 0.23132530120481928, 0.23104693140794225, 0.23197115384615385, 0.23169267707082833, 0.2314148681055156, 0.2311377245508982, 0.23086124401913877, 0.23178016726403824, 0.2315035799522673, 0.23122765196662692, 0.23095238095238096, 0.23067776456599287, 0.23040380047505937, 0.23013048635824437, 0.22985781990521326, 0.22958579881656804, 0.2293144208037825, 0.22904368358913813, 0.22877358490566038, 0.22850412249705537, 0.22823529411764706, 0.22796709753231492, 0.22769953051643194, 0.22743259085580306, 0.22716627634660422, 0.22690058479532163, 0.2266355140186916, 0.22637106184364061, 0.2261072261072261, 0.22584400465657742, 0.2255813953488372, 0.22531939605110338, 0.22505800464037123, 0.22479721900347624, 0.22453703703703703, 0.22427745664739884, 0.22401847575057737, 0.223760092272203, 0.22350230414746544, 0.22324510932105868, 0.22298850574712645, 0.2227324913892078, 0.22247706422018348, 0.2222222222222222, 0.2219679633867277, 0.22171428571428572, 0.22146118721461186, 0.22120866590649943, 0.22095671981776766, 0.22070534698521047, 0.22045454545454546, 0.22020431328036322, 0.2199546485260771, 0.21970554926387317, 0.21945701357466063, 0.2192090395480226, 0.21896162528216703, 0.21871476888387825, 0.21846846846846846, 0.21822272215973004, 0.21797752808988763, 0.21773288439955107, 0.21748878923766815, 0.21724524076147816, 0.21700223713646533, 0.21675977653631284, 0.21651785714285715, 0.21627647714604237, 0.21714922048997773, 0.21690767519466073, 0.21666666666666667, 0.21642619311875694, 0.21618625277161863, 0.2159468438538206, 0.2168141592920354, 0.2165745856353591, 0.2163355408388521, 0.2160970231532525, 0.21585903083700442, 0.2156215621562156, 0.2153846153846154, 0.21624588364434688, 0.21600877192982457, 0.2157721796276013, 0.21553610503282275, 0.21530054644808744, 0.21615720524017468, 0.2159214830970556, 0.21568627450980393, 0.21545157780195864, 0.21521739130434783, 0.21498371335504887, 0.21475054229934923, 0.21451787648970747, 0.21428571428571427, 0.21405405405405406, 0.21382289416846653, 0.21359223300970873, 0.21336206896551724, 0.2131324004305705, 0.2129032258064516, 0.21267454350161116, 0.21244635193133046, 0.21221864951768488, 0.21199143468950749, 0.21176470588235294, 0.2126068376068376, 0.21237993596584845, 0.21215351812366737, 0.21192758253461128, 0.21170212765957447, 0.21147715196599362, 0.21125265392781317, 0.21208907741251326, 0.211864406779661, 0.21164021164021163, 0.21141649048625794, 0.21119324181626187, 0.2109704641350211, 0.2107481559536354, 0.21052631578947367, 0.2103049421661409, 0.21008403361344538, 0.2098635886673662, 0.20964360587002095, 0.2094240837696335, 0.20920502092050208, 0.2089864158829676, 0.20876826722338204, 0.20959332638164754, 0.209375, 0.20915712799167535, 0.20893970893970895, 0.2087227414330218, 0.20850622406639005, 0.20932642487046632, 0.20910973084886128, 0.20889348500517063, 0.20867768595041322, 0.2084623323013416, 0.20824742268041238, 0.20803295571575695, 0.20781893004115226, 0.20760534429599178, 0.20739219712525667, 0.20717948717948717, 0.2069672131147541, 0.2067553735926305, 0.2065439672801636, 0.20633299284984677, 0.20714285714285716, 0.2069317023445464, 0.20672097759674135, 0.20651068158697863, 0.20630081300813008, 0.20609137055837565, 0.20588235294117646, 0.20567375886524822, 0.2054655870445344, 0.20525783619817997, 0.20505050505050504, 0.20484359233097882, 0.20463709677419356, 0.20443101711983888, 0.20422535211267606, 0.20402010050251257, 0.20381526104417672, 0.2046138415245737, 0.20440881763527055, 0.2042042042042042, 0.204], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n",
      "Ratio of states visited for ep 0 [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [0.002770083102493075, 0.00554016620498615, 0.007202216066481995, 0.00997229916897507, 0.011634349030470914, 0.01329639889196676, 0.014958448753462602, 0.015512465373961217, 0.015512465373961217, 0.017174515235457065, 0.01772853185595568, 0.018282548476454295, 0.01883656509695291, 0.019944598337950138, 0.0221606648199446, 0.023268698060941825, 0.024376731301939056, 0.02493074792243767, 0.027146814404432135, 0.02880886426592798, 0.03047091412742382, 0.03213296398891967, 0.0332409972299169, 0.03434903047091413, 0.036011080332409975, 0.037673130193905814, 0.03933518005540167, 0.040997229916897505, 0.04155124653739613, 0.04265927977839336, 0.04376731301939059, 0.0443213296398892, 0.04542936288088643, 0.04709141274238228, 0.04930747922437673, 0.05041551246537396, 0.05207756232686981, 0.05373961218836565, 0.05595567867036011, 0.05706371191135734, 0.058171745152354584, 0.05983379501385042, 0.06094182825484765, 0.06149584487534626, 0.06371191135734072, 0.0659279778393352, 0.06703601108033241, 0.06703601108033241, 0.06869806094182826, 0.0703601108033241, 0.07257617728531855, 0.07368421052631578, 0.07534626038781164, 0.07700831024930747, 0.07867036011080333, 0.08033240997229918, 0.08199445983379502, 0.08310249307479226, 0.08476454293628809, 0.08642659279778395, 0.08642659279778395, 0.08642659279778395, 0.08642659279778395, 0.08698060941828255, 0.08864265927977841, 0.09085872576177284, 0.09141274238227146, 0.09252077562326869, 0.09252077562326869, 0.09252077562326869, 0.09362880886426592, 0.09584487534626039, 0.09750692520775624, 0.1002770083102493, 0.10193905817174516, 0.10249307479224376, 0.1030470914127424, 0.10470914127423825, 0.10637119113573408, 0.10747922437673132, 0.10803324099722993, 0.10914127423822714, 0.110803324099723, 0.11301939058171744, 0.11357340720221606, 0.1146814404432133, 0.11689750692520776, 0.11800554016620497, 0.12022160664819943, 0.1218836565096953, 0.12354570637119114, 0.12409972299168974, 0.12520775623268698, 0.12631578947368421, 0.12686980609418283, 0.12853185595567868, 0.12853185595567868, 0.12908587257617726, 0.1301939058171745, 0.1318559556786704, 0.132409972299169, 0.1329639889196676, 0.1329639889196676, 0.1335180055401662, 0.13518005540166206, 0.13628808864265926, 0.1379501385041551, 0.13850415512465375, 0.1407202216066482, 0.14182825484764544, 0.14293628808864267, 0.1440443213296399, 0.1451523545706371, 0.14681440443213298, 0.14681440443213298, 0.1473684210526316, 0.14847645429362882, 0.14958448753462605, 0.15013850415512467, 0.15124653739612187, 0.15290858725761775, 0.15401662049861495, 0.15512465373961218, 0.1556786703601108, 0.1556786703601108, 0.15678670360110802, 0.15734072022160664, 0.15789473684210525, 0.1584487534626039, 0.1590027700831025, 0.15955678670360113, 0.16011080332409972, 0.16121883656509697, 0.16232686980609418, 0.1628808864265928, 0.16343490304709146, 0.16509695290858728, 0.1662049861495845, 0.1662049861495845, 0.1662049861495845, 0.16786703601108033, 0.16897506925207756, 0.17008310249307476, 0.1717451523545706, 0.17229916897506922, 0.1728531855955679, 0.17340720221606648, 0.17451523545706374, 0.17617728531855956, 0.17673130193905817, 0.17673130193905817, 0.1778393351800554, 0.17950138504155125, 0.18005540166204986, 0.18005540166204986, 0.18116343490304707, 0.18116343490304707, 0.18227146814404432, 0.1828254847645429, 0.1828254847645429, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18393351800554014, 0.18448753462603878, 0.18614958448753463, 0.18781163434903045, 0.18947368421052632, 0.18947368421052632, 0.19002770083102494, 0.19113573407202217, 0.19168975069252076, 0.19279778393351804, 0.19335180055401663, 0.19390581717451524, 0.19501385041551247, 0.1966759002770083, 0.19778393351800555, 0.19778393351800555, 0.19833795013850417, 0.2, 0.20110803324099727, 0.20166204986149588, 0.20166204986149588, 0.20166204986149588, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.2033240997229917, 0.20443213296398893, 0.20443213296398893, 0.20498614958448752, 0.20609418282548475, 0.20664819944598337, 0.20664819944598337, 0.20664819944598337, 0.20775623268698062, 0.20775623268698062, 0.20831024930747924, 0.20941828254847644, 0.21052631578947367, 0.2116343490304709, 0.21218836565096955, 0.21274238227146816, 0.21329639889196678, 0.21385041551246536, 0.21440443213296398, 0.2149584487534626, 0.2155124653739612, 0.21662049861495847, 0.21662049861495847, 0.21662049861495847, 0.21717451523545708, 0.21717451523545708, 0.2177285318559557, 0.21828254847645429, 0.21994459833795013, 0.22049861495844877, 0.22049861495844877, 0.22105263157894733, 0.221606648199446, 0.2227146814404432, 0.22382271468144044, 0.22437673130193905, 0.22493074792243767, 0.22493074792243767, 0.22548476454293626, 0.22603878116343487, 0.2265927977839335, 0.22770083102493074, 0.22770083102493074, 0.22770083102493074, 0.22825484764542942, 0.22880886426592797, 0.22936288088642662, 0.22991689750692518, 0.23213296398891967, 0.2332409972299169, 0.23490304709141271, 0.23601108033240997, 0.2371191135734072, 0.2371191135734072, 0.23767313019390582, 0.23878116343490302, 0.23933518005540164, 0.23988919667590025, 0.23988919667590025, 0.23988919667590025, 0.2404432132963989, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24155124653739612, 0.24265927977839336, 0.24321329639889194, 0.24321329639889194, 0.24432132963988917, 0.24487534626038782, 0.24487534626038782, 0.24542936288088643, 0.24653739612188366, 0.24764542936288086, 0.24819944598337948, 0.24986149584487535, 0.25041551246537397, 0.25041551246537397, 0.25041551246537397, 0.25096952908587256, 0.2520775623268698, 0.25263157894736843, 0.253185595567867, 0.2548476454293629, 0.2554016620498615, 0.2565096952908587, 0.2581717451523546, 0.2581717451523546, 0.2587257617728532, 0.2592797783933518, 0.2592797783933518, 0.2598337950138504, 0.26038781163434904, 0.26094182825484763, 0.26204986149584486, 0.26260387811634345, 0.2642659279778393, 0.2648199445983379, 0.2659279778393352, 0.267590027700831, 0.267590027700831, 0.267590027700831, 0.26814404432132966, 0.26869806094182824, 0.2692520775623269, 0.2698060941828255, 0.2698060941828255, 0.2703601108033241, 0.2709141274238227, 0.2714681440443213, 0.27313019390581716, 0.2736842105263158, 0.2736842105263158, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.27590027700831027, 0.27590027700831027, 0.27590027700831027, 0.27590027700831027, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.2770083102493075, 0.2775623268698061, 0.2775623268698061, 0.2775623268698061, 0.27811634349030473, 0.27811634349030473, 0.27811634349030473, 0.27922437673130196, 0.27922437673130196, 0.27922437673130196, 0.27922437673130196, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.2803324099722992, 0.28088642659279783, 0.28088642659279783, 0.2814404432132964, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28310249307479224, 0.28310249307479224, 0.28310249307479224, 0.2836565096952909, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28476454293628806, 0.28476454293628806, 0.28476454293628806, 0.28531855955678675, 0.28531855955678675, 0.28587257617728534, 0.28587257617728534, 0.28642659279778393, 0.28753462603878116, 0.289196675900277, 0.29030470914127426, 0.2914127423822715, 0.2925207756232687, 0.2936288088642659, 0.29418282548476454, 0.2952908587257618, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29750692520775623, 0.29861495844875346, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.2997229916897507, 0.30083102493074787, 0.30083102493074787, 0.3013850415512465, 0.30193905817174516, 0.30249307479224374, 0.3030470914127424, 0.303601108033241, 0.3041551246537396, 0.3047091412742382, 0.30526315789473685, 0.30526315789473685, 0.3058171745152355, 0.3063711911357341, 0.30692520775623267, 0.30858725761772854, 0.30969529085872577, 0.310803324099723, 0.3113573407202216, 0.31191135734072023, 0.31191135734072023, 0.31191135734072023, 0.3124653739612188, 0.31301939058171746, 0.31357340720221605, 0.31412742382271464, 0.31412742382271464, 0.31412742382271464, 0.31468144044321333, 0.31468144044321333, 0.31468144044321333, 0.3152354570637119, 0.3157894736842105, 0.3157894736842105, 0.31634349030470915, 0.3168975069252078, 0.3174515235457064, 0.318005540166205, 0.3185595567867036, 0.31911357340720226, 0.31966759002770084, 0.32022160664819943, 0.3207756232686981, 0.3213296398891967, 0.3213296398891967, 0.3213296398891967, 0.3218836565096953, 0.3229916897506925, 0.3240997229916897, 0.32520775623268694, 0.3263157894736842, 0.32742382271468146, 0.32742382271468146, 0.3279778393351801, 0.3285318559556787, 0.3285318559556787, 0.3290858725761773, 0.3290858725761773, 0.3296398891966759, 0.3301939058171745, 0.3318559556786703, 0.33240997229916897, 0.3329639889196676, 0.3346260387811634, 0.3346260387811634, 0.33518005540166207, 0.33573407202216066, 0.3362880886426593, 0.3362880886426593, 0.3362880886426593, 0.33684210526315794, 0.33739612188365653, 0.33850415512465376, 0.33905817174515235, 0.33905817174515235, 0.339612188365651, 0.34072022160664817, 0.34072022160664817, 0.34127423822714686, 0.3418282548476454, 0.3429362880886426, 0.3429362880886426, 0.3429362880886426, 0.34349030470914127, 0.3445983379501385, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34570637119113573, 0.34681440443213296, 0.34847645429362883, 0.3490304709141275, 0.3501385041551247, 0.3518005540166205, 0.35290858725761776, 0.35346260387811634, 0.3545706371191136, 0.3556786703601108, 0.35678670360110804, 0.3584487534626039, 0.3595567867036011, 0.36121883656509696, 0.3623268698060942, 0.3634349030470914, 0.3634349030470914, 0.36454293628808865, 0.3656509695290858, 0.3656509695290858, 0.36620498614958447, 0.3673130193905817, 0.36897506925207757, 0.3700831024930748, 0.3717451523545707, 0.3717451523545707, 0.37229916897506926, 0.3734072022160665, 0.37451523545706367, 0.37506925207756237, 0.3756232686980609, 0.37617728531855954, 0.37673130193905824, 0.37673130193905824, 0.37673130193905824, 0.37728531855955677, 0.378393351800554, 0.378393351800554, 0.37894736842105264, 0.37894736842105264, 0.3795013850415513, 0.38060941828254846, 0.3817174515235457, 0.38227146814404434, 0.3828254847645429, 0.38337950138504157, 0.38337950138504157, 0.38337950138504157, 0.3839335180055402, 0.3850415512465374, 0.385595567867036, 0.3861495844875346, 0.38670360110803326, 0.38670360110803326, 0.38725761772853184, 0.3878116343490305, 0.3878116343490305, 0.3883656509695291, 0.3883656509695291, 0.3894736842105263, 0.39058171745152354, 0.3911357340720222, 0.3922437673130194, 0.39279778393351805, 0.39279778393351805, 0.39279778393351805, 0.39279778393351805, 0.39335180055401664, 0.39445983379501387, 0.39501385041551246, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.39612188365650974, 0.397229916897507, 0.3977839335180055, 0.39833795013850415, 0.39889196675900274, 0.3994459833795014, 0.4, 0.4, 0.4, 0.4, 0.40055401662049855, 0.4011080332409972, 0.4016620498614959, 0.4022160664819944, 0.4022160664819944, 0.40277008310249307, 0.40332409972299166, 0.40332409972299166, 0.4044321329639889, 0.40498614958448753, 0.40498614958448753, 0.4055401662049861, 0.40609418282548476, 0.407202216066482, 0.40775623268698064, 0.40831024930747917, 0.40886426592797787, 0.40886426592797787, 0.4094182825484764, 0.40997229916897504, 0.40997229916897504, 0.41052631578947363, 0.41274238227146814, 0.4132963988919668, 0.414404432132964, 0.41551246537396125, 0.41606648199445984, 0.417174515235457, 0.417174515235457, 0.4177285318559557, 0.4177285318559557, 0.4177285318559557, 0.41828254847645424, 0.4188365650969529, 0.4199445983379501, 0.4204986149584487, 0.42105263157894735, 0.4221606648199446, 0.4221606648199446, 0.4227146814404432, 0.4232686980609418, 0.4254847645429363, 0.42603878116343485, 0.42659279778393344, 0.42659279778393344, 0.42659279778393344, 0.4271468144044322, 0.4277008310249307, 0.4282548476454293, 0.4282548476454293, 0.42880886426592796, 0.42880886426592796, 0.4293628808864266, 0.4299168975069252, 0.4299168975069252, 0.4310249307479224, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.4321329639889197, 0.4321329639889197, 0.4332409972299168, 0.4337950138504155, 0.43434903047091417, 0.4349030470914127, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.43711911357340716, 0.43711911357340716, 0.4376731301939058, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.43878116343490303, 0.4393351800554017, 0.4393351800554017, 0.4393351800554017, 0.43988919667590026, 0.4404432132963989, 0.4409972299168975, 0.44155124653739614, 0.44210526315789467, 0.44265927977839337, 0.44265927977839337, 0.44265927977839337, 0.443213296398892, 0.443213296398892, 0.443213296398892, 0.443213296398892, 0.44432132963988913, 0.4448753462603879, 0.445983379501385, 0.4470914127423823, 0.4476454293628809, 0.4476454293628809, 0.4481994459833795, 0.4487534626038781, 0.4487534626038781, 0.44986149584487534, 0.44986149584487534, 0.44986149584487534, 0.4509695290858726, 0.45207756232686985, 0.4526315789473684, 0.4537396121883656, 0.45429362880886426, 0.45429362880886426, 0.4554016620498615, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45706371191135736, 0.4581717451523545, 0.4581717451523545, 0.45872576177285324, 0.45872576177285324, 0.4592797783933518, 0.4592797783933518, 0.4592797783933518, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.46149584487534623, 0.4620498614958449, 0.46260387811634357, 0.46260387811634357, 0.4631578947368421, 0.46426592797783933, 0.46426592797783933, 0.464819944598338, 0.46537396121883656, 0.4659279778393352, 0.4664819944598338, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.467590027700831, 0.467590027700831, 0.46814404432132967, 0.4686980609418283, 0.4703601108033242, 0.4714681440443214, 0.47202216066481995, 0.47257617728531864, 0.47257617728531864, 0.47257617728531864, 0.47257617728531864, 0.4736842105263158, 0.47479224376731305, 0.4759002770083103, 0.47645429362880887, 0.4770083102493075, 0.4770083102493075, 0.4781163434903048, 0.4792243767313019, 0.4792243767313019, 0.4797783933518006, 0.4808864265927978, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.481994459833795, 0.4825484764542936, 0.4836565096952909, 0.4853185595567867, 0.4864265927977839, 0.4869806094182826, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.489196675900277, 0.489196675900277, 0.489196675900277, 0.489196675900277, 0.4903047091412742, 0.4914127423822715, 0.4919667590027701, 0.49252077562326874, 0.49252077562326874, 0.4930747922437673, 0.49418282548476455, 0.4947368421052632, 0.49529085872576173, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.49639889196675896, 0.4969529085872576, 0.4969529085872576, 0.4969529085872576, 0.4969529085872576, 0.4975069252077563, 0.4986149584487535, 0.4997229916897507, 0.5013850415512465, 0.5019390581717451, 0.5030470914127423, 0.5041551246537396, 0.5047091412742382, 0.5058171745152354, 0.5069252077562327, 0.5074792243767312, 0.50803324099723, 0.5091412742382271, 0.5096952908587257, 0.5102493074792245, 0.5102493074792245, 0.5102493074792245, 0.510803324099723, 0.510803324099723, 0.5119113573407201, 0.5119113573407201, 0.5124653739612188, 0.5141274238227147, 0.5152354570637119, 0.5157894736842106, 0.5174515235457064, 0.5191135734072022, 0.5207756232686981, 0.5207756232686981, 0.5218836565096953, 0.5235457063711911, 0.5246537396121884, 0.5257617728531856, 0.5268698060941829, 0.5274238227146815, 0.5279778393351802, 0.5285318559556786, 0.5290858725761773, 0.5301939058171745, 0.5313019390581717, 0.532409972299169, 0.5329639889196676, 0.5329639889196676, 0.5335180055401662, 0.5346260387811634, 0.5362880886426593, 0.5368421052631579, 0.5373961218836565, 0.5385041551246538, 0.5390581717451524, 0.539612188365651, 0.5407202216066482, 0.5429362880886427, 0.5445983379501385, 0.5457063711911357, 0.546814404432133, 0.5473684210526316, 0.5473684210526316, 0.5479224376731302, 0.5479224376731302, 0.5484764542936288, 0.5490304709141275, 0.5490304709141275, 0.5501385041551247, 0.5506925207756233, 0.5512465373961218, 0.5523545706371191, 0.5529085872576178, 0.554016620498615, 0.5551246537396122, 0.5556786703601108, 0.5562326869806093, 0.5573407202216066, 0.5578947368421052, 0.5584487534626038, 0.5595567867036011, 0.5595567867036011, 0.5601108033240998, 0.5606648199445983, 0.5606648199445983, 0.5617728531855957, 0.5623268698060941, 0.5628808864265927, 0.5628808864265927, 0.5628808864265927, 0.5634349030470913, 0.56398891966759, 0.5645429362880886, 0.5656509695290859, 0.5667590027700831, 0.5673130193905817, 0.5673130193905817, 0.5684210526315789, 0.5695290858725761, 0.571191135734072, 0.5722991689750693, 0.5728531855955679, 0.573961218836565, 0.5745152354570637, 0.575623268698061, 0.5767313019390581, 0.5772853185595569, 0.5778393351800555, 0.578393351800554, 0.5800554016620498, 0.5817174515235457, 0.5822714681440443, 0.5828254847645429, 0.5844875346260388, 0.585595567867036, 0.585595567867036, 0.585595567867036, 0.5861495844875346, 0.5872576177285318, 0.5872576177285318, 0.5878116343490305, 0.5889196675900277, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.590027700831025, 0.590027700831025, 0.5911357340720221, 0.5922437673130194, 0.592797783933518, 0.5939058171745153, 0.5939058171745153, 0.5939058171745153, 0.5950138504155125, 0.5950138504155125, 0.5950138504155125, 0.5950138504155125, 0.5955678670360111, 0.5961218836565096, 0.5966759002770083, 0.5972299168975069, 0.5983379501385042, 0.5988919667590029, 0.5994459833795014, 0.6, 0.6011080332409973, 0.6022160664819945, 0.6033240997229917, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989], 'name': 'baseline', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}, {'x': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0, 1000.0], 'y': [0.002770083102493075, 0.00554016620498615, 0.00554016620498615, 0.008310249307479225, 0.008310249307479225, 0.008310249307479225, 0.008310249307479225, 0.0110803324099723, 0.013850415512465374, 0.01662049861495845, 0.019390581717451522, 0.019390581717451522, 0.0221606648199446, 0.0221606648199446, 0.024930747922437674, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.027700831024930747, 0.030470914127423823, 0.0332409972299169, 0.036011080332409975, 0.038781163434903045, 0.038781163434903045, 0.038781163434903045, 0.04155124653739612, 0.0443213296398892, 0.0443213296398892, 0.0443213296398892, 0.0443213296398892, 0.0443213296398892, 0.0443213296398892, 0.0443213296398892, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04709141274238227, 0.04986149584487535, 0.05263157894736842, 0.055401662049861494, 0.05817174515235457, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.060941828254847646, 0.06371191135734072, 0.0664819944598338, 0.06925207756232687, 0.07202216066481995, 0.07479224376731301, 0.07479224376731301, 0.07479224376731301, 0.07479224376731301, 0.07756232686980609, 0.07756232686980609, 0.07756232686980609, 0.07756232686980609, 0.07756232686980609, 0.08033240997229917, 0.08033240997229917, 0.08033240997229917, 0.08033240997229917, 0.08310249307479224, 0.08310249307479224, 0.08310249307479224, 0.08310249307479224, 0.08310249307479224, 0.08587257617728532, 0.08587257617728532, 0.08587257617728532, 0.08587257617728532, 0.08587257617728532, 0.0886426592797784, 0.0886426592797784, 0.0886426592797784, 0.0886426592797784, 0.0886426592797784, 0.09141274238227147, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09695290858725762, 0.0997229916897507, 0.10249307479224377, 0.10526315789473684, 0.10803324099722991, 0.10803324099722991, 0.11080332409972299, 0.11080332409972299, 0.11080332409972299, 0.11080332409972299, 0.11080332409972299, 0.11080332409972299, 0.11357340720221606, 0.11634349030470914, 0.11911357340720222, 0.12188365650969529, 0.12465373961218837, 0.12742382271468145, 0.13019390581717452, 0.1329639889196676, 0.13573407202216067, 0.13850415512465375, 0.13850415512465375, 0.13850415512465375, 0.14127423822714683, 0.14127423822714683, 0.14127423822714683, 0.14127423822714683, 0.14127423822714683, 0.1440443213296399, 0.1440443213296399, 0.1440443213296399, 0.1440443213296399, 0.14681440443213298, 0.14681440443213298, 0.14681440443213298, 0.14681440443213298, 0.14681440443213298, 0.14681440443213298, 0.14958448753462603, 0.14958448753462603, 0.14958448753462603, 0.14958448753462603, 0.1523545706371191, 0.1523545706371191, 0.1523545706371191, 0.1523545706371191, 0.1523545706371191, 0.15512465373961218, 0.15789473684210525, 0.15789473684210525, 0.15789473684210525, 0.15789473684210525, 0.16066481994459833, 0.16066481994459833, 0.16066481994459833, 0.16066481994459833, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.1634349030470914, 0.16620498614958448, 0.16897506925207756, 0.16897506925207756, 0.17174515235457063, 0.1745152354570637, 0.1772853185595568, 0.18005540166204986, 0.18282548476454294, 0.18282548476454294, 0.18282548476454294, 0.18282548476454294, 0.18282548476454294, 0.18559556786703602, 0.18559556786703602, 0.18559556786703602, 0.18559556786703602, 0.1883656509695291, 0.1883656509695291, 0.1883656509695291, 0.1883656509695291, 0.19113573407202217, 0.19113573407202217, 0.19113573407202217, 0.19113573407202217, 0.19113573407202217, 0.19390581717451524, 0.19390581717451524, 0.19390581717451524, 0.19390581717451524, 0.19390581717451524, 0.19667590027700832, 0.19667590027700832, 0.19667590027700832, 0.19667590027700832, 0.19667590027700832, 0.1994459833795014, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20498614958448755, 0.2077562326869806, 0.21052631578947367, 0.21329639889196675, 0.21606648199445982, 0.2188365650969529, 0.22160664819944598, 0.22437673130193905, 0.22714681440443213, 0.2299168975069252, 0.23268698060941828, 0.23545706371191136, 0.23822714681440443, 0.2409972299168975, 0.24376731301939059, 0.24653739612188366, 0.24653739612188366, 0.24653739612188366, 0.24653739612188366, 0.24930747922437674, 0.24930747922437674, 0.24930747922437674, 0.24930747922437674, 0.24930747922437674, 0.2520775623268698, 0.2520775623268698, 0.2520775623268698, 0.2520775623268698, 0.2520775623268698, 0.2520775623268698, 0.2548476454293629, 0.2548476454293629, 0.2548476454293629, 0.2548476454293629, 0.2548476454293629, 0.2548476454293629, 0.2548476454293629, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.25761772853185594, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.26038781163434904, 0.2631578947368421, 0.2659279778393352, 0.26869806094182824, 0.27146814404432135, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2770083102493075, 0.27977839335180055, 0.28254847645429365, 0.2853185595567867, 0.2880886426592798, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29362880886426596, 0.296398891966759, 0.29916897506925205, 0.30193905817174516, 0.3047091412742382, 0.3074792243767313, 0.31024930747922436, 0.31301939058171746, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3157894736842105, 0.3185595567867036, 0.32132963988919666, 0.32132963988919666, 0.32132963988919666, 0.32132963988919666, 0.32132963988919666, 0.32132963988919666, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.32409972299168976, 0.3268698060941828, 0.3296398891966759, 0.33240997229916897, 0.33518005540166207, 0.3379501385041551, 0.3407202216066482, 0.34349030470914127, 0.3462603878116344, 0.3490304709141274, 0.3518005540166205, 0.3545706371191136, 0.3573407202216066, 0.3601108033240997, 0.3628808864265928, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3656509695290859, 0.3684210526315789, 0.37119113573407203, 0.3739612188365651, 0.3767313019390582, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.38227146814404434, 0.3850415512465374, 0.3878116343490305, 0.39058171745152354, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3988919667590028, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40443213296398894, 0.407202216066482, 0.4099722991689751, 0.41274238227146814, 0.4155124653739612, 0.4182825484764543, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42105263157894735, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4265927977839335, 0.4293628808864266, 0.43213296398891965, 0.43490304709141275, 0.4376731301939058, 0.4404432132963989, 0.44321329639889195, 0.44598337950138506, 0.4487534626038781, 0.4487534626038781, 0.4487534626038781, 0.4487534626038781, 0.4515235457063712, 0.4515235457063712, 0.4515235457063712, 0.4515235457063712, 0.4515235457063712, 0.45429362880886426, 0.45429362880886426, 0.45429362880886426, 0.45429362880886426, 0.45706371191135736, 0.45706371191135736, 0.45706371191135736, 0.45706371191135736, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4626038781163435, 0.4626038781163435, 0.4626038781163435, 0.4626038781163435, 0.4626038781163435, 0.4626038781163435, 0.4626038781163435, 0.4626038781163435, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46814404432132967, 0.46814404432132967, 0.46814404432132967, 0.46814404432132967, 0.46814404432132967, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.47368421052631576, 0.47368421052631576, 0.47368421052631576, 0.47368421052631576, 0.47368421052631576, 0.47645429362880887, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.481994459833795, 0.481994459833795, 0.481994459833795, 0.481994459833795, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4930747922437673, 0.49584487534626037, 0.4986149584487535, 0.5013850415512465, 0.5041551246537396, 0.5069252077562327, 0.5096952908587258, 0.5124653739612188, 0.5152354570637119, 0.518005540166205, 0.5207756232686981, 0.5235457063711911, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5318559556786704, 0.5318559556786704, 0.5318559556786704, 0.5318559556786704, 0.5318559556786704, 0.5318559556786704, 0.5318559556786704, 0.5318559556786704, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5429362880886427, 0.5429362880886427, 0.5429362880886427, 0.5429362880886427, 0.5429362880886427, 0.5429362880886427, 0.5429362880886427, 0.5457063711911357, 0.5457063711911357, 0.5457063711911357, 0.5457063711911357, 0.5457063711911357, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5484764542936288, 0.5512465373961218, 0.5512465373961218, 0.5512465373961218, 0.5512465373961218, 0.5512465373961218, 0.5512465373961218, 0.5512465373961218, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.554016620498615, 0.556786703601108, 0.556786703601108, 0.556786703601108, 0.556786703601108, 0.556786703601108, 0.556786703601108, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5623268698060941, 0.5650969529085873, 0.5650969529085873, 0.5650969529085873, 0.5650969529085873], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n",
      "Exploration Factor for ep 0 [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [1.0, 1.0, 0.8666666666666666, 0.9, 0.8400000000000001, 0.8, 0.7714285714285715, 0.7, 0.6222222222222222, 0.6199999999999999, 0.5818181818181818, 0.55, 0.5230769230769231, 0.5142857142857142, 0.5333333333333333, 0.525, 0.5176470588235295, 0.5, 0.5157894736842106, 0.5200000000000001, 0.5238095238095238, 0.5272727272727272, 0.5217391304347825, 0.5166666666666667, 0.52, 0.523076923076923, 0.5259259259259259, 0.5285714285714286, 0.5172413793103449, 0.5133333333333333, 0.5096774193548387, 0.5, 0.49696969696969695, 0.5, 0.5085714285714286, 0.5055555555555555, 0.5081081081081081, 0.5105263157894736, 0.517948717948718, 0.515, 0.5121951219512195, 0.5142857142857143, 0.5116279069767442, 0.5045454545454546, 0.5111111111111112, 0.517391304347826, 0.5148936170212767, 0.5041666666666667, 0.5061224489795919, 0.508, 0.5137254901960785, 0.5115384615384615, 0.5132075471698113, 0.5148148148148148, 0.5163636363636364, 0.5178571428571429, 0.5192982456140351, 0.5172413793103449, 0.5186440677966101, 0.52, 0.5114754098360655, 0.5032258064516129, 0.49523809523809526, 0.490625, 0.49230769230769234, 0.49696969696969695, 0.49253731343283585, 0.4911764705882353, 0.48405797101449277, 0.47714285714285715, 0.476056338028169, 0.4805555555555555, 0.48219178082191777, 0.4891891891891892, 0.49066666666666664, 0.48684210526315785, 0.48311688311688317, 0.48461538461538456, 0.48607594936708864, 0.485, 0.4814814814814815, 0.48048780487804876, 0.48192771084337344, 0.48571428571428577, 0.48235294117647065, 0.48139534883720925, 0.4850574712643678, 0.48409090909090907, 0.48764044943820223, 0.48888888888888893, 0.49010989010989015, 0.48695652173913045, 0.4860215053763441, 0.4851063829787233, 0.4821052631578947, 0.4833333333333333, 0.4783505154639175, 0.47551020408163264, 0.47474747474747475, 0.476, 0.47326732673267324, 0.47058823529411764, 0.4660194174757281, 0.4634615384615385, 0.4647619047619048, 0.4641509433962264, 0.4654205607476635, 0.46296296296296297, 0.46605504587155966, 0.46545454545454545, 0.46486486486486484, 0.4642857142857143, 0.46371681415929206, 0.4649122807017544, 0.4608695652173913, 0.45862068965517244, 0.45811965811965816, 0.4576271186440678, 0.45546218487394957, 0.45499999999999996, 0.4561983471074381, 0.4557377049180328, 0.45528455284552843, 0.4532258064516129, 0.44959999999999994, 0.4492063492063492, 0.44724409448818897, 0.4453125, 0.44341085271317826, 0.44153846153846155, 0.43969465648854966, 0.43787878787878787, 0.437593984962406, 0.43731343283582086, 0.43555555555555553, 0.43382352941176466, 0.435036496350365, 0.43478260869565216, 0.4316546762589928, 0.42857142857142855, 0.4297872340425532, 0.4295774647887324, 0.4293706293706293, 0.4305555555555555, 0.4289655172413793, 0.4273972602739726, 0.4258503401360544, 0.4256756756756757, 0.42684563758389266, 0.4253333333333334, 0.42251655629139073, 0.4223684210526315, 0.4235294117647059, 0.4220779220779221, 0.4193548387096774, 0.41923076923076924, 0.4165605095541401, 0.41645569620253164, 0.41509433962264153, 0.4125, 0.4111801242236025, 0.40864197530864194, 0.40613496932515336, 0.4036585365853658, 0.4012121212121212, 0.3987951807228916, 0.39640718562874255, 0.3952380952380953, 0.3940828402366864, 0.3952941176470589, 0.39649122807017545, 0.39767441860465114, 0.3953757225433526, 0.39425287356321836, 0.3942857142857143, 0.3931818181818182, 0.39322033898305087, 0.3921348314606742, 0.3910614525139665, 0.3911111111111111, 0.39226519337016574, 0.39230769230769236, 0.3901639344262295, 0.38913043478260867, 0.3902702702702703, 0.3903225806451613, 0.3893048128342246, 0.38723404255319144, 0.38518518518518513, 0.38421052631578945, 0.38219895287958117, 0.38020833333333337, 0.37823834196891193, 0.37835051546391746, 0.3784615384615384, 0.376530612244898, 0.3756345177664975, 0.3757575757575758, 0.3748743718592965, 0.37300000000000005, 0.3711442786069652, 0.37128712871287134, 0.3694581280788177, 0.3686274509803921, 0.3687804878048781, 0.36893203883495146, 0.3690821256038648, 0.36826923076923074, 0.36746411483253594, 0.3666666666666667, 0.36587677725118484, 0.3650943396226415, 0.3643192488262911, 0.3635514018691589, 0.36372093023255814, 0.36203703703703705, 0.36036866359447, 0.35963302752293586, 0.35799086757990867, 0.3572727272727273, 0.35656108597285063, 0.35765765765765767, 0.3569506726457399, 0.35535714285714287, 0.3546666666666667, 0.35398230088495575, 0.3541850220264317, 0.3543859649122807, 0.3537117903930131, 0.35304347826086957, 0.35151515151515145, 0.35086206896551725, 0.3502145922746781, 0.34957264957264955, 0.3497872340425532, 0.3483050847457627, 0.34683544303797464, 0.346218487394958, 0.3456066945606694, 0.34500000000000003, 0.34439834024896265, 0.34628099173553717, 0.3465020576131687, 0.34754098360655733, 0.3477551020408163, 0.34796747967479674, 0.34655870445344134, 0.34596774193548385, 0.3461847389558233, 0.3456, 0.3450199203187251, 0.34365079365079365, 0.3422924901185771, 0.34173228346456697, 0.3411764705882353, 0.33984375, 0.33852140077821014, 0.3372093023255814, 0.33590733590733596, 0.33461538461538465, 0.33333333333333337, 0.33282442748091606, 0.33307984790874523, 0.3325757575757576, 0.3313207547169811, 0.331578947368421, 0.33108614232209743, 0.32985074626865674, 0.32936802973977697, 0.3296296296296296, 0.32988929889298896, 0.32941176470588235, 0.3304029304029304, 0.32992700729927005, 0.3287272727272727, 0.327536231884058, 0.32707581227436827, 0.3273381294964029, 0.3268817204301075, 0.3264285714285714, 0.3274021352313167, 0.3269503546099291, 0.32720848056537105, 0.328169014084507, 0.3270175438596491, 0.3265734265734266, 0.3261324041811847, 0.325, 0.3245674740484429, 0.32413793103448274, 0.3237113402061856, 0.323972602739726, 0.32354948805460754, 0.32448979591836735, 0.32406779661016943, 0.3243243243243243, 0.32525252525252524, 0.32416107382550335, 0.3230769230769231, 0.32266666666666666, 0.3222591362126246, 0.3218543046357616, 0.3214521452145215, 0.3203947368421053, 0.32, 0.3196078431372549, 0.3192182410423453, 0.32012987012987015, 0.31974110032362457, 0.31870967741935485, 0.3189710610932476, 0.317948717948718, 0.3169329073482428, 0.3159235668789809, 0.31492063492063493, 0.3139240506329114, 0.31293375394321765, 0.31257861635220124, 0.3115987460815047, 0.31062500000000004, 0.3096573208722741, 0.3086956521739131, 0.3077399380804954, 0.30740740740740746, 0.30646153846153845, 0.305521472392638, 0.3045871559633027, 0.3042682926829268, 0.30334346504559273, 0.30242424242424243, 0.30151057401812686, 0.3006024096385542, 0.3003003003003003, 0.30000000000000004, 0.2991044776119403, 0.29821428571428565, 0.29792284866468843, 0.29704142011834317, 0.296165191740413, 0.29647058823529415, 0.29560117302052785, 0.2947368421052632, 0.29387755102040813, 0.29360465116279066, 0.2927536231884058, 0.29190751445086704, 0.29106628242074933, 0.29022988505747127, 0.28997134670487107, 0.2897142857142857, 0.28888888888888886, 0.2886363636363637, 0.28838526912181306, 0.28757062146892653, 0.2867605633802818, 0.2859550561797753, 0.28515406162464985, 0.2843575418994414, 0.2835654596100279, 0.28277777777777774, 0.28254847645429365, 0.281767955801105, 0.28099173553719003, 0.2802197802197802, 0.27945205479452057, 0.2786885245901639, 0.2779291553133515, 0.27771739130434786, 0.2769647696476965, 0.2762162162162162, 0.27601078167115906, 0.2758064516129032, 0.2750670241286863, 0.27433155080213906, 0.27359999999999995, 0.2728723404255319, 0.2726790450928382, 0.2719576719576719, 0.2712401055408971, 0.2710526315789474, 0.27034120734908135, 0.27015706806282724, 0.2694516971279374, 0.26927083333333335, 0.26961038961038963, 0.2704663212435233, 0.2708010335917313, 0.27113402061855674, 0.2714652956298201, 0.27179487179487183, 0.2716112531969309, 0.27193877551020407, 0.2727735368956743, 0.2720812182741117, 0.2713924050632911, 0.27070707070707073, 0.2700251889168766, 0.2698492462311558, 0.2701754385964912, 0.27, 0.26932668329177056, 0.26865671641791045, 0.2679900744416873, 0.26782178217821784, 0.26814814814814814, 0.26748768472906403, 0.2673218673218673, 0.2671568627450981, 0.26699266503667485, 0.26682926829268294, 0.26666666666666666, 0.26650485436893206, 0.26634382566585957, 0.26618357487922706, 0.26554216867469876, 0.26538461538461544, 0.26522781774580334, 0.26507177033492824, 0.26587112171837707, 0.2661904761904762, 0.2665083135391924, 0.2663507109004739, 0.266193853427896, 0.265566037735849, 0.26494117647058824, 0.26478873239436623, 0.26463700234192034, 0.26448598130841117, 0.2643356643356643, 0.26372093023255816, 0.26310904872389795, 0.26296296296296295, 0.26235565819861434, 0.26175115207373273, 0.2616091954022989, 0.26146788990825687, 0.2608695652173913, 0.26073059360730594, 0.2605922551252847, 0.26045454545454544, 0.2603174603174603, 0.26018099547511314, 0.2600451467268623, 0.25990990990990986, 0.2597752808988764, 0.2596412556053812, 0.2595078299776286, 0.2589285714285714, 0.25835189309576834, 0.25822222222222224, 0.25853658536585367, 0.2588495575221239, 0.25916114790286976, 0.2594713656387665, 0.25978021978021976, 0.25921052631578945, 0.2590809628008753, 0.25895196506550217, 0.25838779956427016, 0.25826086956521743, 0.2577006507592191, 0.25757575757575757, 0.257451403887689, 0.25818965517241377, 0.25806451612903225, 0.25793991416309014, 0.25867237687366174, 0.2581196581196581, 0.2579957356076759, 0.257872340425532, 0.25774946921443737, 0.2572033898305085, 0.2566596194503171, 0.25654008438818565, 0.256421052631579, 0.25672268907563023, 0.2566037735849057, 0.2560669456066945, 0.2559498956158664, 0.25625, 0.2557172557172557, 0.2556016597510373, 0.25548654244306424, 0.25578512396694214, 0.2552577319587629, 0.25473251028806587, 0.2546201232032854, 0.2549180327868853, 0.25480572597137013, 0.25428571428571434, 0.2537678207739308, 0.2532520325203252, 0.2527383367139959, 0.25263157894736843, 0.2529292929292929, 0.2536290322580645, 0.2535211267605634, 0.25381526104417673, 0.2545090180360721, 0.2548, 0.254690618762475, 0.2549800796812749, 0.25526838966202786, 0.25555555555555554, 0.25623762376237624, 0.25652173913043474, 0.25719921104536486, 0.2574803149606299, 0.2577603143418467, 0.2572549019607843, 0.25753424657534246, 0.2578125, 0.2573099415204678, 0.25719844357976657, 0.2574757281553398, 0.25813953488372093, 0.2584139264990329, 0.25907335907335904, 0.2585741811175338, 0.25846153846153846, 0.25873320537428024, 0.2590038314176245, 0.25889101338432124, 0.2587786259541985, 0.25866666666666666, 0.2585551330798479, 0.25806451612903225, 0.2575757575757576, 0.2574669187145558, 0.2577358490566038, 0.25725047080979285, 0.2571428571428572, 0.25666041275797374, 0.2565543071161049, 0.256822429906542, 0.257089552238806, 0.25698324022346364, 0.25687732342007435, 0.25677179962894253, 0.25629629629629636, 0.25582255083179295, 0.255719557195572, 0.2559852670349908, 0.25588235294117645, 0.25577981651376147, 0.2556776556776557, 0.2552102376599635, 0.2551094890510949, 0.25500910746812383, 0.2545454545454545, 0.25444646098003626, 0.2539855072463768, 0.25424954792043397, 0.2545126353790614, 0.2544144144144144, 0.2546762589928057, 0.2545780969479354, 0.25412186379928314, 0.2536672629695885, 0.2532142857142857, 0.2531194295900178, 0.25338078291814947, 0.25328596802841924, 0.2531914893617021, 0.2527433628318584, 0.25229681978798585, 0.2518518518518518, 0.25140845070422535, 0.25131810193321613, 0.251578947368421, 0.25148861646234677, 0.25139860139860143, 0.2513089005235602, 0.25121951219512195, 0.25113043478260866, 0.25069444444444444, 0.25025996533795497, 0.24982698961937713, 0.24974093264248703, 0.24965517241379312, 0.24956970740103274, 0.24948453608247423, 0.2490566037735849, 0.24897260273972602, 0.24888888888888888, 0.24846416382252562, 0.24872231686541735, 0.24863945578231297, 0.24821731748726653, 0.248135593220339, 0.24805414551607444, 0.2483108108108108, 0.24822934232715008, 0.24814814814814815, 0.2480672268907563, 0.2476510067114094, 0.247571189279732, 0.24749163879598662, 0.24707846410684473, 0.24700000000000003, 0.24792013311148087, 0.2478405315614618, 0.24809286898839136, 0.24834437086092714, 0.24826446280991732, 0.2485148514851485, 0.24810543657331138, 0.2480263157894737, 0.24761904761904763, 0.24721311475409835, 0.2471358428805237, 0.24705882352941183, 0.24730831973898862, 0.24723127035830622, 0.24715447154471543, 0.24740259740259743, 0.24700162074554294, 0.24692556634304202, 0.24684975767366718, 0.24774193548387097, 0.24766505636070849, 0.247588424437299, 0.24719101123595505, 0.2467948717948718, 0.24672, 0.24664536741214058, 0.24657097288676236, 0.2461783439490446, 0.24610492845786966, 0.24571428571428572, 0.2456418383518225, 0.2455696202531646, 0.24518167456556084, 0.24542586750788647, 0.24535433070866142, 0.2449685534591195, 0.24458398744113025, 0.24420062695924766, 0.24381846635367763, 0.24343750000000003, 0.24305772230889233, 0.2426791277258567, 0.24230171073094864, 0.24192546583850927, 0.24155038759689923, 0.2414860681114551, 0.241112828438949, 0.24135802469135798, 0.24129429892141757, 0.24123076923076922, 0.24116743471582183, 0.2411042944785276, 0.24073506891271051, 0.24036697247706423, 0.24000000000000005, 0.23963414634146343, 0.23926940639269406, 0.23890577507598784, 0.2385432473444613, 0.2381818181818182, 0.23812405446293497, 0.2377643504531722, 0.23740573152337857, 0.23704819277108435, 0.23669172932330826, 0.2363363363363363, 0.2359820089955023, 0.2359281437125748, 0.23557548579970106, 0.23522388059701496, 0.23487332339791353, 0.23452380952380952, 0.23417533432392273, 0.2338278931750742, 0.23348148148148148, 0.2331360946745562, 0.23279172821270314, 0.23244837758112094, 0.2321060382916053, 0.23176470588235296, 0.23142437591776796, 0.23108504398826982, 0.23103953147877015, 0.2307017543859649, 0.23065693430656933, 0.2306122448979592, 0.2302765647743814, 0.22994186046511628, 0.22960812772133526, 0.22927536231884055, 0.229232995658466, 0.22919075144508674, 0.22886002886002882, 0.22853025936599422, 0.22848920863309355, 0.22844827586206895, 0.2284074605451937, 0.22836676217765045, 0.22832618025751072, 0.2282857142857143, 0.22796005706134093, 0.22763532763532765, 0.22759601706970128, 0.2272727272727273, 0.22695035460992904, 0.22662889518413598, 0.2268741159830269, 0.2268361581920904, 0.22708039492242596, 0.22732394366197184, 0.22728551336146272, 0.22696629213483144, 0.22692847124824683, 0.226890756302521, 0.22657342657342658, 0.22681564245810057, 0.2264993026499303, 0.22618384401114205, 0.22642559109874827, 0.22666666666666666, 0.22662968099861303, 0.2268698060941828, 0.2268326417704011, 0.22651933701657462, 0.2267586206896552, 0.22672176308539943, 0.2264099037138927, 0.2260989010989011, 0.22578875171467763, 0.22547945205479453, 0.22544459644322848, 0.22513661202185792, 0.22482946793997272, 0.22452316076294282, 0.22421768707482995, 0.22391304347826088, 0.2238805970149254, 0.22411924119241192, 0.22381596752368066, 0.22378378378378377, 0.2234817813765182, 0.22345013477088949, 0.2231493943472409, 0.2228494623655914, 0.22281879194630871, 0.2225201072386059, 0.22222222222222224, 0.22192513368983957, 0.2216288384512684, 0.22160000000000002, 0.2213049267643142, 0.22101063829787235, 0.2207171314741036, 0.2204244031830239, 0.2201324503311258, 0.21984126984126987, 0.21981505944517835, 0.21952506596306068, 0.21923583662714097, 0.2189473684210526, 0.21865965834428383, 0.2183727034120735, 0.218348623853211, 0.2183246073298429, 0.21830065359477122, 0.21801566579634465, 0.21799217731421122, 0.21822916666666664, 0.2179453836150845, 0.21792207792207793, 0.21789883268482488, 0.2178756476683938, 0.21785252263906857, 0.21782945736434106, 0.21754838709677418, 0.21726804123711338, 0.216988416988417, 0.21670951156812338, 0.21643132220795894, 0.21615384615384614, 0.21587708066581307, 0.21585677749360613, 0.215581098339719, 0.21556122448979592, 0.21554140127388535, 0.21603053435114505, 0.2162642947903431, 0.21624365482233499, 0.2162230671736375, 0.2159493670886076, 0.21567635903919088, 0.2154040404040404, 0.21563682219419925, 0.2158690176322418, 0.2161006289308176, 0.21608040201005027, 0.21606022584692597, 0.21578947368421053, 0.2160200250312891, 0.21625, 0.21598002496878901, 0.21596009975062347, 0.21618929016189292, 0.21616915422885574, 0.21590062111801242, 0.21563275434243176, 0.215365551425031, 0.2150990099009901, 0.21508034610630405, 0.2150617283950617, 0.21528976572133168, 0.21576354679802953, 0.21599015990159903, 0.21597051597051595, 0.21644171779141103, 0.21617647058823533, 0.21591187270501835, 0.2156479217603912, 0.21538461538461542, 0.21512195121951216, 0.21485992691839217, 0.21484184914841847, 0.21458080194410695, 0.21432038834951456, 0.21406060606060606, 0.21428571428571427, 0.2145102781136638, 0.21449275362318837, 0.21447527141133893, 0.21421686746987953, 0.21419975932611313, 0.2144230769230769, 0.21440576230492198, 0.2143884892086331, 0.21437125748502991, 0.21411483253588517, 0.2138590203106332, 0.21360381861575178, 0.21334922526817643, 0.2130952380952381, 0.21284185493460167, 0.2125890736342043, 0.21233689205219455, 0.21232227488151656, 0.2123076923076923, 0.21205673758865248, 0.21180637544273911, 0.21155660377358493, 0.21154299175500593, 0.2117647058823529, 0.21198589894242068, 0.2124413145539906, 0.21242672919109026, 0.2126463700234192, 0.2128654970760234, 0.21285046728971962, 0.21306884480746793, 0.21328671328671328, 0.21327124563445868, 0.21325581395348836, 0.21347270615563296, 0.21345707656612528, 0.21344148319814601, 0.2131944444444444, 0.21294797687861272, 0.21293302540415704, 0.2126874279123414, 0.21290322580645166, 0.2126582278481013, 0.2126436781609195, 0.21308840413318025, 0.21330275229357798, 0.21328751431844215, 0.2137299771167048, 0.21417142857142854, 0.2146118721461187, 0.2143671607753706, 0.21457858769931662, 0.2150170648464164, 0.21522727272727274, 0.21543700340522137, 0.21564625850340136, 0.21562853907134766, 0.21561085972850677, 0.21559322033898304, 0.2155756207674943, 0.21578354002254793, 0.215990990990991, 0.21619797525309337, 0.21617977528089888, 0.2159371492704826, 0.21591928251121076, 0.21612541993281073, 0.21655480984340042, 0.21653631284916203, 0.21651785714285715, 0.21672240802675585, 0.21670378619153677, 0.21668520578420467, 0.21688888888888885, 0.21753607103218647, 0.217960088691796, 0.21816168327796234, 0.21836283185840707, 0.2183425414364641, 0.21810154525386313, 0.21808158765159869, 0.21784140969162996, 0.2178217821782178, 0.21780219780219778, 0.21756311745334794, 0.21776315789473685, 0.21774370208105145, 0.21772428884026257, 0.21792349726775956, 0.21790393013100434, 0.21810250817884405, 0.21830065359477127, 0.21828073993471162, 0.21826086956521742, 0.2184581976112921, 0.21843817787418654, 0.2184182015167931, 0.21861471861471862, 0.21837837837837842, 0.21835853131749458, 0.21833872707659116, 0.21810344827586206, 0.2182992465016146, 0.2182795698924731, 0.2182599355531686, 0.21802575107296135, 0.2177920685959271, 0.21777301927194861, 0.21775401069518718, 0.2177350427350427, 0.2179295624332978, 0.21812366737739874, 0.21810436634717786, 0.2178723404255319, 0.21806588735387886, 0.21825902335456476, 0.21866383881230117, 0.21885593220338984, 0.21883597883597883, 0.21902748414376322, 0.21900739176346357, 0.2191983122362869, 0.21938883034773443, 0.21936842105263157, 0.21934805467928492, 0.219327731092437, 0.2197271773347324, 0.22012578616352202, 0.2201047120418848, 0.2200836820083682, 0.22048066875653083, 0.22066805845511483, 0.2204379562043796, 0.22020833333333328, 0.22018730489073884, 0.22037422037422041, 0.2201453790238837, 0.22012448132780085, 0.22031088082901557, 0.22028985507246376, 0.22006204756980355, 0.21983471074380168, 0.21960784313725493, 0.21938144329896908, 0.2191555097837281, 0.2191358024691358, 0.21891058581706066, 0.21909650924024637, 0.2192820512820513, 0.2192622950819672, 0.2194472876151484, 0.21922290388548057, 0.21899897854954037, 0.21918367346938777, 0.21896024464831804, 0.21873727087576372, 0.2185147507629705, 0.2184959349593496, 0.21847715736040607, 0.21845841784989856, 0.21843971631205675, 0.2186234817813765, 0.21860465116279068, 0.21858585858585858, 0.21856710393541876, 0.21875, 0.21893252769385702, 0.21911468812877266, 0.2192964824120603, 0.2190763052208835, 0.21885656970912737, 0.21863727454909823, 0.21841841841841844, 0.2182], 'name': 'baseline', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}, {'x': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0, 1000.0], 'y': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.9523809523809523, 0.9090909090909091, 0.8695652173913043, 0.8333333333333334, 0.84, 0.8076923076923077, 0.7777777777777778, 0.75, 0.7586206896551724, 0.7666666666666667, 0.7419354838709677, 0.71875, 0.696969696969697, 0.6764705882352942, 0.6571428571428571, 0.6388888888888888, 0.6216216216216216, 0.6052631578947368, 0.6153846153846154, 0.625, 0.6341463414634146, 0.6428571428571429, 0.6511627906976745, 0.6590909090909091, 0.6444444444444445, 0.6521739130434783, 0.6595744680851063, 0.6666666666666666, 0.673469387755102, 0.68, 0.6666666666666666, 0.6538461538461539, 0.660377358490566, 0.6666666666666666, 0.6545454545454545, 0.6428571428571429, 0.631578947368421, 0.6206896551724138, 0.6101694915254238, 0.6166666666666667, 0.6229508196721312, 0.6290322580645161, 0.6190476190476191, 0.609375, 0.6153846153846154, 0.6060606060606061, 0.6119402985074627, 0.6176470588235294, 0.6231884057971014, 0.6285714285714286, 0.6338028169014085, 0.6388888888888888, 0.6438356164383562, 0.6486486486486487, 0.64, 0.6447368421052632, 0.6493506493506493, 0.6410256410256411, 0.6455696202531646, 0.6375, 0.6419753086419753, 0.6341463414634146, 0.6385542168674698, 0.6428571428571429, 0.6352941176470588, 0.6395348837209303, 0.632183908045977, 0.6363636363636364, 0.6292134831460674, 0.6333333333333333, 0.6263736263736264, 0.6304347826086957, 0.6236559139784946, 0.6276595744680851, 0.6210526315789474, 0.625, 0.6185567010309279, 0.6224489795918368, 0.6161616161616161, 0.62, 0.6138613861386139, 0.6176470588235294, 0.6116504854368932, 0.6057692307692307, 0.6, 0.5943396226415094, 0.5887850467289719, 0.5833333333333334, 0.5779816513761468, 0.5727272727272728, 0.5675675675675675, 0.5625, 0.5575221238938053, 0.5526315789473685, 0.5478260869565217, 0.5431034482758621, 0.5384615384615384, 0.5338983050847458, 0.5294117647058824, 0.525, 0.5206611570247934, 0.5163934426229508, 0.5121951219512195, 0.5080645161290323, 0.504, 0.5, 0.49606299212598426, 0.4921875, 0.4883720930232558, 0.4846153846153846, 0.48091603053435117, 0.4772727272727273, 0.47368421052631576, 0.4701492537313433, 0.4666666666666667, 0.4632352941176471, 0.45985401459854014, 0.45652173913043476, 0.45323741007194246, 0.45, 0.44680851063829785, 0.44366197183098594, 0.4405594405594406, 0.4375, 0.43448275862068964, 0.4315068493150685, 0.42857142857142855, 0.42567567567567566, 0.4228187919463087, 0.42, 0.41721854304635764, 0.4144736842105263, 0.4117647058823529, 0.4090909090909091, 0.4064516129032258, 0.40384615384615385, 0.4012738853503185, 0.3987341772151899, 0.39622641509433965, 0.39375, 0.391304347826087, 0.3888888888888889, 0.38650306748466257, 0.38414634146341464, 0.38181818181818183, 0.3795180722891566, 0.3772455089820359, 0.375, 0.3727810650887574, 0.37058823529411766, 0.3684210526315789, 0.36627906976744184, 0.36416184971098264, 0.3620689655172414, 0.36, 0.35795454545454547, 0.3559322033898305, 0.3539325842696629, 0.35195530726256985, 0.35, 0.34806629834254144, 0.34615384615384615, 0.3442622950819672, 0.3423913043478261, 0.34054054054054056, 0.3387096774193548, 0.33689839572192515, 0.3351063829787234, 0.3333333333333333, 0.3368421052631579, 0.3403141361256545, 0.34375, 0.3471502590673575, 0.35051546391752575, 0.35384615384615387, 0.35714285714285715, 0.3604060913705584, 0.36363636363636365, 0.36683417085427134, 0.37, 0.373134328358209, 0.37623762376237624, 0.3793103448275862, 0.38235294117647056, 0.3853658536585366, 0.3883495145631068, 0.391304347826087, 0.3942307692307692, 0.3923444976076555, 0.3904761904761905, 0.3886255924170616, 0.3867924528301887, 0.38497652582159625, 0.38317757009345793, 0.3813953488372093, 0.38425925925925924, 0.3824884792626728, 0.38073394495412843, 0.3789954337899543, 0.37727272727272726, 0.38009049773755654, 0.3783783783783784, 0.37668161434977576, 0.3794642857142857, 0.37777777777777777, 0.37610619469026546, 0.3788546255506608, 0.3815789473684211, 0.38427947598253276, 0.3869565217391304, 0.38961038961038963, 0.3922413793103448, 0.3948497854077253, 0.3974358974358974, 0.4, 0.4025423728813559, 0.4050632911392405, 0.40756302521008403, 0.4100418410041841, 0.4125, 0.4107883817427386, 0.4090909090909091, 0.4074074074074074, 0.4057377049180328, 0.40408163265306124, 0.4024390243902439, 0.4008097165991903, 0.39919354838709675, 0.40160642570281124, 0.4, 0.398406374501992, 0.3968253968253968, 0.3952569169960474, 0.3937007874015748, 0.39215686274509803, 0.390625, 0.38910505836575876, 0.3875968992248062, 0.38996138996138996, 0.38846153846153847, 0.38697318007662834, 0.38549618320610685, 0.3840304182509506, 0.38257575757575757, 0.38113207547169814, 0.38345864661654133, 0.38202247191011235, 0.3843283582089552, 0.3828996282527881, 0.3814814814814815, 0.3800738007380074, 0.38235294117647056, 0.38095238095238093, 0.3795620437956204, 0.3781818181818182, 0.37681159420289856, 0.37906137184115524, 0.3776978417266187, 0.3763440860215054, 0.375, 0.3736654804270463, 0.3723404255319149, 0.3710247349823322, 0.3732394366197183, 0.37543859649122807, 0.3741258741258741, 0.37630662020905925, 0.375, 0.3737024221453287, 0.3724137931034483, 0.3711340206185567, 0.3732876712328767, 0.37542662116040953, 0.3741496598639456, 0.3728813559322034, 0.3716216216216216, 0.37037037037037035, 0.3691275167785235, 0.36789297658862874, 0.36666666666666664, 0.3654485049833887, 0.36423841059602646, 0.36303630363036304, 0.3618421052631579, 0.3639344262295082, 0.3627450980392157, 0.36156351791530944, 0.36038961038961037, 0.3592233009708738, 0.3580645161290323, 0.35691318327974275, 0.3557692307692308, 0.3546325878594249, 0.35668789808917195, 0.35873015873015873, 0.36075949367088606, 0.3627760252365931, 0.36477987421383645, 0.3667711598746082, 0.36875, 0.3707165109034268, 0.3695652173913043, 0.3684210526315789, 0.37037037037037035, 0.36923076923076925, 0.36809815950920244, 0.3669724770642202, 0.36585365853658536, 0.364741641337386, 0.36363636363636365, 0.36555891238670696, 0.3644578313253012, 0.3633633633633634, 0.3652694610778443, 0.3641791044776119, 0.3630952380952381, 0.3620178041543027, 0.3609467455621302, 0.35988200589970504, 0.3588235294117647, 0.35777126099706746, 0.3567251461988304, 0.3556851311953353, 0.3546511627906977, 0.3536231884057971, 0.35260115606936415, 0.3515850144092219, 0.3505747126436782, 0.3495702005730659, 0.3485714285714286, 0.3504273504273504, 0.3494318181818182, 0.34844192634560905, 0.3474576271186441, 0.3464788732394366, 0.3455056179775281, 0.3445378151260504, 0.3435754189944134, 0.34540389972144847, 0.3472222222222222, 0.3490304709141274, 0.35082872928176795, 0.3526170798898072, 0.3516483516483517, 0.3506849315068493, 0.3524590163934426, 0.35149863760217986, 0.35054347826086957, 0.34959349593495936, 0.34864864864864864, 0.3477088948787062, 0.3467741935483871, 0.34584450402144773, 0.3449197860962567, 0.344, 0.34308510638297873, 0.3421750663129973, 0.3439153439153439, 0.34300791556728233, 0.3447368421052632, 0.3438320209973753, 0.34293193717277487, 0.34203655352480417, 0.3411458333333333, 0.34025974025974026, 0.3393782383419689, 0.34108527131782945, 0.3427835051546392, 0.3444730077120823, 0.34615384615384615, 0.3452685421994885, 0.3469387755102041, 0.3460559796437659, 0.34517766497461927, 0.3468354430379747, 0.34595959595959597, 0.345088161209068, 0.3442211055276382, 0.3458646616541353, 0.345, 0.34413965087281795, 0.34328358208955223, 0.3424317617866005, 0.3415841584158416, 0.34074074074074073, 0.3399014778325123, 0.33906633906633904, 0.3382352941176471, 0.3374083129584352, 0.33658536585365856, 0.3357664233576642, 0.33495145631067963, 0.3365617433414044, 0.3357487922705314, 0.3349397590361446, 0.33413461538461536, 0.3333333333333333, 0.33253588516746413, 0.3317422434367542, 0.33095238095238094, 0.33016627078384797, 0.3293838862559242, 0.32860520094562645, 0.330188679245283, 0.32941176470588235, 0.3286384976525822, 0.32786885245901637, 0.32710280373831774, 0.32634032634032634, 0.32558139534883723, 0.3248259860788863, 0.32407407407407407, 0.325635103926097, 0.3248847926267281, 0.32413793103448274, 0.3256880733944954, 0.32494279176201374, 0.3242009132420091, 0.3234624145785877, 0.325, 0.3242630385487528, 0.3235294117647059, 0.3227990970654628, 0.32207207207207206, 0.32134831460674157, 0.32062780269058294, 0.319910514541387, 0.31919642857142855, 0.3184855233853007, 0.31777777777777777, 0.31929046563192903, 0.3185840707964602, 0.31788079470198677, 0.31718061674008813, 0.31648351648351647, 0.31798245614035087, 0.3172866520787746, 0.3165938864628821, 0.3159041394335512, 0.31521739130434784, 0.31670281995661603, 0.31601731601731603, 0.31533477321814257, 0.3146551724137931, 0.3139784946236559, 0.315450643776824, 0.3147751605995717, 0.3141025641025641, 0.31343283582089554, 0.3127659574468085, 0.31210191082802546, 0.3114406779661017, 0.3107822410147992, 0.310126582278481, 0.3094736842105263, 0.31092436974789917, 0.31027253668763105, 0.30962343096234307, 0.3089770354906054, 0.3104166666666667, 0.3097713097713098, 0.3091286307053942, 0.3105590062111801, 0.30991735537190085, 0.30927835051546393, 0.30864197530864196, 0.3080082135523614, 0.3094262295081967, 0.310838445807771, 0.3122448979591837, 0.31160896130346233, 0.31097560975609756, 0.3103448275862069, 0.3097165991902834, 0.3090909090909091, 0.3084677419354839, 0.30784708249496984, 0.3072289156626506, 0.3066132264529058, 0.306, 0.30538922155688625, 0.3047808764940239, 0.30417495029821073, 0.30357142857142855, 0.30297029702970296, 0.30434782608695654, 0.3057199211045365, 0.30708661417322836, 0.30844793713163066, 0.30980392156862746, 0.3111545988258317, 0.3125, 0.3138401559454191, 0.3151750972762646, 0.31650485436893205, 0.3178294573643411, 0.3191489361702128, 0.3204633204633205, 0.3198458574181118, 0.3192307692307692, 0.31861804222648754, 0.31800766283524906, 0.3173996175908222, 0.31679389312977096, 0.3161904761904762, 0.3155893536121673, 0.31499051233396586, 0.3143939393939394, 0.31379962192816635, 0.3132075471698113, 0.3126177024482109, 0.31203007518796994, 0.31144465290806755, 0.31086142322097376, 0.3102803738317757, 0.30970149253731344, 0.3091247672253259, 0.30855018587360594, 0.3079777365491651, 0.3074074074074074, 0.3068391866913124, 0.3062730627306273, 0.30570902394106814, 0.30514705882352944, 0.30458715596330277, 0.304029304029304, 0.30347349177330896, 0.3029197080291971, 0.302367941712204, 0.3018181818181818, 0.30127041742286753, 0.3007246376811594, 0.30018083182640143, 0.2996389891696751, 0.2990990990990991, 0.29856115107913667, 0.2980251346499102, 0.2974910394265233, 0.29695885509839, 0.29642857142857143, 0.29590017825311943, 0.29537366548042704, 0.29484902309058614, 0.29432624113475175, 0.2938053097345133, 0.29328621908127206, 0.2927689594356261, 0.29225352112676056, 0.29173989455184535, 0.2912280701754386, 0.29071803852889666, 0.2902097902097902, 0.28970331588132636, 0.289198606271777, 0.288695652173913, 0.2881944444444444, 0.2876949740034662, 0.28719723183391005, 0.2867012089810017, 0.28620689655172415, 0.2857142857142857, 0.2852233676975945, 0.2847341337907376, 0.2842465753424658, 0.28376068376068375, 0.2832764505119454, 0.282793867120954, 0.282312925170068, 0.28183361629881154, 0.28135593220338984, 0.2808798646362098, 0.28040540540540543, 0.2799325463743676, 0.27946127946127947, 0.27899159663865547, 0.2785234899328859, 0.2780569514237856, 0.27759197324414714, 0.27712854757929883, 0.27666666666666667, 0.2762063227953411, 0.2757475083056478, 0.2752902155887231, 0.27483443708609273, 0.2743801652892562, 0.2739273927392739, 0.27347611202635913, 0.2730263157894737, 0.27257799671592775, 0.2721311475409836, 0.27168576104746317, 0.27124183006535946, 0.2707993474714519, 0.2703583061889251, 0.26991869918699185, 0.2694805194805195, 0.26904376012965964, 0.2686084142394822, 0.2681744749596123, 0.267741935483871, 0.2673107890499195, 0.26688102893890675, 0.2664526484751204, 0.266025641025641, 0.2656, 0.26517571884984026, 0.2647527910685805, 0.2643312101910828, 0.26391096979332274, 0.2634920634920635, 0.2630744849445325, 0.2626582278481013, 0.26224328593996843, 0.2618296529968454, 0.2614173228346457, 0.2610062893081761, 0.260596546310832, 0.2601880877742947, 0.2597809076682316, 0.259375, 0.2589703588143526, 0.2585669781931464, 0.2581648522550544, 0.2577639751552795, 0.25736434108527134, 0.25696594427244585, 0.25656877897990726, 0.25617283950617287, 0.25577812018489987, 0.2553846153846154, 0.25499231950844853, 0.254601226993865, 0.2542113323124043, 0.25382262996941896, 0.2534351145038168, 0.2530487804878049, 0.2526636225266362, 0.25227963525835867, 0.251896813353566, 0.2515151515151515, 0.25113464447806355, 0.25075528700906347, 0.25037707390648567, 0.25, 0.24962406015037594, 0.24924924924924924, 0.24887556221889057, 0.24850299401197604, 0.24813153961136025, 0.24776119402985075, 0.2473919523099851, 0.24702380952380953, 0.24665676077265974, 0.24629080118694363, 0.24592592592592594, 0.2455621301775148, 0.24519940915805022, 0.2448377581120944, 0.24447717231222385, 0.24411764705882352, 0.24375917767988253, 0.2434017595307918, 0.2430453879941435, 0.24269005847953215, 0.24233576642335766, 0.24198250728862974, 0.24163027656477437, 0.24127906976744187, 0.2409288824383164, 0.24057971014492754, 0.2402315484804631, 0.2398843930635838, 0.23953823953823955, 0.23919308357348704, 0.23884892086330936, 0.23850574712643677, 0.2381635581061693, 0.23782234957020057, 0.2374821173104435, 0.23714285714285716, 0.23680456490727533, 0.23646723646723647, 0.2361308677098151, 0.23579545454545456, 0.23546099290780143, 0.23512747875354106, 0.2362093352192362, 0.23728813559322035, 0.23695345557122707, 0.23661971830985915, 0.23628691983122363, 0.23595505617977527, 0.23562412342215988, 0.23529411764705882, 0.23636363636363636, 0.2360335195530726, 0.23570432357043236, 0.23537604456824512, 0.23504867872044508, 0.2361111111111111, 0.23578363384188628, 0.23545706371191136, 0.2351313969571231, 0.23480662983425415, 0.23448275862068965, 0.23415977961432508, 0.23383768913342504, 0.23351648351648352, 0.23319615912208505, 0.23424657534246576, 0.23529411764705882, 0.23633879781420766, 0.23601637107776263, 0.2356948228882834, 0.23537414965986395, 0.23505434782608695, 0.23473541383989144, 0.23441734417344173, 0.23410013531799728, 0.23378378378378378, 0.23346828609986506, 0.23315363881401618, 0.23283983849259757, 0.2325268817204301, 0.23221476510067113, 0.23190348525469168, 0.23159303882195448, 0.23128342245989306, 0.23097463284379172, 0.23066666666666666, 0.2303595206391478, 0.2300531914893617, 0.2297476759628154, 0.22944297082228116, 0.22913907284768212, 0.22883597883597884, 0.2285336856010568, 0.22823218997361477, 0.22793148880105402, 0.22763157894736843, 0.22733245729303547, 0.22703412073490814, 0.22673656618610746, 0.22643979057591623, 0.2261437908496732, 0.2258485639686684, 0.2255541069100391, 0.22526041666666666, 0.22496749024707413, 0.22467532467532467, 0.2243839169909209, 0.22409326424870465, 0.2238033635187581, 0.2235142118863049, 0.2232258064516129, 0.22293814432989692, 0.22265122265122264, 0.22236503856041132, 0.2220795892169448, 0.22179487179487178, 0.22151088348271447, 0.22122762148337596, 0.22094508301404853, 0.22066326530612246, 0.22038216560509555, 0.22010178117048346, 0.21982210927573062, 0.21954314720812182, 0.21926489226869456, 0.2189873417721519, 0.21871049304677623, 0.21843434343434343, 0.21815889029003782, 0.21788413098236775, 0.21761006289308177, 0.21733668341708542, 0.21706398996235884, 0.21679197994987467, 0.2165206508135169, 0.21625, 0.21598002496878901, 0.21571072319201995, 0.21544209215442092, 0.21517412935323382, 0.21490683229813665, 0.21464019851116625, 0.21437422552664187, 0.2141089108910891, 0.2138442521631644, 0.21481481481481482, 0.21454993834771888, 0.21428571428571427, 0.2140221402214022, 0.21375921375921375, 0.2147239263803681, 0.21446078431372548, 0.2141982864137087, 0.2139364303178484, 0.21367521367521367, 0.21341463414634146, 0.21315468940316687, 0.21289537712895376, 0.212636695018226, 0.21237864077669902, 0.21333333333333335, 0.21307506053268765, 0.21281741233373638, 0.21256038647342995, 0.21230398069963813, 0.21325301204819277, 0.21299638989169675, 0.2127403846153846, 0.21248499399759904, 0.21223021582733814, 0.21317365269461078, 0.21291866028708134, 0.2126642771804062, 0.21360381861575178, 0.21454112038140644, 0.2154761904761905, 0.2164090368608799, 0.2173396674584323, 0.21826809015421114, 0.21919431279620852, 0.22011834319526627, 0.22104018912529552, 0.22077922077922077, 0.22169811320754718, 0.2226148409893993, 0.2235294117647059, 0.22326674500587543, 0.22300469483568075, 0.2227432590855803, 0.2224824355971897, 0.2222222222222222, 0.2219626168224299, 0.22170361726954493, 0.22144522144522144, 0.22118742724097787, 0.22093023255813954, 0.22067363530778164, 0.22041763341067286, 0.220162224797219, 0.2199074074074074, 0.21965317919075145, 0.21939953810623555, 0.21914648212226068, 0.21889400921658986, 0.2186421173762946, 0.21839080459770116, 0.21814006888633755, 0.21788990825688073, 0.21764032073310424, 0.21739130434782608, 0.21714285714285714, 0.21689497716894976, 0.21664766248574688, 0.2164009111617312, 0.21729237770193402, 0.21704545454545454, 0.21679909194097616, 0.2165532879818594, 0.21630804077010193, 0.2160633484162896, 0.21581920903954802, 0.21557562076749437, 0.2153325817361894, 0.21621621621621623, 0.21597300337457817, 0.21685393258426966, 0.2166105499438833, 0.2163677130044843, 0.21612541993281076, 0.21588366890380314, 0.21564245810055865, 0.21651785714285715, 0.21627647714604237, 0.21603563474387527, 0.21579532814238042, 0.21555555555555556, 0.2153163152053274, 0.21507760532150777, 0.21483942414174972, 0.21460176991150443, 0.2154696132596685, 0.2152317880794702, 0.21499448732083792, 0.21475770925110133, 0.2145214521452145, 0.21428571428571427, 0.21405049396267836, 0.2138157894736842, 0.21358159912376778, 0.21334792122538293, 0.21311475409836064, 0.212882096069869, 0.21264994547437296, 0.21241830065359477, 0.21218715995647444, 0.21195652173913043, 0.21172638436482086, 0.21149674620390455, 0.2112676056338028, 0.21103896103896103, 0.21081081081081082, 0.21058315334773217, 0.21035598705501618, 0.2101293103448276, 0.20990312163616792, 0.20967741935483872, 0.20945220193340494, 0.2092274678111588, 0.21007502679528403, 0.21092077087794434, 0.21176470588235294, 0.2126068376068376, 0.21344717182497333, 0.21428571428571427, 0.21512247071352503, 0.2148936170212766, 0.2146652497343252, 0.21443736730360935, 0.2142099681866384, 0.21398305084745764, 0.21481481481481482, 0.2156448202959831, 0.21647307286166842, 0.21729957805907174, 0.21812434141201265, 0.21789473684210525, 0.21766561514195584, 0.21743697478991597, 0.21825813221406087, 0.2180293501048218, 0.21780104712041884, 0.2175732217573222, 0.2173458725182863, 0.21816283924843424, 0.21793534932221065, 0.21770833333333334, 0.21748178980228927, 0.21725571725571727, 0.21703011422637591, 0.21680497925311204, 0.21658031088082902, 0.2163561076604555, 0.21613236814891418, 0.2159090909090909, 0.21568627450980393, 0.2154639175257732, 0.21524201853759012, 0.21502057613168724, 0.2147995889003083, 0.21457905544147843, 0.21435897435897436, 0.21413934426229508, 0.21392016376663256, 0.21370143149284254, 0.21348314606741572, 0.21326530612244898, 0.2130479102956167, 0.21283095723014256, 0.2126144455747711, 0.21239837398373984, 0.21218274111675126, 0.2129817444219067, 0.2127659574468085, 0.2125506072874494, 0.2123356926188069, 0.21212121212121213, 0.2119071644803229, 0.21169354838709678, 0.21148036253776434, 0.2112676056338028, 0.21206030150753769, 0.21184738955823293, 0.21163490471414242, 0.21142284569138275, 0.21121121121121122, 0.211], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n",
      "Ratio of states visited for ep 0 [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [0.002770083102493075, 0.00554016620498615, 0.007202216066481995, 0.00997229916897507, 0.011634349030470914, 0.01329639889196676, 0.014958448753462602, 0.015512465373961217, 0.015512465373961217, 0.017174515235457065, 0.01772853185595568, 0.018282548476454295, 0.01883656509695291, 0.019944598337950138, 0.0221606648199446, 0.023268698060941825, 0.024376731301939056, 0.02493074792243767, 0.027146814404432135, 0.02880886426592798, 0.03047091412742382, 0.03213296398891967, 0.0332409972299169, 0.03434903047091413, 0.036011080332409975, 0.037673130193905814, 0.03933518005540167, 0.040997229916897505, 0.04155124653739613, 0.04265927977839336, 0.04376731301939059, 0.0443213296398892, 0.04542936288088643, 0.04709141274238228, 0.04930747922437673, 0.05041551246537396, 0.05207756232686981, 0.05373961218836565, 0.05595567867036011, 0.05706371191135734, 0.058171745152354584, 0.05983379501385042, 0.06094182825484765, 0.06149584487534626, 0.06371191135734072, 0.0659279778393352, 0.06703601108033241, 0.06703601108033241, 0.06869806094182826, 0.0703601108033241, 0.07257617728531855, 0.07368421052631578, 0.07534626038781164, 0.07700831024930747, 0.07867036011080333, 0.08033240997229918, 0.08199445983379502, 0.08310249307479226, 0.08476454293628809, 0.08642659279778395, 0.08642659279778395, 0.08642659279778395, 0.08642659279778395, 0.08698060941828255, 0.08864265927977841, 0.09085872576177284, 0.09141274238227146, 0.09252077562326869, 0.09252077562326869, 0.09252077562326869, 0.09362880886426592, 0.09584487534626039, 0.09750692520775624, 0.1002770083102493, 0.10193905817174516, 0.10249307479224376, 0.1030470914127424, 0.10470914127423825, 0.10637119113573408, 0.10747922437673132, 0.10803324099722993, 0.10914127423822714, 0.110803324099723, 0.11301939058171744, 0.11357340720221606, 0.1146814404432133, 0.11689750692520776, 0.11800554016620497, 0.12022160664819943, 0.1218836565096953, 0.12354570637119114, 0.12409972299168974, 0.12520775623268698, 0.12631578947368421, 0.12686980609418283, 0.12853185595567868, 0.12853185595567868, 0.12908587257617726, 0.1301939058171745, 0.1318559556786704, 0.132409972299169, 0.1329639889196676, 0.1329639889196676, 0.1335180055401662, 0.13518005540166206, 0.13628808864265926, 0.1379501385041551, 0.13850415512465375, 0.1407202216066482, 0.14182825484764544, 0.14293628808864267, 0.1440443213296399, 0.1451523545706371, 0.14681440443213298, 0.14681440443213298, 0.1473684210526316, 0.14847645429362882, 0.14958448753462605, 0.15013850415512467, 0.15124653739612187, 0.15290858725761775, 0.15401662049861495, 0.15512465373961218, 0.1556786703601108, 0.1556786703601108, 0.15678670360110802, 0.15734072022160664, 0.15789473684210525, 0.1584487534626039, 0.1590027700831025, 0.15955678670360113, 0.16011080332409972, 0.16121883656509697, 0.16232686980609418, 0.1628808864265928, 0.16343490304709146, 0.16509695290858728, 0.1662049861495845, 0.1662049861495845, 0.1662049861495845, 0.16786703601108033, 0.16897506925207756, 0.17008310249307476, 0.1717451523545706, 0.17229916897506922, 0.1728531855955679, 0.17340720221606648, 0.17451523545706374, 0.17617728531855956, 0.17673130193905817, 0.17673130193905817, 0.1778393351800554, 0.17950138504155125, 0.18005540166204986, 0.18005540166204986, 0.18116343490304707, 0.18116343490304707, 0.18227146814404432, 0.1828254847645429, 0.1828254847645429, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18337950138504153, 0.18393351800554014, 0.18448753462603878, 0.18614958448753463, 0.18781163434903045, 0.18947368421052632, 0.18947368421052632, 0.19002770083102494, 0.19113573407202217, 0.19168975069252076, 0.19279778393351804, 0.19335180055401663, 0.19390581717451524, 0.19501385041551247, 0.1966759002770083, 0.19778393351800555, 0.19778393351800555, 0.19833795013850417, 0.2, 0.20110803324099727, 0.20166204986149588, 0.20166204986149588, 0.20166204986149588, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.20221606648199447, 0.2033240997229917, 0.20443213296398893, 0.20443213296398893, 0.20498614958448752, 0.20609418282548475, 0.20664819944598337, 0.20664819944598337, 0.20664819944598337, 0.20775623268698062, 0.20775623268698062, 0.20831024930747924, 0.20941828254847644, 0.21052631578947367, 0.2116343490304709, 0.21218836565096955, 0.21274238227146816, 0.21329639889196678, 0.21385041551246536, 0.21440443213296398, 0.2149584487534626, 0.2155124653739612, 0.21662049861495847, 0.21662049861495847, 0.21662049861495847, 0.21717451523545708, 0.21717451523545708, 0.2177285318559557, 0.21828254847645429, 0.21994459833795013, 0.22049861495844877, 0.22049861495844877, 0.22105263157894733, 0.221606648199446, 0.2227146814404432, 0.22382271468144044, 0.22437673130193905, 0.22493074792243767, 0.22493074792243767, 0.22548476454293626, 0.22603878116343487, 0.2265927977839335, 0.22770083102493074, 0.22770083102493074, 0.22770083102493074, 0.22825484764542942, 0.22880886426592797, 0.22936288088642662, 0.22991689750692518, 0.23213296398891967, 0.2332409972299169, 0.23490304709141271, 0.23601108033240997, 0.2371191135734072, 0.2371191135734072, 0.23767313019390582, 0.23878116343490302, 0.23933518005540164, 0.23988919667590025, 0.23988919667590025, 0.23988919667590025, 0.2404432132963989, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24099722991689748, 0.24155124653739612, 0.24265927977839336, 0.24321329639889194, 0.24321329639889194, 0.24432132963988917, 0.24487534626038782, 0.24487534626038782, 0.24542936288088643, 0.24653739612188366, 0.24764542936288086, 0.24819944598337948, 0.24986149584487535, 0.25041551246537397, 0.25041551246537397, 0.25041551246537397, 0.25096952908587256, 0.2520775623268698, 0.25263157894736843, 0.253185595567867, 0.2548476454293629, 0.2554016620498615, 0.2565096952908587, 0.2581717451523546, 0.2581717451523546, 0.2587257617728532, 0.2592797783933518, 0.2592797783933518, 0.2598337950138504, 0.26038781163434904, 0.26094182825484763, 0.26204986149584486, 0.26260387811634345, 0.2642659279778393, 0.2648199445983379, 0.2659279778393352, 0.267590027700831, 0.267590027700831, 0.267590027700831, 0.26814404432132966, 0.26869806094182824, 0.2692520775623269, 0.2698060941828255, 0.2698060941828255, 0.2703601108033241, 0.2709141274238227, 0.2714681440443213, 0.27313019390581716, 0.2736842105263158, 0.2736842105263158, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.27479224376731304, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.2753462603878116, 0.27590027700831027, 0.27590027700831027, 0.27590027700831027, 0.27590027700831027, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.27645429362880886, 0.2770083102493075, 0.2775623268698061, 0.2775623268698061, 0.2775623268698061, 0.27811634349030473, 0.27811634349030473, 0.27811634349030473, 0.27922437673130196, 0.27922437673130196, 0.27922437673130196, 0.27922437673130196, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.2803324099722992, 0.28088642659279783, 0.28088642659279783, 0.2814404432132964, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.281994459833795, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28254847645429365, 0.28310249307479224, 0.28310249307479224, 0.28310249307479224, 0.2836565096952909, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28421052631578947, 0.28476454293628806, 0.28476454293628806, 0.28476454293628806, 0.28531855955678675, 0.28531855955678675, 0.28587257617728534, 0.28587257617728534, 0.28642659279778393, 0.28753462603878116, 0.289196675900277, 0.29030470914127426, 0.2914127423822715, 0.2925207756232687, 0.2936288088642659, 0.29418282548476454, 0.2952908587257618, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29695290858725765, 0.29750692520775623, 0.29861495844875346, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.2997229916897507, 0.30083102493074787, 0.30083102493074787, 0.3013850415512465, 0.30193905817174516, 0.30249307479224374, 0.3030470914127424, 0.303601108033241, 0.3041551246537396, 0.3047091412742382, 0.30526315789473685, 0.30526315789473685, 0.3058171745152355, 0.3063711911357341, 0.30692520775623267, 0.30858725761772854, 0.30969529085872577, 0.310803324099723, 0.3113573407202216, 0.31191135734072023, 0.31191135734072023, 0.31191135734072023, 0.3124653739612188, 0.31301939058171746, 0.31357340720221605, 0.31412742382271464, 0.31412742382271464, 0.31412742382271464, 0.31468144044321333, 0.31468144044321333, 0.31468144044321333, 0.3152354570637119, 0.3157894736842105, 0.3157894736842105, 0.31634349030470915, 0.3168975069252078, 0.3174515235457064, 0.318005540166205, 0.3185595567867036, 0.31911357340720226, 0.31966759002770084, 0.32022160664819943, 0.3207756232686981, 0.3213296398891967, 0.3213296398891967, 0.3213296398891967, 0.3218836565096953, 0.3229916897506925, 0.3240997229916897, 0.32520775623268694, 0.3263157894736842, 0.32742382271468146, 0.32742382271468146, 0.3279778393351801, 0.3285318559556787, 0.3285318559556787, 0.3290858725761773, 0.3290858725761773, 0.3296398891966759, 0.3301939058171745, 0.3318559556786703, 0.33240997229916897, 0.3329639889196676, 0.3346260387811634, 0.3346260387811634, 0.33518005540166207, 0.33573407202216066, 0.3362880886426593, 0.3362880886426593, 0.3362880886426593, 0.33684210526315794, 0.33739612188365653, 0.33850415512465376, 0.33905817174515235, 0.33905817174515235, 0.339612188365651, 0.34072022160664817, 0.34072022160664817, 0.34127423822714686, 0.3418282548476454, 0.3429362880886426, 0.3429362880886426, 0.3429362880886426, 0.34349030470914127, 0.3445983379501385, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34515235457063714, 0.34570637119113573, 0.34681440443213296, 0.34847645429362883, 0.3490304709141275, 0.3501385041551247, 0.3518005540166205, 0.35290858725761776, 0.35346260387811634, 0.3545706371191136, 0.3556786703601108, 0.35678670360110804, 0.3584487534626039, 0.3595567867036011, 0.36121883656509696, 0.3623268698060942, 0.3634349030470914, 0.3634349030470914, 0.36454293628808865, 0.3656509695290858, 0.3656509695290858, 0.36620498614958447, 0.3673130193905817, 0.36897506925207757, 0.3700831024930748, 0.3717451523545707, 0.3717451523545707, 0.37229916897506926, 0.3734072022160665, 0.37451523545706367, 0.37506925207756237, 0.3756232686980609, 0.37617728531855954, 0.37673130193905824, 0.37673130193905824, 0.37673130193905824, 0.37728531855955677, 0.378393351800554, 0.378393351800554, 0.37894736842105264, 0.37894736842105264, 0.3795013850415513, 0.38060941828254846, 0.3817174515235457, 0.38227146814404434, 0.3828254847645429, 0.38337950138504157, 0.38337950138504157, 0.38337950138504157, 0.3839335180055402, 0.3850415512465374, 0.385595567867036, 0.3861495844875346, 0.38670360110803326, 0.38670360110803326, 0.38725761772853184, 0.3878116343490305, 0.3878116343490305, 0.3883656509695291, 0.3883656509695291, 0.3894736842105263, 0.39058171745152354, 0.3911357340720222, 0.3922437673130194, 0.39279778393351805, 0.39279778393351805, 0.39279778393351805, 0.39279778393351805, 0.39335180055401664, 0.39445983379501387, 0.39501385041551246, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.3955678670360111, 0.39612188365650974, 0.397229916897507, 0.3977839335180055, 0.39833795013850415, 0.39889196675900274, 0.3994459833795014, 0.4, 0.4, 0.4, 0.4, 0.40055401662049855, 0.4011080332409972, 0.4016620498614959, 0.4022160664819944, 0.4022160664819944, 0.40277008310249307, 0.40332409972299166, 0.40332409972299166, 0.4044321329639889, 0.40498614958448753, 0.40498614958448753, 0.4055401662049861, 0.40609418282548476, 0.407202216066482, 0.40775623268698064, 0.40831024930747917, 0.40886426592797787, 0.40886426592797787, 0.4094182825484764, 0.40997229916897504, 0.40997229916897504, 0.41052631578947363, 0.41274238227146814, 0.4132963988919668, 0.414404432132964, 0.41551246537396125, 0.41606648199445984, 0.417174515235457, 0.417174515235457, 0.4177285318559557, 0.4177285318559557, 0.4177285318559557, 0.41828254847645424, 0.4188365650969529, 0.4199445983379501, 0.4204986149584487, 0.42105263157894735, 0.4221606648199446, 0.4221606648199446, 0.4227146814404432, 0.4232686980609418, 0.4254847645429363, 0.42603878116343485, 0.42659279778393344, 0.42659279778393344, 0.42659279778393344, 0.4271468144044322, 0.4277008310249307, 0.4282548476454293, 0.4282548476454293, 0.42880886426592796, 0.42880886426592796, 0.4293628808864266, 0.4299168975069252, 0.4299168975069252, 0.4310249307479224, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.43157894736842106, 0.4321329639889197, 0.4321329639889197, 0.4332409972299168, 0.4337950138504155, 0.43434903047091417, 0.4349030470914127, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.4354570637119113, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.43601108033240993, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.4365650969529085, 0.43711911357340716, 0.43711911357340716, 0.4376731301939058, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.4382271468144044, 0.43878116343490303, 0.4393351800554017, 0.4393351800554017, 0.4393351800554017, 0.43988919667590026, 0.4404432132963989, 0.4409972299168975, 0.44155124653739614, 0.44210526315789467, 0.44265927977839337, 0.44265927977839337, 0.44265927977839337, 0.443213296398892, 0.443213296398892, 0.443213296398892, 0.443213296398892, 0.44432132963988913, 0.4448753462603879, 0.445983379501385, 0.4470914127423823, 0.4476454293628809, 0.4476454293628809, 0.4481994459833795, 0.4487534626038781, 0.4487534626038781, 0.44986149584487534, 0.44986149584487534, 0.44986149584487534, 0.4509695290858726, 0.45207756232686985, 0.4526315789473684, 0.4537396121883656, 0.45429362880886426, 0.45429362880886426, 0.4554016620498615, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45595567867036013, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45650969529085883, 0.45706371191135736, 0.4581717451523545, 0.4581717451523545, 0.45872576177285324, 0.45872576177285324, 0.4592797783933518, 0.4592797783933518, 0.4592797783933518, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.45983379501385047, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.46038781163434905, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.4609418282548477, 0.46149584487534623, 0.4620498614958449, 0.46260387811634357, 0.46260387811634357, 0.4631578947368421, 0.46426592797783933, 0.46426592797783933, 0.464819944598338, 0.46537396121883656, 0.4659279778393352, 0.4664819944598338, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.46703601108033244, 0.467590027700831, 0.467590027700831, 0.46814404432132967, 0.4686980609418283, 0.4703601108033242, 0.4714681440443214, 0.47202216066481995, 0.47257617728531864, 0.47257617728531864, 0.47257617728531864, 0.47257617728531864, 0.4736842105263158, 0.47479224376731305, 0.4759002770083103, 0.47645429362880887, 0.4770083102493075, 0.4770083102493075, 0.4781163434903048, 0.4792243767313019, 0.4792243767313019, 0.4797783933518006, 0.4808864265927978, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.4814404432132964, 0.481994459833795, 0.4825484764542936, 0.4836565096952909, 0.4853185595567867, 0.4864265927977839, 0.4869806094182826, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.48864265927977846, 0.489196675900277, 0.489196675900277, 0.489196675900277, 0.489196675900277, 0.4903047091412742, 0.4914127423822715, 0.4919667590027701, 0.49252077562326874, 0.49252077562326874, 0.4930747922437673, 0.49418282548476455, 0.4947368421052632, 0.49529085872576173, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.4958448753462603, 0.49639889196675896, 0.4969529085872576, 0.4969529085872576, 0.4969529085872576, 0.4969529085872576, 0.4975069252077563, 0.4986149584487535, 0.4997229916897507, 0.5013850415512465, 0.5019390581717451, 0.5030470914127423, 0.5041551246537396, 0.5047091412742382, 0.5058171745152354, 0.5069252077562327, 0.5074792243767312, 0.50803324099723, 0.5091412742382271, 0.5096952908587257, 0.5102493074792245, 0.5102493074792245, 0.5102493074792245, 0.510803324099723, 0.510803324099723, 0.5119113573407201, 0.5119113573407201, 0.5124653739612188, 0.5141274238227147, 0.5152354570637119, 0.5157894736842106, 0.5174515235457064, 0.5191135734072022, 0.5207756232686981, 0.5207756232686981, 0.5218836565096953, 0.5235457063711911, 0.5246537396121884, 0.5257617728531856, 0.5268698060941829, 0.5274238227146815, 0.5279778393351802, 0.5285318559556786, 0.5290858725761773, 0.5301939058171745, 0.5313019390581717, 0.532409972299169, 0.5329639889196676, 0.5329639889196676, 0.5335180055401662, 0.5346260387811634, 0.5362880886426593, 0.5368421052631579, 0.5373961218836565, 0.5385041551246538, 0.5390581717451524, 0.539612188365651, 0.5407202216066482, 0.5429362880886427, 0.5445983379501385, 0.5457063711911357, 0.546814404432133, 0.5473684210526316, 0.5473684210526316, 0.5479224376731302, 0.5479224376731302, 0.5484764542936288, 0.5490304709141275, 0.5490304709141275, 0.5501385041551247, 0.5506925207756233, 0.5512465373961218, 0.5523545706371191, 0.5529085872576178, 0.554016620498615, 0.5551246537396122, 0.5556786703601108, 0.5562326869806093, 0.5573407202216066, 0.5578947368421052, 0.5584487534626038, 0.5595567867036011, 0.5595567867036011, 0.5601108033240998, 0.5606648199445983, 0.5606648199445983, 0.5617728531855957, 0.5623268698060941, 0.5628808864265927, 0.5628808864265927, 0.5628808864265927, 0.5634349030470913, 0.56398891966759, 0.5645429362880886, 0.5656509695290859, 0.5667590027700831, 0.5673130193905817, 0.5673130193905817, 0.5684210526315789, 0.5695290858725761, 0.571191135734072, 0.5722991689750693, 0.5728531855955679, 0.573961218836565, 0.5745152354570637, 0.575623268698061, 0.5767313019390581, 0.5772853185595569, 0.5778393351800555, 0.578393351800554, 0.5800554016620498, 0.5817174515235457, 0.5822714681440443, 0.5828254847645429, 0.5844875346260388, 0.585595567867036, 0.585595567867036, 0.585595567867036, 0.5861495844875346, 0.5872576177285318, 0.5872576177285318, 0.5878116343490305, 0.5889196675900277, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.5894736842105263, 0.590027700831025, 0.590027700831025, 0.5911357340720221, 0.5922437673130194, 0.592797783933518, 0.5939058171745153, 0.5939058171745153, 0.5939058171745153, 0.5950138504155125, 0.5950138504155125, 0.5950138504155125, 0.5950138504155125, 0.5955678670360111, 0.5961218836565096, 0.5966759002770083, 0.5972299168975069, 0.5983379501385042, 0.5988919667590029, 0.5994459833795014, 0.6, 0.6011080332409973, 0.6022160664819945, 0.6033240997229917, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989, 0.604432132963989], 'name': 'baseline', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}, {'x': [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0, 1000.0], 'y': [0.002770083102493075, 0.00554016620498615, 0.008310249307479225, 0.0110803324099723, 0.013850415512465374, 0.01662049861495845, 0.019390581717451522, 0.0221606648199446, 0.024930747922437674, 0.027700831024930747, 0.030470914127423823, 0.0332409972299169, 0.036011080332409975, 0.038781163434903045, 0.04155124653739612, 0.0443213296398892, 0.04709141274238227, 0.04986149584487535, 0.05263157894736842, 0.055401662049861494, 0.055401662049861494, 0.055401662049861494, 0.055401662049861494, 0.055401662049861494, 0.05817174515235457, 0.05817174515235457, 0.05817174515235457, 0.05817174515235457, 0.060941828254847646, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.06371191135734072, 0.0664819944598338, 0.06925207756232687, 0.07202216066481995, 0.07479224376731301, 0.07756232686980609, 0.08033240997229917, 0.08033240997229917, 0.08310249307479224, 0.08587257617728532, 0.0886426592797784, 0.09141274238227147, 0.09418282548476455, 0.09418282548476455, 0.09418282548476455, 0.09695290858725762, 0.0997229916897507, 0.0997229916897507, 0.0997229916897507, 0.0997229916897507, 0.0997229916897507, 0.0997229916897507, 0.10249307479224377, 0.10526315789473684, 0.10803324099722991, 0.10803324099722991, 0.10803324099722991, 0.11080332409972299, 0.11080332409972299, 0.11357340720221606, 0.11634349030470914, 0.11911357340720222, 0.12188365650969529, 0.12465373961218837, 0.12742382271468145, 0.13019390581717452, 0.1329639889196676, 0.1329639889196676, 0.13573407202216067, 0.13850415512465375, 0.13850415512465375, 0.14127423822714683, 0.14127423822714683, 0.1440443213296399, 0.1440443213296399, 0.14681440443213298, 0.14958448753462603, 0.14958448753462603, 0.1523545706371191, 0.1523545706371191, 0.15512465373961218, 0.15512465373961218, 0.15789473684210525, 0.15789473684210525, 0.16066481994459833, 0.16066481994459833, 0.1634349030470914, 0.1634349030470914, 0.16620498614958448, 0.16620498614958448, 0.16897506925207756, 0.16897506925207756, 0.17174515235457063, 0.17174515235457063, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1745152354570637, 0.1772853185595568, 0.18005540166204986, 0.18282548476454294, 0.18559556786703602, 0.1883656509695291, 0.19113573407202217, 0.19390581717451524, 0.19667590027700832, 0.1994459833795014, 0.20221606648199447, 0.20498614958448755, 0.2077562326869806, 0.21052631578947367, 0.21329639889196675, 0.21606648199445982, 0.2188365650969529, 0.22160664819944598, 0.22437673130193905, 0.22714681440443213, 0.22714681440443213, 0.22714681440443213, 0.22714681440443213, 0.22714681440443213, 0.22714681440443213, 0.22714681440443213, 0.22714681440443213, 0.2299168975069252, 0.2299168975069252, 0.2299168975069252, 0.2299168975069252, 0.2299168975069252, 0.23268698060941828, 0.23268698060941828, 0.23268698060941828, 0.23545706371191136, 0.23545706371191136, 0.23545706371191136, 0.23822714681440443, 0.2409972299168975, 0.24376731301939059, 0.24653739612188366, 0.24930747922437674, 0.2520775623268698, 0.2548476454293629, 0.25761772853185594, 0.26038781163434904, 0.2631578947368421, 0.2659279778393352, 0.26869806094182824, 0.27146814404432135, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2742382271468144, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.2770083102493075, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.27977839335180055, 0.28254847645429365, 0.28254847645429365, 0.2853185595567867, 0.2853185595567867, 0.2853185595567867, 0.2853185595567867, 0.2880886426592798, 0.2880886426592798, 0.2880886426592798, 0.2880886426592798, 0.2880886426592798, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29085872576177285, 0.29362880886426596, 0.296398891966759, 0.296398891966759, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.29916897506925205, 0.30193905817174516, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3047091412742382, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.3074792243767313, 0.31024930747922436, 0.31301939058171746, 0.3157894736842105, 0.3185595567867036, 0.32132963988919666, 0.32409972299168976, 0.3268698060941828, 0.3296398891966759, 0.3296398891966759, 0.3296398891966759, 0.33240997229916897, 0.33240997229916897, 0.33240997229916897, 0.33240997229916897, 0.33240997229916897, 0.33240997229916897, 0.33240997229916897, 0.33518005540166207, 0.33518005540166207, 0.33518005540166207, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3379501385041551, 0.3407202216066482, 0.3407202216066482, 0.3407202216066482, 0.3407202216066482, 0.3407202216066482, 0.3407202216066482, 0.3407202216066482, 0.3407202216066482, 0.34349030470914127, 0.3462603878116344, 0.3490304709141274, 0.3518005540166205, 0.3545706371191136, 0.3545706371191136, 0.3545706371191136, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3573407202216066, 0.3601108033240997, 0.3601108033240997, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3628808864265928, 0.3656509695290859, 0.3684210526315789, 0.37119113573407203, 0.3739612188365651, 0.3739612188365651, 0.3767313019390582, 0.3767313019390582, 0.3767313019390582, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.37950138504155123, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.38227146814404434, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3850415512465374, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.3878116343490305, 0.39058171745152354, 0.39058171745152354, 0.39058171745152354, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.39335180055401664, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3961218836565097, 0.3988919667590028, 0.3988919667590028, 0.3988919667590028, 0.3988919667590028, 0.3988919667590028, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40166204986149584, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.40443213296398894, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.407202216066482, 0.4099722991689751, 0.4099722991689751, 0.4099722991689751, 0.4099722991689751, 0.41274238227146814, 0.41274238227146814, 0.41274238227146814, 0.4155124653739612, 0.4155124653739612, 0.4155124653739612, 0.4155124653739612, 0.4155124653739612, 0.4182825484764543, 0.42105263157894735, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.42382271468144045, 0.4265927977839335, 0.4293628808864266, 0.43213296398891965, 0.43490304709141275, 0.4376731301939058, 0.4404432132963989, 0.44321329639889195, 0.44598337950138506, 0.4487534626038781, 0.4515235457063712, 0.45429362880886426, 0.45706371191135736, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4598337950138504, 0.4626038781163435, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46537396121883656, 0.46814404432132967, 0.46814404432132967, 0.46814404432132967, 0.46814404432132967, 0.46814404432132967, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.4709141274238227, 0.47368421052631576, 0.47645429362880887, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.4792243767313019, 0.481994459833795, 0.481994459833795, 0.481994459833795, 0.481994459833795, 0.481994459833795, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48476454293628807, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.48753462603878117, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4903047091412742, 0.4930747922437673, 0.4930747922437673, 0.4930747922437673, 0.49584487534626037, 0.4986149584487535, 0.5013850415512465, 0.5041551246537396, 0.5069252077562327, 0.5096952908587258, 0.5124653739612188, 0.5152354570637119, 0.518005540166205, 0.518005540166205, 0.5207756232686981, 0.5235457063711911, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5263157894736842, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5290858725761773, 0.5318559556786704, 0.5318559556786704, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5346260387811634, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5373961218836565, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5401662049861495, 0.5429362880886427, 0.5457063711911357, 0.5484764542936288, 0.5512465373961218, 0.554016620498615, 0.556786703601108, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5595567867036011, 0.5623268698060941, 0.5650969529085873, 0.5678670360110804, 0.5706371191135734, 0.5734072022160664, 0.5734072022160664, 0.5734072022160664, 0.5734072022160664, 0.5761772853185596, 0.5761772853185596, 0.5761772853185596, 0.5761772853185596, 0.5761772853185596, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5789473684210527, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5817174515235457, 0.5844875346260388, 0.5844875346260388, 0.5844875346260388, 0.5844875346260388, 0.5844875346260388, 0.5844875346260388], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n",
      "Average exploration factor over 1 episodes [{'x': [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0, 82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0, 125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0, 136.0, 137.0, 138.0, 139.0, 140.0, 141.0, 142.0, 143.0, 144.0, 145.0, 146.0, 147.0, 148.0, 149.0, 150.0, 151.0, 152.0, 153.0, 154.0, 155.0, 156.0, 157.0, 158.0, 159.0, 160.0, 161.0, 162.0, 163.0, 164.0, 165.0, 166.0, 167.0, 168.0, 169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0, 181.0, 182.0, 183.0, 184.0, 185.0, 186.0, 187.0, 188.0, 189.0, 190.0, 191.0, 192.0, 193.0, 194.0, 195.0, 196.0, 197.0, 198.0, 199.0, 200.0, 201.0, 202.0, 203.0, 204.0, 205.0, 206.0, 207.0, 208.0, 209.0, 210.0, 211.0, 212.0, 213.0, 214.0, 215.0, 216.0, 217.0, 218.0, 219.0, 220.0, 221.0, 222.0, 223.0, 224.0, 225.0, 226.0, 227.0, 228.0, 229.0, 230.0, 231.0, 232.0, 233.0, 234.0, 235.0, 236.0, 237.0, 238.0, 239.0, 240.0, 241.0, 242.0, 243.0, 244.0, 245.0, 246.0, 247.0, 248.0, 249.0, 250.0, 251.0, 252.0, 253.0, 254.0, 255.0, 256.0, 257.0, 258.0, 259.0, 260.0, 261.0, 262.0, 263.0, 264.0, 265.0, 266.0, 267.0, 268.0, 269.0, 270.0, 271.0, 272.0, 273.0, 274.0, 275.0, 276.0, 277.0, 278.0, 279.0, 280.0, 281.0, 282.0, 283.0, 284.0, 285.0, 286.0, 287.0, 288.0, 289.0, 290.0, 291.0, 292.0, 293.0, 294.0, 295.0, 296.0, 297.0, 298.0, 299.0, 300.0, 301.0, 302.0, 303.0, 304.0, 305.0, 306.0, 307.0, 308.0, 309.0, 310.0, 311.0, 312.0, 313.0, 314.0, 315.0, 316.0, 317.0, 318.0, 319.0, 320.0, 321.0, 322.0, 323.0, 324.0, 325.0, 326.0, 327.0, 328.0, 329.0, 330.0, 331.0, 332.0, 333.0, 334.0, 335.0, 336.0, 337.0, 338.0, 339.0, 340.0, 341.0, 342.0, 343.0, 344.0, 345.0, 346.0, 347.0, 348.0, 349.0, 350.0, 351.0, 352.0, 353.0, 354.0, 355.0, 356.0, 357.0, 358.0, 359.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 367.0, 368.0, 369.0, 370.0, 371.0, 372.0, 373.0, 374.0, 375.0, 376.0, 377.0, 378.0, 379.0, 380.0, 381.0, 382.0, 383.0, 384.0, 385.0, 386.0, 387.0, 388.0, 389.0, 390.0, 391.0, 392.0, 393.0, 394.0, 395.0, 396.0, 397.0, 398.0, 399.0, 400.0, 401.0, 402.0, 403.0, 404.0, 405.0, 406.0, 407.0, 408.0, 409.0, 410.0, 411.0, 412.0, 413.0, 414.0, 415.0, 416.0, 417.0, 418.0, 419.0, 420.0, 421.0, 422.0, 423.0, 424.0, 425.0, 426.0, 427.0, 428.0, 429.0, 430.0, 431.0, 432.0, 433.0, 434.0, 435.0, 436.0, 437.0, 438.0, 439.0, 440.0, 441.0, 442.0, 443.0, 444.0, 445.0, 446.0, 447.0, 448.0, 449.0, 450.0, 451.0, 452.0, 453.0, 454.0, 455.0, 456.0, 457.0, 458.0, 459.0, 460.0, 461.0, 462.0, 463.0, 464.0, 465.0, 466.0, 467.0, 468.0, 469.0, 470.0, 471.0, 472.0, 473.0, 474.0, 475.0, 476.0, 477.0, 478.0, 479.0, 480.0, 481.0, 482.0, 483.0, 484.0, 485.0, 486.0, 487.0, 488.0, 489.0, 490.0, 491.0, 492.0, 493.0, 494.0, 495.0, 496.0, 497.0, 498.0, 499.0, 500.0, 501.0, 502.0, 503.0, 504.0, 505.0, 506.0, 507.0, 508.0, 509.0, 510.0, 511.0, 512.0, 513.0, 514.0, 515.0, 516.0, 517.0, 518.0, 519.0, 520.0, 521.0, 522.0, 523.0, 524.0, 525.0, 526.0, 527.0, 528.0, 529.0, 530.0, 531.0, 532.0, 533.0, 534.0, 535.0, 536.0, 537.0, 538.0, 539.0, 540.0, 541.0, 542.0, 543.0, 544.0, 545.0, 546.0, 547.0, 548.0, 549.0, 550.0, 551.0, 552.0, 553.0, 554.0, 555.0, 556.0, 557.0, 558.0, 559.0, 560.0, 561.0, 562.0, 563.0, 564.0, 565.0, 566.0, 567.0, 568.0, 569.0, 570.0, 571.0, 572.0, 573.0, 574.0, 575.0, 576.0, 577.0, 578.0, 579.0, 580.0, 581.0, 582.0, 583.0, 584.0, 585.0, 586.0, 587.0, 588.0, 589.0, 590.0, 591.0, 592.0, 593.0, 594.0, 595.0, 596.0, 597.0, 598.0, 599.0, 600.0, 601.0, 602.0, 603.0, 604.0, 605.0, 606.0, 607.0, 608.0, 609.0, 610.0, 611.0, 612.0, 613.0, 614.0, 615.0, 616.0, 617.0, 618.0, 619.0, 620.0, 621.0, 622.0, 623.0, 624.0, 625.0, 626.0, 627.0, 628.0, 629.0, 630.0, 631.0, 632.0, 633.0, 634.0, 635.0, 636.0, 637.0, 638.0, 639.0, 640.0, 641.0, 642.0, 643.0, 644.0, 645.0, 646.0, 647.0, 648.0, 649.0, 650.0, 651.0, 652.0, 653.0, 654.0, 655.0, 656.0, 657.0, 658.0, 659.0, 660.0, 661.0, 662.0, 663.0, 664.0, 665.0, 666.0, 667.0, 668.0, 669.0, 670.0, 671.0, 672.0, 673.0, 674.0, 675.0, 676.0, 677.0, 678.0, 679.0, 680.0, 681.0, 682.0, 683.0, 684.0, 685.0, 686.0, 687.0, 688.0, 689.0, 690.0, 691.0, 692.0, 693.0, 694.0, 695.0, 696.0, 697.0, 698.0, 699.0, 700.0, 701.0, 702.0, 703.0, 704.0, 705.0, 706.0, 707.0, 708.0, 709.0, 710.0, 711.0, 712.0, 713.0, 714.0, 715.0, 716.0, 717.0, 718.0, 719.0, 720.0, 721.0, 722.0, 723.0, 724.0, 725.0, 726.0, 727.0, 728.0, 729.0, 730.0, 731.0, 732.0, 733.0, 734.0, 735.0, 736.0, 737.0, 738.0, 739.0, 740.0, 741.0, 742.0, 743.0, 744.0, 745.0, 746.0, 747.0, 748.0, 749.0, 750.0, 751.0, 752.0, 753.0, 754.0, 755.0, 756.0, 757.0, 758.0, 759.0, 760.0, 761.0, 762.0, 763.0, 764.0, 765.0, 766.0, 767.0, 768.0, 769.0, 770.0, 771.0, 772.0, 773.0, 774.0, 775.0, 776.0, 777.0, 778.0, 779.0, 780.0, 781.0, 782.0, 783.0, 784.0, 785.0, 786.0, 787.0, 788.0, 789.0, 790.0, 791.0, 792.0, 793.0, 794.0, 795.0, 796.0, 797.0, 798.0, 799.0, 800.0, 801.0, 802.0, 803.0, 804.0, 805.0, 806.0, 807.0, 808.0, 809.0, 810.0, 811.0, 812.0, 813.0, 814.0, 815.0, 816.0, 817.0, 818.0, 819.0, 820.0, 821.0, 822.0, 823.0, 824.0, 825.0, 826.0, 827.0, 828.0, 829.0, 830.0, 831.0, 832.0, 833.0, 834.0, 835.0, 836.0, 837.0, 838.0, 839.0, 840.0, 841.0, 842.0, 843.0, 844.0, 845.0, 846.0, 847.0, 848.0, 849.0, 850.0, 851.0, 852.0, 853.0, 854.0, 855.0, 856.0, 857.0, 858.0, 859.0, 860.0, 861.0, 862.0, 863.0, 864.0, 865.0, 866.0, 867.0, 868.0, 869.0, 870.0, 871.0, 872.0, 873.0, 874.0, 875.0, 876.0, 877.0, 878.0, 879.0, 880.0, 881.0, 882.0, 883.0, 884.0, 885.0, 886.0, 887.0, 888.0, 889.0, 890.0, 891.0, 892.0, 893.0, 894.0, 895.0, 896.0, 897.0, 898.0, 899.0, 900.0, 901.0, 902.0, 903.0, 904.0, 905.0, 906.0, 907.0, 908.0, 909.0, 910.0, 911.0, 912.0, 913.0, 914.0, 915.0, 916.0, 917.0, 918.0, 919.0, 920.0, 921.0, 922.0, 923.0, 924.0, 925.0, 926.0, 927.0, 928.0, 929.0, 930.0, 931.0, 932.0, 933.0, 934.0, 935.0, 936.0, 937.0, 938.0, 939.0, 940.0, 941.0, 942.0, 943.0, 944.0, 945.0, 946.0, 947.0, 948.0, 949.0, 950.0, 951.0, 952.0, 953.0, 954.0, 955.0, 956.0, 957.0, 958.0, 959.0, 960.0, 961.0, 962.0, 963.0, 964.0, 965.0, 966.0, 967.0, 968.0, 969.0, 970.0, 971.0, 972.0, 973.0, 974.0, 975.0, 976.0, 977.0, 978.0, 979.0, 980.0, 981.0, 982.0, 983.0, 984.0, 985.0, 986.0, 987.0, 988.0, 989.0, 990.0, 991.0, 992.0, 993.0, 994.0, 995.0, 996.0, 997.0, 998.0, 999.0], 'y': [1.0, 1.0, 0.6666666666666666, 0.75, 0.8, 0.6666666666666666, 0.5714285714285714, 0.625, 0.6666666666666666, 0.7, 0.7272727272727273, 0.75, 0.7692307692307693, 0.7857142857142857, 0.8, 0.75, 0.7058823529411765, 0.6666666666666666, 0.631578947368421, 0.6, 0.5714285714285714, 0.5454545454545454, 0.5217391304347826, 0.5, 0.48, 0.5, 0.5185185185185185, 0.5, 0.4827586206896552, 0.4666666666666667, 0.45161290322580644, 0.4375, 0.42424242424242425, 0.4117647058823529, 0.4, 0.3888888888888889, 0.3783783783783784, 0.3684210526315789, 0.38461538461538464, 0.4, 0.3902439024390244, 0.40476190476190477, 0.3953488372093023, 0.4090909090909091, 0.4222222222222222, 0.41304347826086957, 0.40425531914893614, 0.3958333333333333, 0.3877551020408163, 0.38, 0.37254901960784315, 0.36538461538461536, 0.3584905660377358, 0.35185185185185186, 0.34545454545454546, 0.3392857142857143, 0.3333333333333333, 0.3275862068965517, 0.3220338983050847, 0.3333333333333333, 0.3442622950819672, 0.3548387096774194, 0.36507936507936506, 0.375, 0.36923076923076925, 0.36363636363636365, 0.3582089552238806, 0.35294117647058826, 0.34782608695652173, 0.35714285714285715, 0.36619718309859156, 0.3611111111111111, 0.3561643835616438, 0.35135135135135137, 0.3466666666666667, 0.35526315789473684, 0.35064935064935066, 0.34615384615384615, 0.34177215189873417, 0.3375, 0.3333333333333333, 0.32926829268292684, 0.3253012048192771, 0.3333333333333333, 0.3411764705882353, 0.3372093023255814, 0.3333333333333333, 0.3409090909090909, 0.34831460674157305, 0.35555555555555557, 0.3626373626373626, 0.3695652173913043, 0.3763440860215054, 0.3829787234042553, 0.3894736842105263, 0.3958333333333333, 0.3917525773195876, 0.3877551020408163, 0.3838383838383838, 0.39, 0.39603960396039606, 0.4019607843137255, 0.4077669902912621, 0.41346153846153844, 0.41904761904761906, 0.42452830188679247, 0.42990654205607476, 0.42592592592592593, 0.42201834862385323, 0.41818181818181815, 0.4144144144144144, 0.4107142857142857, 0.40707964601769914, 0.41228070175438597, 0.40869565217391307, 0.4051724137931034, 0.4017094017094017, 0.3983050847457627, 0.40336134453781514, 0.4, 0.4049586776859504, 0.4016393442622951, 0.4065040650406504, 0.4112903225806452, 0.416, 0.42063492063492064, 0.4251968503937008, 0.4296875, 0.43410852713178294, 0.4307692307692308, 0.42748091603053434, 0.42424242424242425, 0.42105263157894735, 0.417910447761194, 0.4148148148148148, 0.41911764705882354, 0.41605839416058393, 0.42028985507246375, 0.4172661870503597, 0.4142857142857143, 0.41134751773049644, 0.4084507042253521, 0.40559440559440557, 0.4027777777777778, 0.4, 0.4041095890410959, 0.4013605442176871, 0.39864864864864863, 0.3959731543624161, 0.3933333333333333, 0.39072847682119205, 0.3881578947368421, 0.38562091503267976, 0.38311688311688313, 0.38064516129032255, 0.3782051282051282, 0.37579617834394907, 0.37341772151898733, 0.3710691823899371, 0.36875, 0.36645962732919257, 0.36419753086419754, 0.3619631901840491, 0.3597560975609756, 0.3575757575757576, 0.35542168674698793, 0.3532934131736527, 0.35714285714285715, 0.3609467455621302, 0.36470588235294116, 0.3684210526315789, 0.37209302325581395, 0.3699421965317919, 0.367816091954023, 0.3657142857142857, 0.36363636363636365, 0.3615819209039548, 0.3595505617977528, 0.3575418994413408, 0.35555555555555557, 0.35359116022099446, 0.3516483516483517, 0.34972677595628415, 0.34782608695652173, 0.34594594594594597, 0.34408602150537637, 0.3422459893048128, 0.3404255319148936, 0.3386243386243386, 0.3368421052631579, 0.33507853403141363, 0.3333333333333333, 0.3316062176165803, 0.32989690721649484, 0.3282051282051282, 0.32653061224489793, 0.3248730964467005, 0.32323232323232326, 0.32160804020100503, 0.32, 0.31840796019900497, 0.31683168316831684, 0.31527093596059114, 0.3137254901960784, 0.3121951219512195, 0.3106796116504854, 0.30917874396135264, 0.3076923076923077, 0.3062200956937799, 0.3047619047619048, 0.3033175355450237, 0.3018867924528302, 0.3004694835680751, 0.29906542056074764, 0.29767441860465116, 0.2962962962962963, 0.29493087557603687, 0.29357798165137616, 0.2922374429223744, 0.2909090909090909, 0.29411764705882354, 0.2972972972972973, 0.3004484304932735, 0.30357142857142855, 0.30666666666666664, 0.30973451327433627, 0.31277533039647576, 0.3157894736842105, 0.31877729257641924, 0.3217391304347826, 0.3203463203463203, 0.31896551724137934, 0.31759656652360513, 0.3162393162393162, 0.3191489361702128, 0.3220338983050847, 0.3206751054852321, 0.3235294117647059, 0.3263598326359833, 0.32916666666666666, 0.33195020746887965, 0.3347107438016529, 0.3333333333333333, 0.3319672131147541, 0.3306122448979592, 0.3333333333333333, 0.3319838056680162, 0.33064516129032256, 0.3293172690763052, 0.328, 0.32669322709163345, 0.3253968253968254, 0.3241106719367589, 0.32677165354330706, 0.3254901960784314, 0.32421875, 0.3229571984435798, 0.32558139534883723, 0.32432432432432434, 0.3230769230769231, 0.3218390804597701, 0.32061068702290074, 0.3231939163498099, 0.32575757575757575, 0.3283018867924528, 0.3308270676691729, 0.3333333333333333, 0.3358208955223881, 0.3345724907063197, 0.3333333333333333, 0.33210332103321033, 0.33088235294117646, 0.32967032967032966, 0.3284671532846715, 0.32727272727272727, 0.32971014492753625, 0.3285198555956679, 0.3273381294964029, 0.32616487455197135, 0.325, 0.3274021352313167, 0.32978723404255317, 0.3321554770318021, 0.33098591549295775, 0.3298245614035088, 0.32867132867132864, 0.32752613240418116, 0.3263888888888889, 0.32525951557093424, 0.32413793103448274, 0.32646048109965636, 0.3253424657534247, 0.3242320819112628, 0.32653061224489793, 0.3254237288135593, 0.32432432432432434, 0.32323232323232326, 0.3221476510067114, 0.3210702341137124, 0.32, 0.31893687707641194, 0.31788079470198677, 0.3201320132013201, 0.3223684210526316, 0.32131147540983607, 0.3202614379084967, 0.31921824104234525, 0.3181818181818182, 0.32038834951456313, 0.3225806451612903, 0.3247588424437299, 0.3269230769230769, 0.3258785942492013, 0.3248407643312102, 0.3238095238095238, 0.3259493670886076, 0.3249211356466877, 0.3238993710691824, 0.322884012539185, 0.321875, 0.32087227414330216, 0.3198757763975155, 0.3188854489164087, 0.31790123456790126, 0.3169230769230769, 0.3159509202453988, 0.3149847094801223, 0.31402439024390244, 0.3130699088145897, 0.31212121212121213, 0.311178247734139, 0.3102409638554217, 0.30930930930930933, 0.3083832335329341, 0.3074626865671642, 0.30654761904761907, 0.3056379821958457, 0.3047337278106509, 0.30383480825958703, 0.3058823529411765, 0.30498533724340177, 0.30409356725146197, 0.3032069970845481, 0.3023255813953488, 0.30144927536231886, 0.30057803468208094, 0.29971181556195964, 0.2988505747126437, 0.2979942693409742, 0.29714285714285715, 0.2962962962962963, 0.29545454545454547, 0.29461756373937675, 0.2937853107344633, 0.29295774647887324, 0.29213483146067415, 0.2913165266106443, 0.2905027932960894, 0.28969359331476324, 0.28888888888888886, 0.29085872576177285, 0.2900552486187845, 0.2892561983471074, 0.29120879120879123, 0.29041095890410956, 0.2923497267759563, 0.29427792915531337, 0.296195652173913, 0.2981029810298103, 0.2972972972972973, 0.29649595687331537, 0.2956989247311828, 0.2975871313672922, 0.2967914438502674, 0.296, 0.29521276595744683, 0.29442970822281167, 0.2962962962962963, 0.29815303430079154, 0.3, 0.2992125984251969, 0.29842931937172773, 0.29765013054830286, 0.296875, 0.2961038961038961, 0.29533678756476683, 0.29457364341085274, 0.2963917525773196, 0.29562982005141386, 0.2948717948717949, 0.29411764705882354, 0.29336734693877553, 0.2926208651399491, 0.2918781725888325, 0.2911392405063291, 0.2904040404040404, 0.29219143576826195, 0.29396984924623115, 0.2957393483709273, 0.2975, 0.29925187032418954, 0.3009950248756219, 0.3002481389578164, 0.2995049504950495, 0.29876543209876544, 0.30049261083743845, 0.29975429975429974, 0.29901960784313725, 0.2982885085574572, 0.2975609756097561, 0.29683698296836986, 0.2961165048543689, 0.29539951573849876, 0.2946859903381642, 0.29397590361445786, 0.2932692307692308, 0.29256594724220625, 0.291866028708134, 0.2911694510739857, 0.29285714285714287, 0.2921615201900237, 0.2938388625592417, 0.29550827423167847, 0.2971698113207547, 0.2988235294117647, 0.3004694835680751, 0.2997658079625293, 0.3014018691588785, 0.30303030303030304, 0.30465116279069765, 0.3062645011600928, 0.30787037037037035, 0.3071593533487298, 0.3064516129032258, 0.3057471264367816, 0.30504587155963303, 0.30434782608695654, 0.3036529680365297, 0.30296127562642367, 0.30227272727272725, 0.30385487528344673, 0.3031674208144796, 0.30248306997742663, 0.30180180180180183, 0.30337078651685395, 0.30493273542600896, 0.30648769574944074, 0.3080357142857143, 0.30957683741648107, 0.3088888888888889, 0.3082039911308204, 0.30973451327433627, 0.31125827814569534, 0.31057268722466963, 0.3098901098901099, 0.3092105263157895, 0.3085339168490153, 0.3078602620087336, 0.3093681917211329, 0.30869565217391304, 0.3080260303687636, 0.30952380952380953, 0.30885529157667385, 0.3081896551724138, 0.30752688172043013, 0.30686695278969955, 0.30620985010706636, 0.3055555555555556, 0.3049040511727079, 0.30638297872340425, 0.3057324840764331, 0.3050847457627119, 0.3044397463002114, 0.3037974683544304, 0.3031578947368421, 0.3025210084033613, 0.3018867924528302, 0.301255230125523, 0.30062630480167013, 0.3, 0.2993762993762994, 0.2987551867219917, 0.2981366459627329, 0.2975206611570248, 0.29690721649484536, 0.2962962962962963, 0.29568788501026694, 0.29508196721311475, 0.294478527607362, 0.2938775510204082, 0.29327902240325865, 0.2926829268292683, 0.2920892494929006, 0.291497975708502, 0.2909090909090909, 0.2903225806451613, 0.289738430583501, 0.2891566265060241, 0.28857715430861725, 0.288, 0.2874251497005988, 0.2868525896414343, 0.28628230616302186, 0.2857142857142857, 0.2871287128712871, 0.2865612648221344, 0.2859960552268245, 0.2854330708661417, 0.28487229862475444, 0.28431372549019607, 0.2837573385518591, 0.28515625, 0.28460038986354774, 0.2840466926070039, 0.283495145631068, 0.28294573643410853, 0.28239845261121854, 0.28185328185328185, 0.2813102119460501, 0.28076923076923077, 0.2802303262955854, 0.2796934865900383, 0.27915869980879543, 0.2786259541984733, 0.28, 0.279467680608365, 0.27893738140417457, 0.2803030303030303, 0.28166351606805295, 0.2830188679245283, 0.2843691148775895, 0.2857142857142857, 0.2870544090056285, 0.2883895131086142, 0.2897196261682243, 0.291044776119403, 0.29236499068901306, 0.29182156133828996, 0.2912801484230056, 0.29074074074074074, 0.2902033271719039, 0.2896678966789668, 0.289134438305709, 0.28860294117647056, 0.28807339449541286, 0.2875457875457875, 0.2870201096892139, 0.2864963503649635, 0.2859744990892532, 0.28545454545454546, 0.2867513611615245, 0.286231884057971, 0.2857142857142857, 0.2851985559566787, 0.28468468468468466, 0.2841726618705036, 0.28545780969479356, 0.2849462365591398, 0.2844364937388193, 0.2839285714285714, 0.28342245989304815, 0.28291814946619215, 0.2824156305506217, 0.28191489361702127, 0.2814159292035398, 0.28091872791519434, 0.2804232804232804, 0.27992957746478875, 0.281195079086116, 0.2824561403508772, 0.28371278458844135, 0.28321678321678323, 0.28272251308900526, 0.28222996515679444, 0.2817391304347826, 0.28125, 0.2807625649913345, 0.28027681660899656, 0.27979274611398963, 0.2793103448275862, 0.27882960413080893, 0.27835051546391754, 0.27787307032590053, 0.2773972602739726, 0.27692307692307694, 0.2764505119453925, 0.27597955706984667, 0.2755102040816326, 0.27504244482173174, 0.2745762711864407, 0.27411167512690354, 0.27533783783783783, 0.2765598650927487, 0.2760942760942761, 0.27563025210084036, 0.2751677852348993, 0.2747068676716918, 0.27424749163879597, 0.27545909849749584, 0.275, 0.27454242928452577, 0.27408637873754155, 0.2736318407960199, 0.2731788079470199, 0.2727272727272727, 0.2722772277227723, 0.27182866556836904, 0.2713815789473684, 0.270935960591133, 0.27049180327868855, 0.2700490998363339, 0.2696078431372549, 0.26916802610114193, 0.2687296416938111, 0.2682926829268293, 0.26785714285714285, 0.26742301458670986, 0.2669902912621359, 0.2665589660743134, 0.2661290322580645, 0.26570048309178745, 0.2652733118971061, 0.26484751203852325, 0.2644230769230769, 0.264, 0.26357827476038337, 0.2631578947368421, 0.2627388535031847, 0.26232114467408585, 0.2619047619047619, 0.26148969889064977, 0.2610759493670886, 0.26066350710900477, 0.26025236593059936, 0.25984251968503935, 0.25943396226415094, 0.25902668759811615, 0.25862068965517243, 0.25821596244131456, 0.2578125, 0.2574102964118565, 0.2570093457943925, 0.25660964230171074, 0.2562111801242236, 0.2558139534883721, 0.25541795665634676, 0.2550231839258114, 0.25462962962962965, 0.2542372881355932, 0.25384615384615383, 0.2534562211981567, 0.25306748466257667, 0.25267993874425726, 0.25229357798165136, 0.25190839694656486, 0.25152439024390244, 0.2511415525114155, 0.2507598784194529, 0.2503793626707132, 0.25, 0.24962178517397882, 0.24924471299093656, 0.248868778280543, 0.24849397590361447, 0.24962406015037594, 0.24924924924924924, 0.25037481259370314, 0.25149700598802394, 0.25112107623318386, 0.25223880597014925, 0.2533532041728763, 0.2544642857142857, 0.2555720653789004, 0.2551928783382789, 0.2562962962962963, 0.257396449704142, 0.2570162481536189, 0.25663716814159293, 0.25625920471281294, 0.25588235294117645, 0.2555066079295154, 0.2565982404692082, 0.25768667642752563, 0.25877192982456143, 0.25985401459854013, 0.260932944606414, 0.26055312954876275, 0.2601744186046512, 0.2597968069666183, 0.2594202898550725, 0.2590448625180897, 0.2586705202312139, 0.2597402597402597, 0.260806916426513, 0.26187050359712233, 0.2614942528735632, 0.2611190817790531, 0.2607449856733524, 0.2603719599427754, 0.26142857142857145, 0.26105563480741795, 0.2606837606837607, 0.2603129445234708, 0.2599431818181818, 0.25957446808510637, 0.26062322946175637, 0.26025459688826025, 0.2598870056497175, 0.25952045133991536, 0.2591549295774648, 0.2587904360056259, 0.25842696629213485, 0.25806451612903225, 0.25770308123249297, 0.2573426573426573, 0.25837988826815644, 0.2580195258019526, 0.2576601671309192, 0.25869262865090403, 0.25833333333333336, 0.2579750346740638, 0.25761772853185594, 0.2572614107883817, 0.2569060773480663, 0.25655172413793104, 0.256198347107438, 0.2558459422283356, 0.2554945054945055, 0.25651577503429357, 0.25753424657534246, 0.25718194254445964, 0.2568306010928962, 0.25648021828103684, 0.2561307901907357, 0.25578231292517006, 0.2554347826086957, 0.25508819538670285, 0.25609756097560976, 0.2571041948579161, 0.2581081081081081, 0.25775978407557354, 0.2574123989218329, 0.2570659488559892, 0.2567204301075269, 0.2563758389261745, 0.25603217158176944, 0.2556894243641232, 0.2553475935828877, 0.25500667556742324, 0.25466666666666665, 0.25432756324900135, 0.25398936170212766, 0.2549800796812749, 0.25596816976127323, 0.25695364238410595, 0.2566137566137566, 0.2562747688243065, 0.2559366754617414, 0.25559947299077734, 0.25526315789473686, 0.25492772667542707, 0.2545931758530184, 0.254259501965924, 0.25392670157068065, 0.25359477124183005, 0.25326370757180156, 0.2529335071707953, 0.2526041666666667, 0.25227568270481143, 0.2519480519480519, 0.251621271076524, 0.25129533678756477, 0.25097024579560157, 0.25064599483204136, 0.2503225806451613, 0.25, 0.24967824967824967, 0.2493573264781491, 0.2490372272143774, 0.24871794871794872, 0.24839948783610755, 0.24808184143222506, 0.24776500638569604, 0.24744897959183673, 0.24713375796178344, 0.24681933842239187, 0.24650571791613723, 0.24619289340101522, 0.2458808618504436, 0.24556962025316456, 0.24525916561314792, 0.24494949494949494, 0.244640605296343, 0.24433249370277077, 0.2440251572327044, 0.24371859296482412, 0.24341279799247176, 0.24310776942355888, 0.2428035043804756, 0.2425, 0.2421972534332085, 0.24189526184538654, 0.24159402241594022, 0.24129353233830847, 0.24099378881987576, 0.24069478908188585, 0.24039653035935563, 0.2400990099009901, 0.23980222496909764, 0.23950617283950618, 0.23921085080147966, 0.23891625615763548, 0.23862238622386223, 0.23955773955773957, 0.2392638036809816, 0.23897058823529413, 0.2386780905752754, 0.23838630806845965, 0.23809523809523808, 0.23780487804878048, 0.23751522533495736, 0.23722627737226276, 0.23693803159173754, 0.2366504854368932, 0.23636363636363636, 0.2360774818401937, 0.23579201934703747, 0.23550724637681159, 0.23522316043425814, 0.23493975903614459, 0.23465703971119134, 0.234375, 0.234093637454982, 0.23381294964028776, 0.23353293413173654, 0.2332535885167464, 0.23297491039426524, 0.23269689737470167, 0.232419547079857, 0.23333333333333334, 0.23305588585017836, 0.2327790973871734, 0.232502965599051, 0.23222748815165878, 0.2319526627218935, 0.23167848699763594, 0.23140495867768596, 0.23113207547169812, 0.2308598351001178, 0.23176470588235293, 0.23266745005875442, 0.2323943661971831, 0.2321219226260258, 0.23185011709601874, 0.23157894736842105, 0.23130841121495327, 0.2310385064177363, 0.23076923076923078, 0.23050058207217694, 0.2302325581395349, 0.22996515679442509, 0.2296983758700696, 0.22943221320973348, 0.22916666666666666, 0.22890173410404624, 0.22863741339491916, 0.22837370242214533, 0.22811059907834103, 0.22784810126582278, 0.22758620689655173, 0.22732491389207807, 0.22706422018348624, 0.2268041237113402, 0.22654462242562928, 0.22628571428571428, 0.22602739726027396, 0.22576966932725198, 0.2255125284738041, 0.22525597269624573, 0.225, 0.22474460839954596, 0.22448979591836735, 0.22423556058890148, 0.2239819004524887, 0.22372881355932203, 0.2234762979683973, 0.22322435174746336, 0.22297297297297297, 0.22272215973003376, 0.22247191011235956, 0.2222222222222222, 0.2219730941704036, 0.22172452407614782, 0.2214765100671141, 0.2212290502793296, 0.22098214285714285, 0.22073578595317725, 0.22048997772828507, 0.22024471635150167, 0.22, 0.21975582685904552, 0.21951219512195122, 0.21926910299003322, 0.21902654867256638, 0.21878453038674034, 0.2196467991169978, 0.2205071664829107, 0.22026431718061673, 0.22002200220022003, 0.21978021978021978, 0.21953896816684962, 0.21929824561403508, 0.21905805038335158, 0.2188183807439825, 0.2185792349726776, 0.2183406113537118, 0.21810250817884405, 0.2178649237472767, 0.2176278563656148, 0.21739130434782608, 0.21715526601520088, 0.21691973969631237, 0.21668472372697725, 0.21645021645021645, 0.21621621621621623, 0.2159827213822894, 0.21574973031283712, 0.21551724137931033, 0.21528525296017223, 0.21505376344086022, 0.21482277121374865, 0.2145922746781116, 0.21436227224008575, 0.21413276231263384, 0.21390374331550802, 0.21367521367521367, 0.21344717182497333, 0.21321961620469082, 0.21299254526091588, 0.21382978723404256, 0.21360255047821466, 0.21337579617834396, 0.21314952279957583, 0.2129237288135593, 0.2126984126984127, 0.2124735729386892, 0.2133051742344245, 0.2141350210970464, 0.21390937829293993, 0.21368421052631578, 0.21345951629863302, 0.21323529411764705, 0.2130115424973767, 0.21278825995807127, 0.21256544502617802, 0.21234309623430964, 0.21212121212121213, 0.21189979123173278, 0.2116788321167883, 0.21145833333333333, 0.21123829344432882, 0.21101871101871103, 0.21079958463136034, 0.21058091286307054, 0.21036269430051813, 0.21014492753623187, 0.20992761116856257, 0.2097107438016529, 0.20949432404540763, 0.20927835051546392, 0.2090628218331617, 0.2088477366255144, 0.20863309352517986, 0.20841889117043122, 0.2082051282051282, 0.20799180327868852, 0.20777891504605936, 0.20756646216768918, 0.20735444330949948, 0.20714285714285716, 0.2069317023445464, 0.20672097759674135, 0.20651068158697863, 0.20630081300813008, 0.20609137055837565, 0.20588235294117646, 0.20567375886524822, 0.2054655870445344, 0.20525783619817997, 0.20505050505050504, 0.20484359233097882, 0.20463709677419356, 0.20443101711983888, 0.20422535211267606, 0.20402010050251257, 0.20381526104417672, 0.20361083249749248, 0.20340681362725452, 0.2032032032032032, 0.203], 'name': 'default', 'type': 'scatter', 'mode': 'lines', 'textposition': 'right', 'line': {}, 'marker': {'size': 10, 'symbol': 'dot', 'line': {'color': '#000000', 'width': 0.5}}}]\n"
     ]
    },
    {
     "ename": "IndexError",
     "evalue": "list index out of range",
     "output_type": "error",
     "traceback": [
      "\u001b[0;31m---------------------------------------------------------------------------\u001b[0m",
      "\u001b[0;31mIndexError\u001b[0m                                Traceback (most recent call last)",
      "\u001b[0;32m<ipython-input-20-dead7829924e>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     24\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mexperiments\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 25\u001b[0;31m     \u001b[0m_\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlegends\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkeys\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mexploration_plots\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresults\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexperiments\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfig\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0max1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0max2\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlegends\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkeys\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcolor\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcolors\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     26\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     27\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n",
      "\u001b[0;32m<ipython-input-19-083b2a5f97a6>\u001b[0m in \u001b[0;36mexploration_plots\u001b[0;34m(x, results, key, fig, ax1, ax2, legends, keys, color)\u001b[0m\n\u001b[1;32m     42\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mk\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mmf_plots\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mkeys\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     43\u001b[0m             \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mres\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 44\u001b[0;31m             \u001b[0mmf_plots\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mres\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mk\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'y'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;36m1000\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     45\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     46\u001b[0m     \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n",
      "\u001b[0;31mIndexError\u001b[0m: list index out of range"
     ]
    },
    {
     "data": {
      "image/png": "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\n",
      "text/plain": [
       "<Figure size 864x432 with 2 Axes>"
      ]
     },
     "metadata": {
      "needs_background": "light"
     },
     "output_type": "display_data"
    }
   ],
   "source": [
    "\n",
    "baseline_data_fname=os.path.join(ROOT_DIR, "experiments", 'maze', 'plots', 'baselines', 'random_agent_wallless_%d.json' % size_maze)\n",
    "\n",
    "fig, (ax1, ax2) = plt.subplots(nrows=1, ncols=2, figsize=(12, 6))\n",
    "# fig.suptitle(\"Open Labyrinth experiments steps=%d, trials=%d, size_maze=%d\" % (n_steps, trials, size_maze), fontsize=16)\n",
    "ax1.set_ylim([0, 1.01])\n",
    "ax1.set_xlabel('environment steps')\n",
    "ax1.set_ylabel('# unique states visited /\\n# total states visited', wrap=True)\n",
    "\n",
    "ax2.set_ylim([0, 1.01])\n",
    "ax2.set_xlabel('environment steps')\n",
    "ax2.set_ylabel('proportion of all states visited')\n",
    "\n",
    "ax1.grid(True)\n",
    "ax2.grid(True)\n",
    "\n",
    "fig.tight_layout(rect=[0, 0.03, 1, 0.9])\n",
    "legends = []\n",
    "keys = []\n",
    "\n",
    "\n",
    "legends, keys, x = plot_baseline(baseline_data_fname, ax1, ax2, legends, keys)\n",
    "\n",
    "for i in range(len(experiments)):\n",
    "    _, legends, keys = exploration_plots(x, results, experiments[i], fig, ax1, ax2, legends, keys, color=colors[i])\n",
    "\n",
    "\n",
    "# mb1 = exploration_plots(results, keys[2], fig, ax1 ,ax2)\n",
    "# fig.legend((l for l in legends), (k for k in keys), 'lower right')\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# FOR PLOTTING WITH WALLS\n",
    "walled_size_maze = 21\n",
    "walled_n_steps = 1000\n",
    "walled_trials = 3\n",
    "walled_plot_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results_4_room')\n",
    "walled_experiments = [('simple maze count reward with q argmax', 'Count w/ Q-argmax'),\n",
    "                ('simple maze novelty reward with q argmax', 'Novelty w/ Q-argmax'),\n",
    "                ('simple maze novelty reward with 1 step q planning', 'Novelty w/ Planning (d=1)'),\n",
    "                ('simple maze novelty reward with 5 step q planning', 'Novelty w/ Planning (d=5)'),\n",
    "#                'simple maze novelty reward with 5 step reward planning'\n",
    "              ]\n",
    "walled_experiment_keys = ['Novelty w/ d=5',\n",
    "                         ]\n",
    "colors = ['orange', 'purple', 'green', 'red', 'brown', 'cyan']\n",
    "walled_results = {}\n",
    "for exp, title in walled_experiments:\n",
    "    walled_results[exp] = parse_visdom_plot_dir(os.path.join(walled_plot_dir, exp), trials=walled_trials)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "walled_baseline_data_fname=os.path.join(ROOT_DIR, "experiments", 'maze', 'plots', 'baselines', 'random_agent_%d.json' % walled_size_maze)\n",
    "\n",
    "walled_fig, (wax1, wax2) = plt.subplots(nrows=1, ncols=2, figsize=(12, 6))\n",
    "# walled_fig.suptitle(\"4-room Labyrinth experiments steps=%d, trials=%d, size_maze=%d\" % (walled_n_steps, walled_trials, walled_size_maze), fontsize=16)\n",
    "wax1.set_ylim([0, 1.01])\n",
    "wax1.set_xlabel('environment steps')\n",
    "wax1.set_ylabel('# unique states visited /\\n# total states visited', wrap=True)\n",
    "\n",
    "wax2.set_ylim([0, 1.01])\n",
    "wax2.set_xlabel('environment steps')\n",
    "wax2.set_ylabel('proportion of all states visited')\n",
    "\n",
    "wax1.grid(True)\n",
    "wax2.grid(True)\n",
    "\n",
    "walled_fig.tight_layout(rect=[0, 0.03, 1, 0.9])\n",
    "walled_legends = []\n",
    "walled_keys = []\n",
    "\n",
    "wlegends, wkeys, x = plot_baseline(walled_baseline_data_fname, wax1, wax2, walled_legends, walled_keys)\n",
    "for i in range(len(walled_experiments)):\n",
    "    _, walled_legends, walled_keys = exploration_plots(x, walled_results, walled_experiments[i], walled_fig, wax1, wax2, wlegends, wkeys, color=colors[i])\n",
    "# walled_fig.legend((l for l in walled_legends), (k for k in walled_keys), bbox_to_anchor=(1,0), loc=\"lower right\", \n",
    "#                           bbox_transform=plt.gcf().transFigure)\n",
    "# plt.subplots_adjust(left=0.1, bottom=0.1, right=0.5)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# FOR ABLATION\n",
    "walled_size_maze = 21\n",
    "walled_n_steps = 1000\n",
    "walled_trials = 3\n",
    "# walled_plot_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "walled_plot_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "\n",
    "walled_experiments = [\n",
    "                ('simple maze novelty reward with 5 step q planning avg_knn', 'Novelty w/ Planning Avg'),\n",
    "                ('simple maze novelty reward with 5 step q planning', 'Novelty w/ Planning Weighted Avg'),\n",
    "#                'simple maze novelty reward with 5 step reward planning'\n",
    "              ]\n",
    "walled_experiment_keys = ['Novelty w/ d=5',\n",
    "                         ]\n",
    "colors = ['orange', 'purple', 'green', 'red', 'brown', 'cyan']\n",
    "walled_results = {}\n",
    "for exp, title in walled_experiments:\n",
    "    walled_results[exp] = parse_visdom_plot_dir(os.path.join(walled_plot_dir, exp), trials=walled_trials)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "walled_baseline_data_fname=os.path.join(ROOT_DIR, "experiments", 'maze', 'plots', 'baselines', 'random_agent_%d.json' % walled_size_maze)\n",
    "\n",
    "walled_fig, (wax1, wax2) = plt.subplots(nrows=1, ncols=2, figsize=(12, 6))\n",
    "# walled_fig.suptitle(\"4-room Labyrinth experiments steps=%d, trials=%d, size_maze=%d\" % (walled_n_steps, walled_trials, walled_size_maze), fontsize=16)\n",
    "wax1.set_ylim([0, 1.01])\n",
    "wax1.set_xlabel('environment steps')\n",
    "wax1.set_ylabel('# unique states visited /\\n# total states visited', wrap=True)\n",
    "\n",
    "wax2.set_ylim([0, 1.01])\n",
    "wax2.set_xlabel('environment steps')\n",
    "wax2.set_ylabel('proportion of all states visited')\n",
    "\n",
    "wax1.grid(True)\n",
    "wax2.grid(True)\n",
    "\n",
    "walled_fig.tight_layout(rect=[0, 0.03, 1, 0.9])\n",
    "walled_legends = []\n",
    "walled_keys = []\n",
    "\n",
    "wlegends, wkeys, x = plot_baseline(walled_baseline_data_fname, wax1, wax2, walled_legends, walled_keys, plot=False)\n",
    "for i in range(len(walled_experiments)):\n",
    "    _, walled_legends, walled_keys = exploration_plots(x, walled_results, walled_experiments[i], walled_fig, wax1, wax2, wlegends, wkeys, color=colors[i])\n",
    "# walled_fig.legend((l for l in walled_legends), (k for k in walled_keys), bbox_to_anchor=(1,0), loc=\"right\", \n",
    "#                           bbox_transform=plt.gcf().transFigure)\n",
    "# plt.subplots_adjust(left=0.1, bottom=0.1, right=0.5)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "experiments_dir = os.path.join(ROOT_DIR, "experiments", 'maze', "runs")\n",
    "results_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "name = 'NEW simple maze count reward with q_argmax'\n",
    "\n",
    "try:\n",
    "    os.mkdir(os.path.join(results_dir, name))\n",
    "except Exception:\n",
    "    print(\"file exists\")\n",
    "    raise Exception(\"FILE EXISTS ERROR\")\n",
    "\n",
    "ids = [3455171,\n",
    "3455173,\n",
    "3455174,\n",
    "3455175,\n",
    "3455176,\n",
    "3455177,\n",
    "3455178,\n",
    "3455179,\n",
    "3455180,\n",
    "3455181]\n",
    "\n",
    "ids = [str(i) for i in ids]\n",
    "combined_results = {'exploration_factors': [],\n",
    "                    'ratios_visited': []}\n",
    "\n",
    "for jobid in ids:\n",
    "    for exp_dir in os.listdir(experiments_dir):\n",
    "        if jobid in exp_dir:\n",
    "            results_file = os.path.join(experiments_dir, exp_dir, 'results.json')\n",
    "            with open(results_file, 'r') as f:\n",
    "                results = json.load(f)\n",
    "                combined_results['exploration_factors'].append(results['exploration_factors'][0])\n",
    "                combined_results['ratios_visited'].append(results['ratios_visited'][0])\n",
    "\n",
    "with open(os.path.join(results_dir, name, 'parsed_results.json'), 'w') as f:\n",
    "    json.dump(combined_results, f)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "len(combined_results['ratios_visited'][0])"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# WALLLESS EXPERIMENTS NEW\n",
    "size_maze = 21\n",
    "n_steps = 1000\n",
    "trials = 3\n",
    "plot_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "experiments = [('NEW simple maze count reward with q_argmax', 'Count w/ Q-argmax'),\n",
    "               ('NEW simple maze novelty reward with q_argmax', 'Novelty w/ Q-argmax'),\n",
    "               ('NEW simple maze novelty reward with 1 step q planning', 'Novelty w/ Planning (d=1)'),\n",
    "#                'simple maze novelty reward with 3 step q planning'\n",
    "#                ('simple maze novelty reward with 3 step reward planning', 'Novelty w/ MCTS (d=3)'),\n",
    "               ('NEW simple maze novelty reward with 5 step q planning', 'Novelty w/ Planning (d=5)'),\n",
    "#                'simple maze novelty reward with 5 step reward planning'\n",
    "              ]\n",
    "colors = ['orange', 'purple', 'green', 'red', 'brown', 'cyan']"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "baseline_data_fname=os.path.join(ROOT_DIR, "experiments", 'maze', 'plots', 'baselines', 'random_agent_wallless_%d.json' % size_maze)\n",
    "data_base_fname=os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "\n",
    "fig, (ax1, ax2) = plt.subplots(nrows=1, ncols=2, figsize=(12, 6))\n",
    "# fig.suptitle(\"Open Labyrinth experiments steps=%d, trials=%d, size_maze=%d\" % (n_steps, trials, size_maze), fontsize=16)\n",
    "ax1.set_ylim([0, 1.01])\n",
    "ax1.set_xlabel('environment steps')\n",
    "ax1.set_ylabel('# unique states visited /\\n# total states visited', wrap=True)\n",
    "\n",
    "ax2.set_ylim([0, 1.01])\n",
    "ax2.set_xlabel('environment steps')\n",
    "ax2.set_ylabel('proportion of all states visited')\n",
    "\n",
    "ax1.grid(True)\n",
    "ax2.grid(True)\n",
    "\n",
    "fig.tight_layout(rect=[0, 0.03, 1, 0.9])\n",
    "legends = []\n",
    "keys = []\n",
    "\n",
    "\n",
    "legends, keys, x = plot_baseline(baseline_data_fname, ax1, ax2, legends, keys)\n",
    "\n",
    "for i in range(len(experiments)):\n",
    "    with open(os.path.join(data_base_fname, experiments[i][0], 'parsed_results.json')) as f:\n",
    "        print(os.path.join(data_base_fname, experiments[i][0], 'parsed_results.json'))\n",
    "        results = json.load(f)\n",
    "    explr_fac = results['exploration_factors']\n",
    "    visited_ratios = results['ratios_visited']\n",
    "    _, legends, keys = plot_means_with_std(x, explr_fac, visited_ratios, experiments[i][1], fig, ax1, ax2, legends, keys, color=colors[i])\n",
    "#     _, legends, keys = exploration_plots(x, results, experiments[i], fig, ax1, ax2, legends, keys, color=colors[i])\n",
    "\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "\n",
    "def parse_dirs(ids, name, results_dir, experiments_dir):\n",
    "    try:\n",
    "        os.mkdir(os.path.join(results_dir, name))\n",
    "    except Exception:\n",
    "        print(\"file exists\")\n",
    "        raise Exception(\"FILE EXISTS ERROR\")\n",
    "        \n",
    "    ids = [str(i) for i in ids]\n",
    "    combined_results = {'exploration_factors': [],\n",
    "                        'ratios_visited': []}\n",
    "\n",
    "    for jobid in ids:\n",
    "        for exp_dir in os.listdir(experiments_dir):\n",
    "            if jobid in exp_dir:\n",
    "                results_file = os.path.join(experiments_dir, exp_dir, 'results.json')\n",
    "                with open(results_file, 'r') as f:\n",
    "                    results = json.load(f)\n",
    "                    combined_results['exploration_factors'].append(results['exploration_factors'][0])\n",
    "                    combined_results['ratios_visited'].append(results['ratios_visited'][0])\n",
    "\n",
    "    with open(os.path.join(results_dir, name, 'parsed_results.json'), 'w') as f:\n",
    "        json.dump(combined_results, f)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "experiments_dir = os.path.join(ROOT_DIR, "experiments", 'maze', "runs")\n",
    "results_dir = os.path.join(ROOT_DIR, "experiments", 'maze', 'results')\n",
    "\n",
    "experiments = [('NEW simple maze w walls count reward with q_argmax',\n",
    "               {'title': 'Count w/ Q-argmax', 'ids': \n",
    "                [3558044,\n",
    "                3558045,\n",
    "                3558047,\n",
    "                3558048,\n",
    "                3558050,\n",
    "                3558051,\n",
    "                3558052]}),\n",
    "               ('NEW simple maze w walls novelty reward with q_argmax', \n",
    "                {'title': 'Novelty w/ Q-argmax', 'ids': \n",
    "                 [3557966,\n",
    "                3557967,\n",
    "                3557968,\n",
    "                3557969,\n",
    "                3557970,\n",
    "                3557971,\n",
    "                3557972]}),\n",
    "               ('NEW simple maze w walls novelty reward with 1 step q planning', \n",
    "                {'title': 'Novelty w/ Planning (d=1)', 'ids':\n",
    "                 [3557982,\n",
    "                3557983,\n",
    "                3557984,\n",
    "                3557985,\n",
    "                3557986,\n",
    "                3557987,\n",
    "                3557988]}),\n",
    "               ('NEW simple maze w walls novelty reward with 5 step q planning', \n",
    "                {'title': 'Novelty w/ Planning (d=5)', 'ids':\n",
    "                 [3558006,\n",
    "                3558007,\n",
    "                3558008,\n",
    "                3558009,\n",
    "                3558010,\n",
    "                3558011,\n",
    "                3558012]})\n",
    "              ]\n",
    "\n",
    "# for exp, info in experiments:\n",
    "#     parse_dirs(info['ids'], exp, results_dir, experiments_dir)\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "add_results = {}\n",
    "for i in range(len(walled_experiments)):\n",
    "#     _, walled_legends, walled_keys = exploration_plots(x, walled_results, walled_experiments[i], walled_fig, wax1, wax2, wlegends, wkeys, color=colors[i])\n",
    "    key, title = walled_experiments[i]\n",
    "    mf = walled_results[key]\n",
    "\n",
    "    mf_plots = {k: [] for k in list(mf.values())[0].keys()}\n",
    "    for fname, res in mf.items():\n",
    "        for k in mf_plots.keys():\n",
    "            mf_plots[k].append(res[k][1]['y'][:1000])\n",
    "    try:\n",
    "        exp_fac_plots = mf_plots['Exploration Factor for ep 0'] if 'Exploration Factor for ep 0' in mf_plots else mf_plots['Exploration Factor']\n",
    "        vis_rat_plots = mf_plots['Ratio of states visited for ep 0'] if 'Ratio of states visited for ep 0' in mf_plots else mf_plots['Ratio of states visited']   \n",
    "    except Exception:\n",
    "        print(mf)\n",
    "        print(mf_plots.keys())\n",
    "        \n",
    "    explr_fac = np.array(exp_fac_plots)\n",
    "    visited_ratios = np.array(vis_rat_plots)\n",
    "    add_results[experiments[i][0]] = {\n",
    "        'name': key,\n",
    "        'exploration_factors': explr_fac,\n",
    "        'ratios_visited': visited_ratios\n",
    "    }"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "walled_baseline_data_fname=os.path.join(ROOT_DIR, "experiments", 'maze', 'plots', 'baselines', 'random_agent_%d.json' % walled_size_maze)\n",
    "\n",
    "walled_fig, (wax1, wax2) = plt.subplots(nrows=1, ncols=2, figsize=(12, 6))\n",
    "# walled_fig.suptitle(\"4-room Labyrinth experiments steps=%d, trials=%d, size_maze=%d\" % (walled_n_steps, walled_trials, walled_size_maze), fontsize=16)\n",
    "wax1.set_ylim([0, 1.01])\n",
    "wax1.set_xlabel('environment steps')\n",
    "wax1.set_ylabel('# unique states visited /\\n# total states visited', wrap=True)\n",
    "\n",
    "wax2.set_ylim([0, 1.01])\n",
    "wax2.set_xlabel('environment steps')\n",
    "wax2.set_ylabel('proportion of all states visited')\n",
    "\n",
    "wax1.grid(True)\n",
    "wax2.grid(True)\n",
    "\n",
    "walled_fig.tight_layout(rect=[0, 0.03, 1, 0.9])\n",
    "walled_legends = []\n",
    "walled_keys = []\n",
    "\n",
    "wlegends, wkeys, x = plot_baseline(walled_baseline_data_fname, wax1, wax2, walled_legends, walled_keys)\n",
    "\n",
    "\n",
    "for i in range(len(experiments)):\n",
    "    with open(os.path.join(results_dir, experiments[i][0], 'parsed_results.json')) as f:\n",
    "        print(os.path.join(results_dir, experiments[i][0], 'parsed_results.json'))\n",
    "        results = json.load(f)\n",
    "    explr_fac = np.concatenate((results['exploration_factors'], add_results[experiments[i][0]]['exploration_factors']), axis=0)\n",
    "    visited_ratios = np.concatenate((results['ratios_visited'], add_results[experiments[i][0]]['ratios_visited']), axis=0)\n",
    "#     print(explr_fac.shape)\n",
    "#     print(visited_ratios.shape)\n",
    "    _, walled_legends, walled_keys = plot_means_with_std(x, explr_fac, visited_ratios, experiments[i][1], walled_fig, wax1, wax2, walled_legends, walled_keys, color=colors[i])\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": []
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.6.9"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 2
}
