{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["random_steps = np.array([528, 1956, 2592, 1865, 3274]) #, 2231, 994, 2896, 3701, 2605\n", "d_step_q_steps = np.array([307, 240, 417, 461])\n", "x_axis = np.array(['random', '5 step Q planning'])\n", "steps = [random_steps, d_step_q_steps]\n", "means = np.array([np.average(l, axis=-1) for l in steps])\n", "std_devs = np.array([np.std(l, axis=-1) for l in steps])\n", "plt.errorbar(x_axis, means, std_devs, fmt='o', capsize=5,)\n", "plt.ylabel(\"Avg # steps to goal state (w/ std dev)\")\n", "plt.title(f\"Initial Acrobot results\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([2043.  ,  356.25]), array([911.17945543,  87.46820851]))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["means, std_devs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}