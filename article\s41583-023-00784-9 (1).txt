Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 195
nature reviews neurosciencehttps://doi.org/10.1038/s41583-023-00784-9
Review article
 Check for updates
Curiosity: primate neural circuits for 
novelty and information seeking
Ilya E. Monosov    1,2,3,4,5  
Abstract
For many years, neuroscientists have investigated the behavioural, 
computational and neurobiological mechanisms that support 
value-based decisions, revealing how humans and animals make choices 
to obtain rewards. However, many decisions are influenced by factors other than the value of physical rewards or second-order reinforcers 
(such as money). For instance, animals (including humans) frequently 
explore novel objects that have no intrinsic value solely because they 
are novel and they exhibit the desire to gain information to reduce their 
uncertainties about the future, even if this information cannot lead  to reward or assist them in accomplishing upcoming tasks. In this  
Review, I discuss how circuits in the primate brain responsible for detecting, predicting and assessing novelty and uncertainty regulate behaviour and give rise to these behavioural components of curiosity. 
I also briefly discuss how curiosity-related behaviours arise during 
postnatal development and point out some important reasons for the persistence of curiosity across generations.Sections
Introduction
Novelty: detecting and 
seeking
Uncertainty: representing  
and reducingOutstanding questions and 
future directions
1Department of Neuroscience, Washington University School of Medicine, St. Louis, MO, USA. 2Department of 
Electrical Engineering, Washington University, St. Louis, MO, USA. 3Department of Biomedical Engineering, 
Washington University, St. Louis, MO, USA. 4Department of Neurosurgery, Washington University, St. Louis, MO, 
USA. 5Pain Center, Washington University, St. Louis, MO, USA.  e-mail: <EMAIL>
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 196
Review articlepredict or be associated with rewards and punishments, their ability to 
attract inspection behaviours seem to be highly (external and internal) 
state dependent36–40.
In the sub-sections that follow, I will first discuss the mechanisms 
and neural circuits in the primate brain that detect perceptually novel 
objects and support rapid stimulus-evoked inspection or avoidance 
behaviours. I will then discuss how some of the same neural circuits 
support the prediction of future novelty.
Novelty detection
Insight into how biological neural systems might detect or measure per -
ceptual object novelty can be gained through a brief consideration of 
the diverse strategies that computer algorithms use to classify sensory  
stimuli as novel or familiar, as well as the challenges that they face.
To detect novelty, algorithms identify patterns in statistical data 
and/or rely on memory of past stimuli. Hidden Markov models detect 
novelty by detecting outlier events — or ‘surprises’ — such as an out-of-
sequence stimulus in a stream of sequentially presented stimuli41. 
Recurrent neural networks can incorporate uncertainty and learn 
distributional higher-order statistics of data, allowing them to detect 
sensory novelty and surprises by assigning probabilities to stimuli, such 
that those with low probability are classified as novel or surprising42,43. 
Neither of these approaches can easily discriminate improbable or 
unpredictable stimuli from truly ‘novel’ ones — that is, stimuli that have 
never been seen in the past28. Taking a different approach, Hopfield net -
works (HNs), which are inspired by Hebbian plasticity and designed for 
memory storage, rely on ‘memory’ of the sensory content of a stimulus 
to detect its novelty44,45. After the ‘memories’ of stimuli are stored, the 
HN has an energy function that is lower for expected and unsurpris -
ing familiar stimuli (that is, the network is in a more stable state) and 
higher for novel stimuli (that is, the network is in a less stable state). 
The energy function (network stability) is readout by novelty-signalling 
units. In HN-inspired models of the cortical ventral visual stream, as a 
stimulus transforms from novel to familiar because of repeated pres-entation, there is a redistribution of weights in the HN that decreases 
the activity of novelty-signalling units and increases the activity of 
familiarity-preferring units. HNs display a relatively high-capacity 
stimulus memory and some degree of visual generalization44–46. Fur-
thermore, by changing the learning rules, HNs can be optimized to 
enable continuous learning of many stimuli, minimizing catastrophic 
interference among previously learned and newly learned stimuli46.
While very useful, these novelty detector algorithms also face 
important challenges. First, novelty detection requires a measure of 
how different an event or stimulus is from an expectation or prior. The 
difference threshold that leads to a classification of novelty must be 
determined in a flexible context-sensitive manner. Second, selecting 
the most behaviourally useful or relevant information (that which can 
be used to form beliefs or expectations to detect novel or surprising 
events) is difficult. Third, parsing novelty from surprise or unexpected -
ness using computational algorithms is currently challenging. Fourth, 
the timescales that govern the learning and subsequent forgetting 
of sensory information, and hence novelty detection, must be sensi -
tive to the task at hand. Last, with a few exceptions47, most artificial 
novelty detectors do not incorporate sensitivity to when objects were 
seen (recency) and cannot support the multiple timescales of object 
memory in support of adaptive learning, storage and forgetting of 
sensory data.
Rather than utilizing a single algorithm that relies on either statisti-
cal data or memory, the mammalian brain could use multiple systems for Introduction
In recent decades, neurobiologists have used economic and ethological 
methods to assess the behavioural, computational and neurobiologi -
cal mechanisms of value-guided decision-making and motivation. 
They also studied how humans and animals seek information to help 
them obtain first-order reinforcers (such as food) and second-order 
reinforcers (such as money)1–6. These instrumental behaviours are an 
important component of exploration that allows humans and other 
animals to find rewards and avoid punishments.
Also, many everyday decisions are guided by factors other than 
physical rewards and their instrumental values. For example, humans and  
animals explore novel objects (those that they have never seen) and/or  
display motivations to obtain information that can reduce their 
uncertainties about the future, even when the object or the informa-
tion cannot be used to obtain rewards or for the task at hand. Such 
information-seeking behaviours, often called non-instrumental infor -
mation seeking4–7, are core components of ‘curiosity’6,8–14 — a term in 
psychology that is often used to describe a constellation of motivations 
and behaviours that contribute to the formation of beliefs about the 
world and one’s own state through the reduction of uncertainty and 
to the maximization of novel experiences (see previous reviews8,9,12,14,15 
and Box  1). These motivations have a broad impact on decision-making, 
internal states, attention, and action.
In this Review, I will present a brain circuit-based discussion of 
curiosity. I will focus on a subset of the multiple diverse yet interact-ing sources (or components) of curiosity, selected because they are those that the field understands best at the level of neurobiology in primates. First, I describe the multiple manners in which the primate 
brain could detect novel visual objects. Next, I consider internally 
driven sources of curiosity such as the desire to predict and seek out 
future novelty and to estimate and reduce reward and punishment 
uncertainty (Fig.  1). I discuss how these signals emerge in the brain and 
how they flexibly impact different behaviours and internal states16–18, 
such as attention and choice. Last, I outline how the primate brain com -
putes the value of uncertainty-resolving information in the context  
of decisions in which agents must choose among offers that consist of 
many different attributes or features, similar to the decisions we make 
in our daily lives, and how this value of information can be integrated 
with the value of physical rewards to guide behaviour (thus, balancing 
the need for reward with the desire for information). Alongside these 
discussions, I briefly consider an integrative set of hypotheses about 
how curiosity-related behaviours are constructed and learned across 
postnatal development and suggest how and why these behaviours 
persist in the animal kingdom, across generations and species (Box  2).
Novelty: detecting and seeking
Primates and other animals are often motivated to obtain the opportu -
nity to inspect and interact with novel objects8,9,12,19–30. The behaviours 
motivated by this desire can usually be separated into those that involve 
exploring or inspecting novel objects that are present in the current 
environment (guided by novelty detection) and those that involve 
predicting and then seeking (or avoiding) future novelty.
The extent of the preference of an animal for novelty depends on 
many factors. Depending on the environment or context and the task at 
hand, novel objects can have a positive or a negative motivational value 
and be either appetitive or aversive. Moreover, some animal species are 
(on average) neophobic, whereas others are neophilic18,31,32. Preferences 
for novelty may also change as a function of experience and develop -
ment19,33–35 (Box 2) . Even when novel objects are explicitly known to not 
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 197
Review articlenovelty detection that function on distinct timescales and have distinct 
objectives25,48–52. This is akin to what occurs in  motor learning, which 
evolves on fast and slow timescales across our lifetime48. Behavioural 
heterogeneity in response to novel objects or situations could also be 
related to the multiple heterogenous timescales of neuronal processing 
and integration observed across different brain areas51,53– 55.
To address the question of which systems the brain uses to 
detect novelty more directly, a recent study assessed the relationship 
between novelty, recency and different forms of sensory surprise at the 
level of single neurons and explored the timescales over which object 
learning and forgetting take place across the primate temporal cor -
tex, amygdala, hippocampus, basal ganglia and prefrontal cortices49. 
Across these regions, on average, the neurons that discriminated 
between novel objects and familiar objects also displayed sensitivity 
to the duration of time that had elapsed since the familiar objects were 
last seen (object recency; also see ref. 25) and tended to discriminate 
between predictable and unpredictable familiar objects (sensory 
surprise) (Fig.  2). There were also important differences across brain areas: novelty-discriminating neurons in the basal forebrain, hip -
pocampus and striatum were sensitive to unpredictable objects that 
violated well-learned sequences of objects, whereas neurons in the 
perirhinal cortex and amygdala tended to respond more generally to 
objects whose identity was not predictable in a sequence of objects. These relationships between novelty, surprise and recency would be expected to arise from a neural network that uses surprise to detect novelty and in which learning and forgetting occur on multiple time-
scales. Indeed, the novelty-discriminating neurons displayed hetero -
geneous timescales of learning and forgetting. Neurons that learned 
fast also forgot fast: as novel objects were repeatedly presented, these 
novelty-sensitive neurons quickly changed their responses, effectively 
treating objects as familiar after just a few presentations. After an 
overnight break, these fast-learning neurons often largely ‘forgot’ the 
training of the last day, treating the objects again as novel. By contrast, 
neurons that learned slowly also tended to forget slowly. As novel 
objects were repeatedly presented, these neurons required many 
more trials to lose their novelty selectivity. Following an overnight Box 1
The long-term value of curiosity
According to classical definitions, instrumental information seeking 
provides direct benefits to the organism. For example, in probabil-istic arm bandit tasks, exploration leads to better performance, by allowing the animal to obtain more reward gains within a relatively  
short time frame
17. Non-instrumental information-seeking or 
curiosity-related behaviours do not provide such direct immediate benefits and, hence, can be interpreted as having the aim of obtaining information for its own sake. However, it could also be argued that they may serve the individual agent or species on longer timescales and provide benefits beyond the immediately foreseeable future
4,14. For example, curiosity-related behaviours 
may uncover hidden sources of uncertainty and facilitate the formation of internal models or beliefs, thus, improving survival and success across a lifetime. This may be especially important for slow replicating
15 organisms engaged in complex situations, such as 
in social settings or in dynamic foraging scenarios in which trade-offs between many factors need to be taken into account to survive. Importantly, these arguments do not imply that instrumental and non-instrumental information seeking are motivated by precisely the same computations or circuits, but they do suggest that they have more functions or mechanisms in common than is often appreciated.
Work in machine learning and artificial intelligence has shown 
that novelty seeking can help agents learn their environment and overcome the sparse reward problem (in which environments do not have enough reward
19,40). However, it may seem less clear why 
agents seek advance non-instrumental information to resolve their uncertainty. Below, I discuss three possible answers.
Non-instrumentality in single contexts does not imply that a 
behaviour is not adaptive or instrumental when ‘averaged’ across many contexts (particularly in foraging situations in which we and other animals evolved).  Information gathering might help form accurate probability estimates and, thus, provide an adaptive advantage over long time periods. The value of this advantage may be large enough to bias an agent to sacrifice a small reward or time  
in the short term for the benefit of either the species as a whole (across evolution) or of the agent themselves.
Beyond refining knowledge of statistical inner dependencies, 
early information may have advantages in stay-or-leave decisions, such as those that occur during foraging in ‘real-world’ patches  
or micro-environments. In these situations, the physical parameters (space, difficulty of terrain) mean that time is required to leave, and competition and predation may also be present. In this situation,  
and under conditions of volatility and non-reducible uncertainty, early information may give agents an advantage (for example, may enable them to leave a depleting patch earlier).
Early information may help an agent to infer the sources and 
behavioural significance of a surprise. For example, it may help to determine whether a surprise is attributable to irreducible uncertainty (that is, noise) or to a lack of knowledge (a situation  
that we can potentially change). Because a perfect solution for  
this problem does not exist, and because the statistics of the  
world are constantly changing, it may be advantageous to reinforce the formation of beliefs or internal models, regardless of their perceived usefulness. To better understand such mechanisms,  
it will be critical to understand the effects of early information on how agents learn from surprises and adjust their behavioural policies when foraging across many contexts or environments.
Early information could reduce the working memory and 
attentional load of an agent, enhancing the accuracy of credit assignment, that is of identifying which action or event led to which outcome or reward, and improving performance. This and other possibilities require further theoretical, experimental and computational work to resolve.
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 198
Review articlebreak, these neurons continued to display signatures of the learning 
that had taken place the previous day49. Although heterogeneous 
timescales were found in all brain areas, there was also a population 
trend in which some brain regions on average learned quickly whereas 
others learned slowly. Findings broadly consistent with these results 
have been reported in the prefrontal basal ganglia circuitry56– 58. How -
ever, those experiments were carried out in the context of object 
value associative learning and additional work is needed to determine 
whether the findings generalize to novelty-familiarity learning and 
forgetting. Such future studies would give important hints about the 
synaptic mechanisms that underlie behavioural change in distinct but 
overlapping forms of learning.
Other studies have provided further evidence that the primate 
brain contains single neurons whose activity is dominantly driven by 
the novelty or familiarity of sensory stimuli, rather than their features20. 
However, the primate brain also contains neurons that are both highly 
object or stimulus selective (displaying differential responses to objects 
because of their underlying low-level features) and modulated by stimu -
lus novelty or familiarity19,59. To understand how this diversity in novelty 
representation contributes to behaviour, additional circuit information 
will be required — as is beginning to be revealed in the fly60 and modelled 
in silico.In sum, studies to date have shown that novelty detection in the 
primate brain involves a set of diverse computations that are closely 
tied to the processing of multiple forms of surprise and recency over 
multiple timescales. This may afford context-dependent, graded and 
highly tunable capacities to process novel stimuli — a notion that is 
inspired by theories of motor learning48,61 in which fast and slow learn-
ing and forgetting are proposed to support behaviour in fast-changing 
and slow-changing scenarios, respectively.
Novelty prediction and novelty seeking
Animals not only passively detect and react to novel stimuli but also 
actively predict, search for and/or avoid them. This has prompted inves -
tigations of the neural circuits that the brain uses to learn to anticipate 
or predict future novelty to regulate novelty seeking.
One candidate mechanism for signalling predictions about future 
novelty — such as the probability that a novel object will be encoun -
tered in a particular context — was the neural activity of dopamine 
neurons62. Dopamine neurons in the medial regions of the substantia 
nigra pars compacta and the ventral tegmental area signal reward pre -
diction errors (RPEs), the difference between predicted and received 
rewards63–65. RPEs are thought to be used by the brain to learn the values 
of states and actions and to mediate reward-guided motivation65–67. It 
has, therefore, been suggested that dopamine neurons could also signal 
novelty and novelty predictions in situations in which novelty has no extrinsic reward value and, thus, motivate novelty seeking. However, 
when novelty is dissociated from opportunities to learn or to obtain  
physical reward, RPE-coding dopamine neurons do not signal pre -
dictions of future novelty or novelty prediction errors in monkeys18  
and mice68. This dissociation or separability of novelty and reward 
at the level of neural circuits may support their dissociability at the 
level of behaviour in human infants, adults and many animals19. 
Indeed, because novelty can be aversive or appetitive depending on 
the context36,40,69, it could be argued that linking novelty prediction 
with reward valuation and reward value prediction at all levels of the computational hierarchy would not be useful for context-dependent 
behavioural control. Instead, the separability of reward and novelty in 
some neural circuits might enable behavioural flexibility by allowing for 
context-dependent modulation of value by novelty40. This is supported 
by evidence that novelty and economic value may influence behaviour 
on overlapping but distinct timescales (Fig. 1 ) through distinct effector 
or motor control mechanisms27,58 ,70.
The separability of reward prediction and novelty prediction has 
been proposed by artificial-learning researchers aiming to construct 
‘self-evolving’ agents (agents with the ability to improve, adapt or evolve 
their own capabilities over time without direct human intervention)19,71,72.  
The flexible context-dependent modulation of value by novelty afforded 
by this architecture can help to solve some forms of exploration–  
exploitation dilemmas3,12,19,62,73, particularly in contexts in which rewards 
are sparse, by allowing for flexible boosting (or bonusing) of RPEs by 
novelty to modulate exploration in a context-dependent manner74.
What is the source of novelty predictions in the primate brain? Extend -
ing previous work on novelty detection described above, it was recently 
found that neurons in the perirhinal cortex (PRH) that discriminate between 
novel and familiar objects also anticipate or predict future novel objects18. It 
was further shown that the caudal lateral zona incerta (ZI) utilizes such nov -
elty predictions to guide gaze to enable inspection of future novel objects. 
ZI neurons signalled novelty predictions and combined them with informa -
tion about the location and timing of upcoming gaze shifts to obtain the 
opportunity to interact with a novel object (Fig.  3). ZI regions that were • Estimation of uncertainty
• Information valuation
and prediction
• Physical reward valuation 
and prediction
Action selectionValue ofinformationValue ofphysicalreward
Total valueDetection and prediction• Novelty and surprise
• Sources of uncertainty 
• Behaviour-relevant stimuli
Fig. 1 | Multiple interacting sources of curiosity-related behaviours. Ways in 
which distinct interacting sources could give rise to curiosity-related behavioural programmes. Sensory systems detect novel and surprising stimuli, as well as potential sources of uncertainty, such as changes in sensory complexity 
(Fig.  2). The sensory systems can influence the motor system directly, to guide 
rapid responses, such as saccadic eye movements to novel objects (Fig.  3). 
Relatively slower processes integrate events and outcomes detected by sensory 
systems over time and estimate outcome uncertainty and guide behaviours to reduce it (Fig.  4). Uncertainty estimation also influences valuation and 
decision-making, biasing choices towards the selection of more informative or less informative offers, and participating in the integration of the value of 
information with the value of physical rewards (Fig.  5). These fast (leftmost 
arrow) and relatively slower (right arrows) processes may influence action 
selection to guide behaviour towards physical rewards and cognitive rewards (such as information and novelty) on different timescales through distinct neural circuits (Figs.  3–5).
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 199
Review articleenriched with novelty-predicting neurons send extensive projections to the 
superior colliculus18,75, a key region for the modulation of gaze and spatial 
attention76. Accordingly, inactivation of the ZI affected novelty-seeking 
gaze shifts. In rodents, the ZI has also been linked to processing the novelty 
of objects and regulating approach behaviour to these objects through 
the brainstem77. Future studies are required to assess the conserved and 
divergent functions of the ZI across species (see ref. 40  for review).
How PRH neurons acquire novelty predictions is currently unknown. 
One possibility is that this signal is acquired through an associative learning 
process that relates objects or events to abstract concepts such as novelty 
or familiarity. The basal forebrain — a major source of modulation of the neocortex — rapidly detects novel objects in object sequences20 and could, 
therefore, be one important contributor to such a learning mechanism in 
PRH. Indeed, degeneration of the basal forebrain impacts memory and other 
cognitive functions that support novelty detection78. Future work must 
now be conducted to understand whether the basal forebrain can guide 
learning in the PRH in a manner that impacts novelty-seeking behaviour.
Modulation of valuation by novelty
Learning theory suggests that RPEs could be modulated by novelty62,79 
to increase or decrease novelty seeking and increase or decrease the 
subjective values of reward-predicting novel sensory events. Indeed, 
Box 2
Curiosity across development
The process of learning to be curious tends to begin in the 
early stages of development and to be supported by genetic predisposition and computational capacity, which may differ across species. Understanding how behaviours are established over developmental and evolutionary timescales will shed further light  
on the neurobiological mechanisms of curiosity.
Theories in developmental psychology propose that an evo-
lutionary shift from simple types of exploration (such as where to move the body) to more complex types of exploration (such as cog-nitive operations that rely on abstract representation and distribu-tional statistical learning) has been supported by increasing levels of brain complexity
155,156 and increasing durations of postnatal 
development. However, the role of postnatal development in sup-porting curiosity is just beginning to be explored at the behavioural level and neurobiological research in this area is scant.
As they develop, the exploratory behaviour of children becomes 
more goal directed and less random
157. Early on, children engage in 
extensive surprise-driven and novelty-driven exploration, which may be an efficient way to acquire statistical models of the world
158–161. As they 
mature, their capacity to learn abstract representations and statistical relationships increases, as does the complexity and richness of their exploratory and curiosity-related behaviour. Rapid bottom–up novelty detection mechanisms (Figs.  2 and 3 ) may promote learning and 
exploration early in development. Later, relatively more sophisticated motivators of curiosity (Figs.  3–5), such as novelty prediction and 
uncertainty reduction, may become progressively important
91 (Fig. 1 ).
The highly complex and recurrent brain architecture of many 
animals and their rich behavioural repertoire raise the possibility that curiosity may arise from multiple mechanisms that operate on different timescales (Fig. 1 ). Among these is a simplified form of 
multi-agent learning, in which some circuits promote undirected exploratory or surprise-driven behaviour, whereas others learn simple associations and higher-order statistics from the resulting outcomes
151. A biological example of this is provided by the bird 
homologues of the mammalian basal ganglia, which contain neurons that generate behavioural variability in bird song production
162. This 
variability appears to be developmentally regulated163.
Primates possess distinctive traits that probably contribute to 
their exceptional capacity for curiosity. For example, the primate visual system is highly adept in its capacity to support rapid abstract reasoning
164–167. Additionally, the eye — a key initial means for primates 
to interact with their environment and a critical instrument supporting curiosity — is capable of rapid movement, and allows for strong control over sensory input
27. At early stages of postnatal development, 
eye movements and orienting behaviours are strongly influenced by perceptual novelty and surprising stimuli
168–171. As other effectors, such 
as hand movements, come online, they are coordinated and guided by this orienting to maximize the influence of surprise on statistical learning
172–175. Eye movements continue to be a key tool for information 
seeking and remain relatively strongly influenced by salient stimuli and surprising events, after maturation. However, at maturation, the other effectors are further guided to a relatively greater extent by the (signed) value of physical rewards and punishments (probably because, for example, the consequences of a misplaced hand or foot could prove fatal). Interestingly, eye movements also remain relatively stochastic, a feature that could participate in driving exploratory behaviour in adult agents (for review, see refs.  4,6,12,15).
Curiosity-related behaviours may also be involved in the develop-
mental construction of affordances that define the relationship between objects and oneself to build motor skills
176–181. Object affordances change 
as the body of the agent changes and tend to develop along with the ability to grip and explore objects with touch. Such behaviour often supports instrumental information seeking because many reaching behaviours help us learn and manipulate the world. To date, the interac-tion of somatosensory exploration and visual exploration, particularly across development, remains poorly understood (but see ref. 182 ).
During early postnatal development, primates and other mammals 
are often under the care of other agents whose behavioural goals are  
to maximize value (gain rewards and avoid punishments) and mini-mize the death rate of the young. As this supervision diminishes and social competition increases, the desire of an individual to explore and learn may change in magnitude or expression (for example, instead of approaching surprising and novel stimuli, they may mentally simulate the range of possible outcomes).  Risk preference and information preference can also change across development, reflecting a distaste for uncertainty as a function of age
183. Overall, 
these dynamics12,19,34,159,183– 188 could be associated with the dependence 
of older animals on more complex world models and beliefs and may correspond with shifts in the balance of the need to maximize surprise (when under the care of others) and the need to seek reward.
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 200
Review articlealthough value-coding dopamine neurons do not signal predictions 
of valueless novel objects18, they do respond more strongly to RPEs 
elicited by novel objects than those elicited by familiar objects during 
object value learning. Evidence for this comes from a study in which 
monkeys participated in a task in which a set of novel objects presented 
at the start of a behavioural session signalled three distinct probabilities of reward. As the objects and their outcomes were repeated, and the 
objects became familiar, the animals learned which object was associ -
ated with which reward probability. Across learning, the reward value 
signals of those dopamine neurons were stronger when the value  
was conveyed by novel objects at the start of learning than when it was  
conveyed by the now-familiar objects at later stages of learning80.Novelty Recency Sensory surprise
Sequence type IbFiring rate of a single
neuron in the amygdala
(spikes s–1)
02040
0 50002040
0 50002040
0 500
Time after stimulus onset (ms)FamiliarNovel 
Sequence type II
 Sequence type IISequence type III
. . . . . . . . .
Time after stimulus onset (ms) Time after stimulus onset (ms)PredictableUnpredictable
TimeSequence type IV
. . .. . .. . .. . .
 Less recent
More recentNovel
fractal
Familiar
fractal
PredictableUnpredictable
More recentLess recent0.6
0.5
0.4Classi/f.shortication accuracy
across all brain regions
Train: decode recencyTest: decode noveltyTrain: decode surpriseTest: decode noveltyAVMTC/Perirhinal
8AGlobus pallidusHippocampusStriatumInsulaOFCAnterior entorhinal cortex45BPosterior medial temporal cortexBasal forebrainAmygdala9/46V
Novelty-related neurons (%)0 20 40a
Brain region
Sequence type IV
Fig. 2 | Object novelty sensitivity in the primate brain. a, In a recent study, many 
brain areas in the primate brain (displayed on the y -axis) were found to contain 
neurons that discriminate between novel and familiar objects (displayed on the 
x-axis)49. b, Novelty detection in the brain is related to the detection of sensory 
surprise (unpredictability) and object recency49. The schematics show how these 
factors were manipulated during neural recording. Monkeys were presented  
with several sequences of visual stimuli consisting of three fractal objects. In  
one type of sequence (top row, left), the second object was always novel, but  
the other objects were familiar, and their identity was predictable. In a second  
type of sequence (middle row, left), all objects were familiar and predictable.  
Novelty sensitivity in panel  a was measured as a differential response between  
the novel object (in the first sequence) versus the familiar object (appearing  
in the second position in the second sequence). In a third sequence, three highly familiar objects appeared, but their identity was not predictable because they were drawn in a random sequence from a pool of familiar objects (top row, 
middle). Comparing responses to sequence types II and III provided a measure of 
neural sensitivity to surprise (unpredictability). Recency could be measured by comparing responses to familiar objects that were recently seen with responses to objects that were less recently seen. Examples of the responses of a single amygdala neuron to the different stimulus types from these sequences are shown at the bottom. This neuron and many other neurons across the brain displayed sensitivity to novelty, sensory surprise and recency. Because of this relationship 
of novelty, surprise, and recency, neural decoders trained to classify surprise 
or recency from the neural population responses could also classify objects as novel or familiar (bar plot). Figure adapted with permission from ref. 49 , Elsevier. 
AVMTC, anterior ventral medial temporal cortex; OFC, orbitofrontal cortex.
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 201
Review articleThe ZI prominently projects to the lateral habenula (LHb) and 
to the substantia nigra pars compacta (SNC), two key brain regions 
known to control and to signal RPEs40,81,82. Therefore, ZI neurons may 
be ideally positioned not only to control gaze behaviour in response 
to novelty predictions (directly through the superior colliculus18,75) 
but also to modulate learning through dopaminergic activity (either 
directly, through ZI projections to the SNC or indirectly through  
ZI projections to the LHb), in an excitatory or inhibitory manner. The ZI 
contains inhibitory and excitatory neurons and is particularly enriched 
with excitatory neurons in the caudal lateral regions40. How novelty is processed in these ZI areas and the precise neurons that it influ -
ences in the SNC and LHb will be important to uncover. Also, a circuit 
architecture in which motor control and learning are linked in a tem-
porally precise manner (that is, the same population of units modulate 
both motor output and learning) could be essential for efficient and 
accurate attribution of outcomes to actions (credit assignment), for 
example during novelty seeking in uncertain contexts40.
The PRH sends strong projections to neural circuits that are 
important for value-based decision-making, including the orbito -
frontal cortex (OFC)83. Disruptions of the PRH can disrupt object value 
Activity after gaze 
shifts resulting innovel objects
050Firing rate of a single neuron in
the ZI (spikes s–1)
100 msPresentation
of novel object Saccade to
familiar objectPresentation of familiar object
in the periphery
that predictsfuture novelty
Time (ms)Activity before
gaze shifts resulting in
novel objects
Activity after gaze shifts
resulting in familiar objectsa b
Novel object trialsFamiliar object trialsPRH
ZINoveltyprediction
network
Superior
colliculusLateralhabenulaSNC OFC
Control of
orientingand attentionPredictionerror Objectvalue
Dynamic control of learningand decision-making
Activity before gaze shifts resulting in familiar objects
Fig. 3 | Neural network for novelty seeking in the primate brain. A recent study 
has shown that, in the primate, the perirhinal cortex (PRH) broadcasts novelty 
predictions that are utilized by the zona incerta (ZI) to drive novelty seeking18.  
a, Activity of a single ZI neuron during a behavioural task that allows for the study 
of novelty seeking. In the raster  plot, each tick indicates the time of the action 
potential fired by the neuron, and each row is a single trial. The neuron’s activity 
is also displayed below the rasters as spike density functions summarizing the 
neuron’s activity across many trials (red and blue traces). Key task events are shown at the top and demarcated by vertical lines. In the novelty-seeking task, one of several familiar objects appeared in the periphery of the screen viewed by the primate. These either predicted the opportunity to gaze at a novel object  
in the future or predicted the opportunity to gaze at another future familiar object. To gain the opportunity to gaze at the predicted future novel or familiar objects, 
monkeys made gaze shifts to the initial peripheral objects. After the animals  
were trained and understood the structure of the task, neural activity in PRH,  
ZI and in many other brain regions was recorded. During the presentation of  
the first familiar objects, many neurons in the PRH signalled the prediction  
of future novelty earlier than those in the ZI, but did not strongly signal  
additional information needed to control novelty-seeking eye movements
18.  
By contrast, many recorded ZI neurons signalled novelty predictions and motor 
commands necessary to regulate novelty seeking. This is shown by the example neuron in panel a  which robustly anticipated the timing of gaze shifts towards 
novelty-predicting familiar objects. Activity in red is from trials in which a prediction was made that a gaze shift would result in an opportunity to  
view a novel object, whereas activity shown in blue is from trials in which  
a prediction was made that a gaze shift would result in an opportunity to gaze  
at a familiar object (for simplicity, rasters above the spike density functions  
only show activity during novelty-predicting trials). When the same trials occurred on the ipsilateral side (relative to the neural recording), many ZI neurons did not display these novelty or movement related  modulations  
(data not shown), indicating that the ZI contained spatial information to guide eye movements as well as predictions of future novel objects. b , A neural network 
including the PRH and the ZI for the control of novelty seeking. PRH can directly or indirectly send novelty predictions to the ZI (dashed line). These can regulate eye movements, attention and orienting directly through the ZI–superior colliculus circuit
40,75 to valueless novel objects. This network can also modulate 
the circuits of decision-making and learning by sending novelty prediction and novelty-seeking action-related information to the lateral habenula–substantia nigra pars compacta (SNC) reward prediction error network and to the 
orbitofrontal cortex (OFC) that signals the values of decision offers and objects, 
which together function to regulate decision-making
4,40,83–85,91,121, 153. I propose 
that these circuits allow novelty-seeking predictions and actions to influence (or ‘bonus’
62) prediction errors and object valuation processing in order to 
adaptively mediate the behavioural interactions among novelty and reward  
value in a flexible context-dependent manner40. Part a  is adapted from ref.  18,  
Springer Nature Ltd.
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 202
Review articlelearning84,85. Therefore, the PRH–OFC circuit is, thus, another impor-
tant candidate circuit through which novelty predictions can impact 
value-based behaviour.
Uncertainty: representing and reducing
Another component of curiosity is the desire to know what future 
appetitive or aversive events or outcomes will be encountered, even 
if this information cannot be used to gain more reward or to optimize task performance in the foreseeable future. The neural pathways and algorithms that support this motivation and mediate the valuation of 
outcome uncertainty-reducing information have only recently begun 
to be uncovered.
In theory, there are at least two types of outcome uncertainty 
or variance that organisms and artificial agents must learn to 
negotiate15,86–89, both of which are usually present in naturalistic set -
tings. The first is aleatoric uncertainty, sometimes called expected 
uncertainty, which is uncertainty that arises because of irreducible 
noise or variability in an outcome distribution. The second is epistemic 
uncertainty, which often arises because of lack of knowledge or train-ing, or because of environmental volatility. Both types of uncertainty 
could modulate curiosity and instrumental and non-instrumental infor -
mation seeking3,4,90,91. However, their contributions to behavioural con -
trol are not well understood, particularly at the level of neural circuits. 
Qualitative comparisons across previously published studies suggest 
that some brain regions may be more sensitive to expected uncertainty, 
whereas others can be relatively insensitive to the source or type of 
uncertainty92,93; however, how these distinct sensitivities contribute 
to behaviour remains unknown and whether the brain can efficiently 
parse them is an area of ongoing research. A related complexity can 
be appreciated in machine learning algorithms. Whereas some algo -
rithms try to distinguish between types of uncertainty94 (for example, 
by measuring total uncertainty from the frequency and magnitude of all 
surprising rewards and then attempting to infer which are attributable 
to a lack of knowledge95), others avoid uncertainty measurements and 
use different strategies to guide behaviour15,87,96–98. It will be important 
to identify the distinct information-seeking strategies that the many 
forms of uncertainty elicit to discover the underlying neural mecha -
nisms4. To match to current knowledge, in the sub-sections that follow,  
I concentrate on studies that link neural activity to the non-instrumental motivation to reduce uncertainty defined as outcome variance in highly 
controlled settings.
Representation of outcome uncertainty in the primate
Neural systems that have evolved to support uncertainty estimation to 
regulate curiosity must function continuously and in a manner that is 
not necessarily time locked to either the state of the external world or to 
motor planning and execution. Furthermore, they must often process 
a long history of sensory, action and motivational events, and general -
ize across tasks and contexts. Theoretical work suggests that, when 
learning to perform many tasks, explicit representations of abstract 
variables (such as uncertainty) could be useful for task performance and 
task acquisition99–102. The notion of explicit representation of outcome 
uncertainty in the firing rates of individual neurons may seem to contra -
dict the observation that sensory uncertainty (that is, noise in incom-ing sensory data) is encoded at the population level
88,103 ,104. However, 
further consideration reveals that the functional differences between 
sensory and higher-order cognitive systems place different demands 
on neural coding, resulting in distinct uncertainty-representation 
strategies.Brain areas involved in the earliest stages of sensory processing 
contain neurons with particularly low intrinsic timescales that reliably 
and rapidly signal modality-specific incoming information about sen -
sory inputs, in a stimulus time-locked manner51,53,105. To maintain such 
speed and reliability, it has been proposed that these early sensory  
processing circuits do not explicitly compute second-order statis -
tics103 ,104 ,106, such as estimations of sensory uncertainty, but rather 
reflect information about incoming stimulus uncertainty or variability 
at the population level88,103.
The situation in higher-order brain systems is different. Signals 
that relate to the uncertainty of upcoming rewards and punishments 
have been observed at the level of single-neuron firing rates in several 
primate brain regions (for review, see ref. 15 ). Among these, neurons 
in an interconnected network that includes the anterior cingulate 
cortex (ACC, also known as area 24), the ventral lateral prefrontal cor-tex (VLPFC, also known as area 12o/47) and the basal ganglia have also 
been linked to information seeking and uncertainty reduction16,70. 
This has been referred to as an information-seeking network because 
these neurons are not solely dedicated to signalling or discriminating 
uncertainty: they are also involved in motivating actions to gain infor -
mation to resolve it107 ,108 (Fig. 4 ). Uncertainty-related activity across the 
information-seeking network predicts the moment at which subjects 
will gain information16,109 (Fig. 4 ). This anticipatory activity fluctu -
ates with information-seeking behaviour on a moment-by-moment 
basis, and inactivating the basal ganglia areas in this network impairs 
information-seeking gaze shifts. This tight linkage between activity 
in the information-seeking network and behaviour may explain why 
neurons in this network reflect uncertainty at the single-neuron level: 
it can be hypothesized that as the relationship of a neuron with the 
decision output (or action) increases, so does the compression of 
high-dimensional data to a lower-dimensional scalar code that can 
directly impact behaviour. The idea is that neurons that are closer to 
the output of a decision-making process must filter out much of the 
detailed information used to make the decision and instead focus on 
encoding the result. Related ideas have been proposed for networks 
of brain regions involved in visual processing and motor control110 –114. 
The ACC and VLPFC can both richly encode higher-order statistical 
information (such as uncertainty) at the level of neural populations 
and can signal outcome uncertainty at the single-neuron level16,108 ,115– 117. 
One speculation that would require further testing is that the preva -
lence of this compression at the single neuron level (resulting in data 
loss about distributions of outcomes) may increase in the network 
as uncertainty-related information is transmitted through the basal 
ganglia in the service of behavioural control.
Not all regions in the information-seeking network perform 
the same function. During anticipation of uncertainty resolution,  
a relatively categorical signal (which indicates whether a state is uncer -
tain or certain) is first observed in the pallidal regions of the basal 
ganglia, whereas a more graded signal that discriminates levels of 
uncertainty emerges later in the cortex109. The cortex leads the basal 
ganglia in predicting gaze shifts to resolve uncertainty, suggesting 
that it has a relatively more pronounced or earlier role in the planning 
of information seeking. Many neurons in the ACC reflect informa -
tion preference in a valence-sensitive manner, signalling uncertainty 
reduction about rewards or about punishments, whereas a significant 
proportion of VLPFC neurons reflect the total information prefer -
ence of the subject across both reward and punishment uncertainty, 
in a valence-independent manner16. Overall, these differences sug -
gest that there are at least two cooperative mechanisms that support 
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 203
Review articleinformation seeking: one that rapidly detects uncertainty and one that 
guides uncertainty resolution, and that information seeking about 
rewards and punishments may be dissociable at the level of neural 
circuits and behaviour.
Single neurons in the uncertainty reduction network (Fig.  4) that 
mediate information seeking lack strong oculomotor or spatial signals. 
Therefore, an important question would be: how do they ultimately 
guide eye movements? A possible candidate mechanism has been 
identified by Gottlieb and her colleagues in the parietal cortex — a 
brain region that displays highly spatially selective responses that 
guide  attention and orienting6,17,118– 120. An important area of future 
research must, therefore, be identifying the circuits through which 
the parietal cortex and other oculomotor and motor control circuits  
interact with the uncertainty reduction network (Fig.  4) to medi -
ate attention and gaze to resolve uncertainty (see ref. 15  for further 
discussion). Along these lines, anterior ZI receives inputs from the 
uncertainty reduction network121 and has been relatively unexplored 
in our studies of perceptual novelty seeking (Fig.  3). On this basis, we 
recently suggested a functional gradient within the ZI which must 
now be experimentally assessed40. This organization can serve as a substrate for the interaction among novelty seeking and uncertainty 
reduction within the ZI. 
Assigning value to uncertainty reduction
The valuation of non-instrumental information is essential to guide 
multi-attribute decisions that resemble our daily behaviour in which 
both the cognitive reward of reducing uncertainty and physical rewards 
must be considered. Although information seeking has been studied in humans and animals, little is known about the computational rules that their brains use to assign value to non-instrumental information, 
and whether these rules are conserved across species. A few studies 
have now begun to uncover the neural mechanisms underlying such 
valuation across species.
A landmark study discovered that dopamine neurons signal 
unexpected opportunities to gain information to resolve reward uncertainty
122. This result appeared to be consistent with older 
hypotheses that suggested that the brain utilizes uncertainty signals 
to generate information value signals that govern information-seeking 
behaviour (for detailed review of behavioural studies and theories, 
see refs. 4 ,12,122 –124). To answer this question in the context of multi • Signal value of information (VI) for 
decision-making
• Motivate sustained pursuit of information
• In/f.shortluence gaze to gather information
• Prepare to learn from information
Gaze towards 
uncertainty-
resolving information
Gaze away from 
uncertainty-resolving informationGaze shift-related activirty–0.5
Time after gaze shift (s)0.5 00
–0.20.2a 
Striatum (icbDS)
PallidumACC (a24)
VLPFC (a12)
c
0.50.9Normalized uncertainty 
signal (discrimination)
0.5 sTime (s)b
Oﬀer presentation Cue presentation Outcome
Informative oﬀer
Non-informative oﬀerReward cue
Non-informative cueNo-reward cue100% reward
50% rewardNo reward
Predicts uncertainty-
resolving informationby visual cuePredicts uncertainty-
resolving information 
by outcomeFunctions of the network Uncertainty reduction network
Fig. 4 | Neural network for uncertainty reduction in the primate brain.   
a, Regions within the neural network involved in uncertainty reduction. These 
include the anterior cingulate cortex (ACC, also known as area 24), the ventral 
lateral prefrontal cortex (VLPFC, also known as area 12), the internal capsule 
bordering the dorsal striatum (icbDS), and the anterior pallidum4,15,16,109, which 
also projects to the lateral habenula (Fig. 5 ). Arrows indicate heterogenous but 
mostly excitatory connectivity between brain regions, whereas blunt-ended 
arrows indicate connections that are probably exclusively inhibitory. Proposed 
functions of the neural network are indicated on the right. Those that have 
been tested are shown in grey109. b, In studies investigating this neural network, 
monkeys experienced two types of reward-uncertain offers: informative and non-informative
16,109. Informative offers were followed by informative cues that 
resolved the uncertainty of a monkey about the outcome before the outcome  
was delivered. Non-informative offers were followed by a non-informative  
cue, and monkeys remained in a state of uncertainty until the outcome  
was delivered (reward or no reward). The plot shows the average activity  
of uncertainty-sensitive neurons across several parts of the neural network during trials with informative offers (red) and with non-informative offers (blue). This activity predicts uncertainty resolution by visual cues in informative offer trials and uncertainty resolution by outcomes (reward or no reward) in non-informative offer trials. The plot shows how the normalized uncertainty 
signal (y -axis), which quantifies the neural discriminability of uncertainty, 
changes over time (x -axis) and is aligned with specific events (shown above) 
in trials with informative and non-informative offers. c , Activity in several 
parts of the neural network shown in part a  (specifically the ACC and the 
basal ganglia) has a moment-by-moment relationship with information 
seeking (VLPFC was not tested in that study). This activity increased before gaze shifts that resolved uncertainty and resulted in information (red curve) 
and decreased before gaze shifts that avoided uncertainty reduction (blue 
curve). The implementation of this motivational signal probably occurs through interactions of this neural network with oculomotor and motor control circuits (Fig.  1), such as in the parietal cortex
6,119, and perhaps with circuits in the 
superior colliculus (Fig. 3 ) and the frontal eye fields. Parts b  and c  are adapted 
from ref. 109 , Springer Nature Ltd.
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 204
Review articleattribute decision making, we recently  directly examined how the brain 
assigns value to information while recording neuronal activity in the 
output node of the information-seeking network — the pallidum — and 
in a major recipient of pallidal inputs that is also known to regulate 
dopamine neurons — the LHb125– 129. In this study, monkeys and humans 
were taught to choose between offers associated with reward distri -
butions with varying degrees of uncertainty and expected reward91. 
The subjects knew that some offers were informative, meaning that 
if they chose those offers information about precisely which reward  
they would get in the future would be provided early. Conversely, if they 
chose non-informative offers, they had to wait in a state of ignorance 
until the outcome was revealed to them later in the trial. This made it 
possible to measure the value that individuals assigned to information 
by measuring how much reward they were willing to forgo to obtain 
it earlier. This revealed an intuitive and relatively conserved principle: 
higher value was assigned to information when the outcomes of the 
decision are uncertain. That is, when reward uncertainty is high, both 
humans and monkeys are willing to forgo large amounts of finan -
cial reward (in the case of humans) or juice reward (in the case of mon -
keys) to obtain early information about which of the uncertain rewards 
they would obtain at the end of the trial. Furthermore, both humans 
and monkeys computed this information value in remarkably consis-
tent ways, valuing information based on similar mathematical forms of uncertainty. The algorithm for valuation of information was consistent, 
despite large variations in species’ and individuals’ attitudes towards 
risk. In both species, the value of information was also highest when 
information was predicted to come early in advance of the outcome.
The same study also tested how the factors that most consistently 
and strongly influenced information value — uncertainty and time — 
influenced information-related activity in the LHb and in the pallidum 
(Fig.  5). Although monkeys integrated expected value, uncertainty, 
time, and informativeness in their choices, many single pallidum 
neurons did not integrate all of these attributes. For example, some 
pallidum neurons signalled information value but not reward value, 
whereas others signalled the reverse. The same was true for another 
major input to the LHb, the subthalamic nucleus. By contrast, many 
or most LHb neurons reflected the full integration of information, 
reward, uncertainty, and time into a common currency of economic or 
total value of the decision offers. Furthermore, variations in this LHb 
activity both predicted and causally influenced the decisions on a 
trial-by-trial basis.
How do single LHb neurons acquire this integrated signal and 
how do they impact ongoing decision-making? A candidate mech -
anism involves the integration of signals from multiple pallidum and/or subthalamic nucleus neurons which  transmit signals from the cortical–striatal network to the LHb. This notion is consistent 
with the observation that cortical neurons in key regions involved in 
guiding value-based decisions, particularly within the OFC, can also 
signal subjective value in a relatively distributed manner, with differ-ent neurons signalling the values of physical reward and the value of 
information130 ,131. Pallidum neurons are notably capable of co-release132, 
which may further facilitate additional flexibility in the pallidum–LHb 
circuit that generates a subjective value signal, though the underlying Pallidum
0 11060Firing rate of a single neuron in
the lateral habenula (spikes s–1)
0 1
 0 1Safe UncertainReward Uncertainty Information Time ...
Single
attributes
Partialintegration
Full 
integration(total value)
Small reward
Big reward
No information
 Information
Time after decision
oﬀer onset (s)Time after decision 
oﬀer onset (s)Time after decision
oﬀer onset (s)a
bVI VPR
Single
habenulaneuron
Value of physical reward 
(VPR)Value of information
(VI)Uncertaintyreduction
networkFig. 5 | Neural network for integrating the value of information with the 
value of physical reward in the primate brain. Decision offer attributes, such as expected reward, the time that will elapse before reward is received, and uncertainty, are processed and begin to be integrated and weighted by the 
subject’s preferences in the cortical–basal ganglia circuitry
91 including within  
the uncertainty reduction network (Fig.  4). a, Neurons in the cortical–basal ganglia 
circuit are shown as circles in a neural network. Neurons in the pallidum often 
reflect either the value of information or the value of physical reward, though a 
minority can signal both. The lateral habenula (final row) commonly integrates 
the value of information and the value of physical reward on a single-neuron  
level into a total value to guide choice. Future work must compare the levels of integration, as well as prevalence of different representational mechanisms (for example, population versus scalar) along the entire cortical–basal ganglia information seeking network (Fig. 4 ). b, The integration of multiple attributes 
into a total value signal is illustrated in the responses of an example neuron 
recorded in the lateral habenula during multi-attribute decision-making in which 
monkeys traded physical reward for uncertainty reducing information. In this task animals considered reward, uncertainty, information, and time. This neuron reflected their total value. For simplicity we show here the coding of physical reward and the value of information. The activation of this neuron is shown over time relative to the onset of decision offers (x -axis). Its firing rate (y -axis) reflects 
both the subjective value of both physical reward (which increases with size) and 
the value of information (which goes up with uncertainty)
91. Note that the lateral 
habenula signals value negatively, oppositely to the preferences of the monkeys, 
such that small rewards and uninformative uncertain offers elicit the higher activation. These negative value signals from the lateral habenula are thought 
to undergo sign flipping, through the brainstem, to guide behaviour through the 
control of neuromodulators
154. Figure adapted from ref. 91, Springer Nature Ltd.
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 205
Review articlemechanisms remain unclear and remains under investigation. An 
additional candidate mediator of multi attribute decisions, may be the 
raphe nucleus: a recent preprint reported that this region also contains 
neurons that can scale their activity with subjective value of decision 
offers133. Future experiments will be required to assess whether and 
how raphe neurons modulate the capacity of LHb neurons to integrate 
and learn from value-related inputs. A second important question that 
requires further investigation is through which circuitry could the LHb 
influence decisions. The LHb is a powerful modulator of dopaminergic 
and serotonergic neurons, and, the LHb could theoretically influence 
choices on fast and slow timescales through these mechanisms or 
pathways that include co-release of dopamine and glutamate by 
dopamine neurons67,134– 136. These questions must now be answered 
by cross-species interrogations of LHb circuitry.
Outstanding questions and future directions
Primates and many other animals display a lifelong attentional bias for 
novelty (Box 2). Neurons in the ventral posterior basal ganglia have a 
critical role in signalling the importance of objects and are sensitive to 
object novelty137. They receive direct projections from the PRH and are 
modulated by a unique population of caudal lateral substantia nigra 
dopamine neurons that are believed to signal various forms of behav -
ioural salience and are resistant to short-term changes in value50,64,69. 
These dopamine neurons receive projections from the ZI138 ,139. One 
possible substrate for persistent novelty preference across lifetimes of 
individual primates in oculomotor behaviour is the PRH–basal ganglia 
loop. Testing this hypothesis will require a deeper understanding of the 
synaptic functions of neurons in the ventral posterior basal ganglia140 
and their interactions with the PRH and ZI.
Another important issue is to understand what behavioural vari-
ables neuromodulators, and particularly dopamine neurons, signal 
across development. As a developing animal’s world model becomes 
more sophisticated with time, an important question is: how do predic -
tion errors in dopamine neurons in medial SNC and salience signals in 
dopamine neurons in caudal and lateral SNC change across time and 
development? One possibility is that, early in post-natal development, 
both group of dopamine neurons signal some forms of salience or sur -
prise, and that the medial SNC prediction error system later acquires 
the capacities to transmit a signed value-based prediction error. This 
possibility along with how object novelty processing across the SNC 
changes across time requires further attention. 
In social settings, how does supervision from other agents sup -
port curiosity and how do we build biology-inspired machine learning 
algorithms based on multi-agent supervision? Box  2 discusses a set 
of hypotheses that suggest that, in early postnatal development in 
primates, the visual system and other sensory processes that support 
orienting maximize novelty and surprise to enable efficient statistical 
learning through curiosity-related behaviour. During this time, the 
agent receives guidance from other individuals to prevent undesirable 
outcomes (for example, restricting curiosity and exploration to par -
ticular contexts and locations). As agents develop more sophisticated 
world models, their information-seeking strategies also become more 
sophisticated and they begin to go beyond maximizing surprise and 
novelty and also seek advance information to resolve their uncertainty 
and update their beliefs. This is facilitated by processes that assign 
value to information and integrate this value with value of physical 
rewards and punishments. Long postnatal development, socially 
cooperative and competitive environments, and multiple learning 
and effector systems (guided by distinct objective functions) could be crucial components of curiosity, and of primate curiosity in particular. 
Investigating these ideas at the neurobiological level will shed light on 
the neural basis of curiosity and support the development of machines 
that aim to emulate human intelligence.
Novelty detection and predictions must impact uncertainty esti-
mation. At the same time, it is possible that uncertainty estimation 
could drive gain modulation in sensory processing and/or boost nov -
elty prediction (akin to attention13,141). At this time, the behavioural 
algorithms or neural mechanisms involved in these important inter -
actions remain poorly understood. Further inquiry may be guided 
by the probable difference in the timescales of novelty detection and uncertainty measurement. As discussed elsewhere, novelty detection 
can occur in a relatively bottom–up sensory-evoked manner (Fig. 1 ). 
By contrast, uncertainty estimation and reduction can be incremental, 
computationally demanding, and may evolve over longer timescales. 
Therefore, novelty detection is well positioned to act as a short latency 
controller of attentional processing, participating in the early selection 
of relevant stimuli that may then be further interpreted, including to 
understand and infer the statistics of complex environments,  including 
their uncertainty.
A related question is whether there is a general controller for 
curiosity in the mature primate brain. Novelty seeking and investiga-tion and information seeking to reduce uncertainty are components of a curiosity phenotype. However, these motivations are not always 
correlated, that is, humans or other animals can be highly motivated to 
seek information, aiming to reduce their reward uncertainty, but may 
avoid novelty, or vice versa. Similarly, attitudes towards the resolution 
of reward and punishment uncertainty can be diverse16, with some 
subjects preferring the resolution of one and not the other.
The novelty of places or spaces is processed by a network of brain 
areas involved in declarative memory, such as the hippocampus and the 
mammillary region of the hypothalamus28,37,142 –148. Contextual novelty 
seems to engage similar or at least partially overlapping circuits37,49,143. 
Understanding how different forms of novelty (object, place and con -
text) are encoded and used to guide behaviour will shed further light 
on the mechanisms of curiosity, and also on the formation of cognitive 
maps and their behavioural utility20,49,149 ,150.
Genetic (or epigenetic) programming alone cannot produce com -
plex cognitive behavioural policies or impart knowledge of volatile 
environmental statistics151. Instead, genetics constrains neural circuits 
and provides the necessary architectures and computational capacity 
to support complex behaviours that make up curiosity. A starting point 
for assessing the relationship between genetics and curiosity could be 
to ask what circuit properties support uncertainty estimation. Uncer -
tainty estimation is dependent on the ability to integrate surprising 
events over long timescales. Assessing the genetic basis of the hier -
archy of timescales in the cortex53 may, thus, provide important hints 
regarding the mechanisms of uncertainty estimation and information 
seeking across development and evolution.
It goes without saying that curiosity is not only a form of uncer -
tainty intolerance. If it were, there would be a strong relationship 
between risk aversion and the willingness to pay for information91. 
Instead, curiosity may have a critical role in the formation of beliefs 
and world models, a process that itself can be rewarding to humans and  
possibly other animals14. Therefore, multiple mechanisms tend to 
mediate information seeking and curiosity. To understand these 
underlying biological mechanisms, we need to ascertain curiosity’s 
multiple behavioural and emotional sources or ‘motivators’ . For exam -
ple, recent work in machine learning and psychology suggests that 
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 206
Review articlethe desire to learn and to monitor one’s own learning progress12,72,152 
are additional important sources of curiosity-related behaviour. Are 
these simply another form of uncertainty reduction or are they distinct 
forms of curiosity-related behaviour? Furthermore, internal states that 
evolve on long timescales, such as emotions or moods, probably have 
a powerful influence on curiosity, information seeking and novelty 
seeking, but how remains unclear.
Our desire to acquire knowledge and interact with novelty persists 
despite the absence of immediate or apparent benefits. However, it 
may confer advantages over the course of a lifetime (Box 1), enhancing 
adaptive foraging and increasing opportunities for species survival 
in complex environments. It will, therefore, be important to study 
curiosity-related behaviours at the evolutionary level, strongly relying 
on approaches advanced in computational biology and theory. This 
work may offer key insights into why information seeking and sensitivity 
to novelty are such enduring features of mammalian behaviour.
Published online: 23 January 2024
References
1. Glimcher, P. W. & Fehr, E. (eds) Neuroeconomics: Decision Making and the Brain 2nd edn 
(Academic, 2013).
2. Padoa-Schioppa, C. & Cai, X. The orbitofrontal cortex and the computation of subjective value: consolidated concepts and new perspectives. Ann. N. Y. Acad. Sci. 1239, 130–137 
(2011).
3. Costa, V. D., Mitz, A. R. & Averbeck, B. B. Subcortical substrates of explore-exploit decisions in primates. Neuron 103, 533–545 (2019).
4. Bromberg-Martin, E. S. & Monosov, I. E. Neural circuitry of information seeking.  
Curr. Opin. Behav. Sci. 35, 62–70 (2020).
5. Bennett, D., Bode, S., Brydevall, M., Warren, H. & Murawski, C. Intrinsic valuation of information in decision making under uncertainty. PLoS Comput. Biol. 12, e1005020 (2016).
6. Gottlieb, J., Cohanpour, M., Li, Y., Singletary, N. & Zabeh, E. Curiosity, information demand and attentional priority. Curr. Opin. Behav. Sci. 35, 83–91 (2020).
7. Eliaz, K. & Schotter, A. Experimental testing of intrinsic preferences for noninstrumental 
information. Am. Econ. Rev. 97, 166–169 (2007).
8. Berlyne, D. E. Novelty and curiosity as determinants of exploratory behaviour. Br. J. 
Psychol. 41, 68–80 (1950).
9. Loewenstein, G. The psychology of curiosity: a review and reinterpretation. Psychol. Bull.  
116, 75–98 (1994).
10. Byrne, R. W. Animal curiosity. Curr. Biol. 23, R469–R470 (2013).
11. van Lieshout, L. L., de Lange, F. P. & Cools, R. Why so curious? Quantifying mechanisms of information seeking. Curr. Opin. Behav. Sci. 35, 112–117 (2020).
12. Gottlieb, J., Oudeyer, P.-Y., Lopes, M. & Baranes, A. Information-seeking, curiosity, and 
attention: computational and neural mechanisms. Trends Cogn. Sci. 17, 585–593 (2013).
13. Gottlieb, J., Hayhoe, M., Hikosaka, O. & Rangel, A. Attention, reward, and information 
seeking. J. Neurosci. 34, 15497–15504 (2014).
14. Bromberg-Martin, E. S. & Sharot, T. The value of beliefs. Neuron 106, 561–565 (2020).
15. Monosov, I. E. How outcome uncertainty mediates attention, learning, and decision-making. Trends Neurosci. 43, 795–809 (2020).
16. Jezzini, A., Bromberg-Martin, E. S., Trambaiolli, L. R., Haber, S. N. & Monosov, I. E.  
A prefrontal network integrates preferences for advance information about uncertain rewards and punishments. Neuron 109, 2339–2352.e5 (2021).
17. Gottlieb, J. Emerging principles of attention and information demand. Curr. Dir. Psychol. Sci. 32, 152–159 (2023).
18. Ogasawara, T. et al. A primate temporal cortex–zona incerta pathway for novelty seeking. 
Nat. Neurosci. 25, 50–60 (2022).
19. Jaegle, A., Mehrpour, V. & Rust, N. Visual novelty, curiosity, and intrinsic reward in 
machine learning and the brain. Curr. Opin. Neurobiol. 58, 167–174 (2019).
20. Zhang, K., Chen, C. D. & Monosov, I. E. Novelty, salience, and surprise timing are signaled by neurons in the basal forebrain. Curr. Biol. 29, 134–142.e3 (2019).
21. Tiitinen, H., May, P., Reinikainen, K. & Näätänen, R. Attentive novelty detection in humans 
is governed by pre-attentive sensory memory. Nature 372, 90–92 (1994).
22. Tapper, A. R. & Molas, S. Midbrain circuits of novelty processing. Neurobiol. Learn. Mem.  
176, 107323 (2020).
23. Anderson, B., Mruczek, R. E. B., Kawasaki, K. & Sheinberg, D. Effects of familiarity on 
neural activity in monkey inferior temporal lobe. Cereb. Cortex  18, 2540–2552 (2008).
24. Joshua, M., Adler, A. & Bergman, H. Novelty encoding by the output neurons of the basal 
ganglia. Front. Syst. Neurosci. 3, 20 (2010).
25. Xiang, J.-Z. & Brown, M. Differential neuronal encoding of novelty, familiarity and recency 
in regions of the anterior temporal lobe. Neuropharmacology 37, 657–676 (1998).
26. Bogacz, R., Brown, M. W. & Giraud-Carrier, C. Model of co-operation between recency, familiarity and novelty neurons in the perirhinal cortex. Neurocomputing 38, 1121–1126 
(2001).27. Ghazizadeh, A., Griggs, W. & Hikosaka, O. Ecological origins of object salience: reward, uncertainty, aversiveness, and novelty. Front. Neurosci. 10, 378 (2016).
28. Barto, A., Mirolli, M. & Baldassarre, G. Novelty or surprise? Front. Psychol. 4, 907 (2013).
29. Butler, R. A. Discrimination learning by rhesus monkeys to visual-exploration motivation. J. Comp. Physiol. Psychol. 46, 95–98 (1953).
30. Wang, T. & Mitchell, C. J. Attention and relative novelty in human perceptual learning.  
J. Exp. Psychol. Anim. Behav. Process. 37, 436–445 (2011).
31. Akiti, K. et al. Striatal dopamine explains novelty-induced behavioral dynamics and 
individual variability in threat prediction. Neuron 110, 3789–3804.e9 (2022).
32. Pai, J. & Monosov, I. E. Dopamine in the rodent tail of striatum regulates behavioral 
variability in response to threatening novel objects. Neuron 110, 3653–3655 (2022).
33. Kelley, A. E., Schochet, T. & Landry, C. F. Risk taking and novelty seeking in adolescence: introduction to part I. Ann. N. Y. Acad. Sci. 1021, 27–32 (2004).
34. Hartley, C. A. & Somerville, L. H. The neuroscience of adolescent decision-making.  
Curr. Opin. Behav. Sci. 5, 108–115 (2015).
35. Nussenbaum, K. et al. Novelty and uncertainty differentially drive exploration across development. eLife 12, e84260 (2022).
36. Traner, M. R., Bromberg-Martin, E. S. & Monosov, I. E. How the value of the environment controls persistence in visual search. PLoS Comput. Biol. 17, e1009662 (2021).
37. Kumaran, D. & Maguire, E. A. Which computational mechanisms operate in the 
hippocampus during novelty detection? Hippocampus 17, 735–748 (2007).
38. Djamshidian, A., O’Sullivan, S. S., Wittmann, B. C., Lees, A. J. & Averbeck, B. B. Novelty 
seeking behaviour in Parkinson’s disease. Neuropsychologia 49, 2483–2488 (2011).
39. Costa, V. D., Tran, V. L., Turchi, J. & Averbeck, B. B. Dopamine modulates novelty seeking behavior during decision making. Behav. Neurosci. 128, 556–566 (2014).
40. Monosov, I. E., Ogasawara, T., Haber, S. N., Heimel, J. A. & Ahmadlou, M. The zona incerta 
in control of novelty seeking and investigation across species. Curr. Opin. Neurobiol. 77, 
102650 (2022).
41. Miljković, D. Review of novelty detection methods. In Proc. The 33rd International 
Convention MIPRO 593–598 (IEEE, New York, 2010).
42. Nguyen, D., Kirsebom, O. S., Frazão, F., Fablet, R. & Matwin, S. Recurrent neural networks 
with stochastic layers for acoustic novelty detection. In Proc. ICASSP 2019-2019 IEEE 
International Conference on Acoustics, Speech and Signal Processing (ICASSP) 765–769 (IEEE, New York, 2019).
43. Marchi, E., Vesperini, F., Squartini, S. & Schuller, B. Deep recurrent neural network-based 
autoencoders for acoustic novelty detection. Comput. Intell. Neurosci. 2017, 4694860 
(2017).
44. Bogacz, R. & Brown, M. W. An anti-Hebbian model of familiarity discrimination in the perirhinal cortex. Neurocomputing https://doi.org/10.1016/S0925-2312(02)00738-5 
(2003).
45. Bogacz, R. & Brown, M. W. Comparison of computational models of familiarity discrimination in the perirhinal cortex. Hippocampus 13, 494–524 (2003).
46. Tyulmankov, D., Yang, G. R. & Abbott, L. Meta-learning synaptic plasticity and memory 
addressing for continual familiarity detection. Neuron 110, 544–555 (2021).
47. Dasgupta, S., Sheehan, T. C., Stevens, C. F. & Navlakha, S. A neural data structure for novelty detection. Proc. Natl Acad. Sci. USA 115, 13093–13098 (2018).
48. Kording, K. P., Tenenbaum, J. B. & Shadmehr, R. The dynamics of memory as a consequence 
of optimal adaptation to a changing body. Nat. Neurosci. 10, 779–786 (2007).
49. Zhang, K., Bromberg-Martin, E. S., Sogukpinar, F., Kocher, K. & Monosov, I. E. Surprise  
and recency in novelty detection in the primate brain. Curr. Biol. 32, 2160–2173.e6 (2022).
50. Hikosaka, O. et al. Multiple neuronal circuits for variable object–action choices based  
on short- and long-term memories. Proc. Natl Acad. Sci. USA 116, 26313–26320 (2019).
51. Spitmaan, M., Seo, H., Lee, D. & Soltani, A. Multiple timescales of neural dynamics and integration of task-relevant signals across cortex. Proc. Natl Acad. Sci. USA 117, 
22522–22531 (2020).
52. Bromberg-Martin, E. S., Matsumoto, M., Nakahara, H. & Hikosaka, O. Multiple timescales of memory in lateral habenula and dopamine neurons. Neuron 67, 499–510 (2010).
53. Murray, J. D. et al. A hierarchy of intrinsic timescales across primate cortex. Nat. Neurosci.  
17, 1661–1663 (2014).
54. Cavanagh, S. E., Wallis, J. D., Kennerley, S. W. & Hunt, L. T. Autocorrelation structure at 
rest predicts value correlates of single neurons during reward-guided choice. eLife 5, 
e18937 (2016).
55. Manea, A. M., Zilverstand, A., Ugurbil, K., Heilbronner, S. R. & Zimmermann, J. Intrinsic timescales as an organizational principle of neural processing across the whole rhesus macaque brain. eLife 11, e75540 (2022).
56. Ghazizadeh, A., Hong, S. & Hikosaka, O. Prefrontal cortex represents long-term memory of object values for months. Curr. Biol. 28, 2206–2217.e5 (2018).
57. Ghazizadeh, A., Griggs, W., Leopold, D. A. & Hikosaka, O. Temporal–prefrontal cortical 
network for discrimination of valuable objects in long-term memory. Proc. Natl Acad. Sci. 
USA 115, E2135–E2144 (2018).
58. Kim, H. F. & Hikosaka, O. Distinct basal ganglia circuits controlling behaviors guided by 
flexible and stable values. Neuron 79, 1001–1010 (2013).
59. Mehrpour, V., Meyer, T., Simoncelli, E. P. & Rust, N. C. Pinpointing the neural signatures of 
single-exposure visual recognition memory. Proc. Natl Acad. Sci. USA 118, e2021660118 
(2021).
60. Hattori, D. et al. Representations of novelty and familiarity in a mushroom body compartment. Cell 169, 956–969.e17 (2017).
61. Smith, M. A., Ghazizadeh, A. & Shadmehr, R. Interacting adaptive processes with different timescales underlie short-term motor learning. PLoS Biol. 4, e179 (2006).
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208
 207
Review article62. Kakade, S. & Dayan, P. Dopamine: generalization and bonuses. Neural Netw. 15, 549–559 
(2002).
63. Cohen, J. Y., Haesler, S., Vong, L., Lowell, B. B. & Uchida, N. Neuron-type-specific  
signals for reward and punishment in the ventral tegmental area. Nature 482, 85–88 
(2012).
64. Matsumoto, M. & Hikosaka, O. Two types of dopamine neuron distinctly convey positive 
and negative motivational signals. Nature 459, 837–841 (2009).
65. Bromberg-Martin, E. S., Matsumoto, M. & Hikosaka, O. Dopamine in motivational control: 
rewarding, aversive, and alerting. Neuron 68, 815–834 (2010).
66. Schultz, W., Dayan, P. & Montague, P. R. A neural substrate of prediction and reward. 
Science 275, 1593–1599 (1997).
67. Schultz, W. Multiple dopamine functions at different time courses. Annu. Rev. Neurosci.  
30, 259–288 (2007).
68. Menegas, W., Babayan, B. M., Uchida, N. & Watabe-Uchida, M. Opposite initialization 
to novel cues in dopamine signaling in ventral and posterior striatum in mice. eLife 6, 
e21886 (2017).
69. Menegas, W., Akiti, K., Amo, R., Uchida, N. & Watabe-Uchida, M. Dopamine neurons projecting to the posterior striatum reinforce avoidance of threatening stimuli.  
Nat. Neurosci. 21, 1421–1430 (2018).
70. Monosov, I. E. Anterior cingulate is a source of valence-specific information about value and uncertainty. Nat. Commun. 8, 134 (2017).
71. Oudeyer, P.-Y., Kaplan, F. & Hafner, V. V. Intrinsic motivation systems for autonomous 
mental development. IEEE Trans. Evolut. Comput. 11, 265–286 (2007).
72. Gottlieb, J., Lopes, M. & Oudeyer, P.-Y. in Recent Developments in Neuroscience Research on Human Motivation Vol. 19 (eds Kim, S.-I. et al.) 149–172 (Emerald, 2016).
73. Schwartenbeck, P., FitzGerald, T., Dolan, R. & Friston, K. Exploration, novelty, surprise, 
and free energy minimization. Front. Psychol. 4, 710 (2013).
74. Dayan, P. & Sejnowski, T. J. Exploration bonuses and dual control. Mach. Learn. 25, 5–22 
(1996).
75. May, P. J. & Basso, M. A. Connections between the zona incerta and superior colliculus in 
the monkey and squirrel. Brain Struct. Funct. 223, 371–390 (2018).
76. Krauzlis, R. J., Lovejoy, L. P. & Zénon, A. Superior colliculus and visual spatial attention. 
Annu. Rev. Neurosci. 36, 165–182 (2013).
77. Ahmadlou, M. et al. A cell type-specific cortico-subcortical brain circuit for investigatory 
and novelty-seeking behavior. Science 372, eabe9681 (2021).
78. Everitt, B. J. & Robbins, T. W. Central cholinergic systems and cognition. Annu. Rev. Psychol. 48, 649–684 (1997).
79. Houillon, A. et al. The effect of novelty on reinforcement learning. Prog. Brain Res. 202, 
415–439 (2013).
80. Lak, A., Stauffer, W. R. & Schultz, W. Dopamine neurons learn relative chosen value from probabilistic rewards. eLife 5, e18044 (2016).
81. Matsumoto, M. & Hikosaka, O. Lateral habenula as a source of negative reward signals in dopamine neurons. Nature 447, 1111–1115 (2007).
82. Watabe-Uchida, M., Zhu, L., Ogawa, S. K., Vamanrao, A. & Uchida, N. Whole-brain 
mapping of direct inputs to midbrain dopamine neurons. Neuron 74, 858–873 (2012).
83. Suzuki, W. A. & Amaral, D. G. Perirhinal and parahippocampal cortices of the macaque 
monkey: cortical afferents. J. Comp. Neurol. 350, 497–533 (1994).
84. Murray, E. A. & Richmond, B. J. Role of perirhinal cortex in object perception, memory, and associations. Curr. Opin. Neurobiol. 11, 188–193 (2001).
85. Liu, Z., Murray, E. A. & Richmond, B. J. Learning motivational significance of visual cues 
for reward schedules requires rhinal cortex. Nat. Neurosci. 3, 1307–1315 (2000).
86. Bach, D. R. & Dolan, R. J. Knowing how much you don’t know: a neural organization of 
uncertainty estimates. Nat. Rev. Neurosci. 13, 572–586 (2012).
87. Pulcu, E. & Browning, M. The misestimation of uncertainty in affective disorders. Trends Cogn. Sci. 23, 865–875 (2019).
88. Ma, W. J. & Jazayeri, M. Neural coding of uncertainty and probability. Annu. Rev. Neurosci.  
37, 205–220 (2014).
89. Gold, J. I. & Stocker, A. A. Visual decision-making in an uncertain and dynamic world. Annu. Rev. Vis. Sci. 3, 227–250 (2017).
90. Costa, V. D. & Averbeck, B. B. Primate orbitofrontal cortex codes information relevant for managing explore–exploit tradeoffs. J. Neurosci. 40, 2553–2561 (2020).
91. Bromberg-Martin, E. S. et al. A neural mechanism for conserved value computations 
integrating information and rewards. Nat. Neurosci. 27,159–175 (2024).
92. White, J. K. & Monosov, I. E. Neurons in the primate dorsal striatum signal the uncertainty 
of object–reward associations. Nat. Commun. 7, 12735 (2016).
93. Monosov, I. E. & Hikosaka, O. Selective and graded coding of reward uncertainty by neurons in the primate anterodorsal septal region. Nat. Neurosci. 16, 756–762 (2013).
94. Piray, P. & Daw, N. D. A model for learning based on the joint estimation of stochasticity 
and volatility. Nat. Commun. 12, 6587 (2021).
95. Abdar, M. et al. A review of uncertainty quantification in deep learning: techniques, 
applications and challenges. Inf. Fusion 76, 243–297 (2021).
96. Behrens, T. E., Woolrich, M. W., Walton, M. E. & Rushworth, M. F. Learning the value of information in an uncertain world. Nat. Neurosci. 10, 1214–1221 (2007).
97. Mathys, C. D. et al. Uncertainty in perception and the Hierarchical Gaussian Filter.  
Front. Hum. Neurosci. 8, 825 (2014).
98. Kalman, R. E. A new approach to linear filtering and prediction problems. J. Basic Eng. 82, 
35–45 (1960).
99. Hanson, S. J. & Burr, D. J. What connectionist models learn: learning and representation in connectionist networks. Behav. Brain Sci. 13, 471–489 (1990).100. Christensen, A. J., Ott, T. & Kepecs, A. Cognition and the single neuron: how cell types construct the dynamic computations of frontal cortex. Curr. Opin. Neurobiol. 77, 102630 
(2022).
101. Dubreuil, A., Valente, A., Beiran, M., Mastrogiuseppe, F. & Ostojic, S. The role of population structure in computations through neural dynamics. Nat. Neurosci. 25, 
783–794 (2022).
102. Flesch, T., Juechems, K., Dumbalska, T., Saxe, A. & Summerfield, C. Orthogonal representations for robust context-dependent task performance in brains and neural 
networks. Neuron 110, 1258–1270.e11 (2022).
103. Jazayeri, M. & Movshon, J. A. Optimal representation of sensory information by neural 
populations. Nat. Neurosci. 9, 690–696 (2006).
104. Pouget, A., Drugowitsch, J. & Kepecs, A. Confidence and certainty: distinct probabilistic quantities for different goals. Nat. Neurosci. 19, 366–374 (2016).
105. Schmolesky, M. T. et al. Signal timing across the macaque visual system. J. Neurophysiol.  
79, 3272–3278 (1998).
106. Dekleva, B., Ramkumar, P., Wanda, P., Kording, K. & Miller, L. The neural representation  
of likelihood uncertainty in the motor system. eLife 5, e14316 (2016).
107. Khamassi, M., Quilodran, R., Enel, P., Dominey, P. F. & Procyk, E. Behavioral regulation and the modulation of information coding in the lateral prefrontal and cingulate cortex. Cereb. Cortex  25, 3197–3218 (2015).
108. Stoll, F. M., Fontanier, V. & Procyk, E. Specific frontal neural dynamics contribute to decisions to check. Nat. Commun. 7, 11990 (2016).
109. White, J. K. et al. A neural network for information seeking. Nat. Commun. 10, 5168 (2019).
110. Hong, H., Yamins, D. L., Majaj, N. J. & DiCarlo, J. J. Explicit information for category-  
orthogonal object properties increases along the ventral stream. Nat. Neurosci. 19, 
613–622 (2016).
111. DiCarlo, J. J., Zoccolan, D. & Rust, N. C. How does the brain solve visual object recognition? Neuron 73, 415–434 (2012).
112. Cisek, P. Making decisions through a distributed consensus. Curr. Opin. Neurobiol. 22, 
927–936 (2012).
113. Thura, D. & Cisek, P. Deliberation and commitment in the premotor and primary motor cortex during dynamic decision making. Neuron 81, 1401–1416 (2014).
114. Russo, A. A. et al. Motor cortex embeds muscle-like commands in an untangled population response. Neuron 97, 953–966.e8 (2018).
115. Monosov, I. E. & Rushworth, M. F. Interactions between ventrolateral prefrontal 
and anterior cingulate cortex during learning and behavioural change. 
Neuropsychopharmacology 47, 196–210 (2022).
116. Sallet, J. et al. Expectations, gains, and losses in the anterior cingulate cortex.  
Cogn. Affect. Behav. Neurosci. 7, 327–336 (2007).
117. Quilodran, R., Rothe, M. & Procyk, E. Behavioral shifts and action valuation in the anterior cingulate cortex. Neuron 57, 314–325 (2008).
118. Taghizadeh, B. et al. Reward uncertainty asymmetrically affects information transmission within the monkey fronto-parietal network. Commun. Biol. 3, 594 (2020).
119. Horan, M., Daddaoua, N. & Gottlieb, J. Parietal neurons encode information sampling 
based on decision uncertainty. Nat. Neurosci. 22, 1327–1335 (2019).
120. Daddaoua, N., Lopes, M. & Gottlieb, J. Intrinsically motivated oculomotor exploration 
guided by uncertainty reduction and conditioned reinforcement in non-human primates. 
Sci. Rep. 6, 20202 (2016).
121. Haber, S. N., Lehman, J., Maffei, C. & Yendiki, A. The rostral zona incerta: a subcortical 
integrative hub and potential DBS target for OCD. Biol. Psychiatry 93, 1010–1022 (2022).
122. Bromberg-Martin, E. S. & Hikosaka, O. Midbrain dopamine neurons signal preference for advance information about upcoming rewards. Neuron 63, 119–126 (2009).
123. Gottlieb, J. Attention, learning, and the value of information. Neuron 76, 281–295 (2012).
124. Berlyne, D. E. Uncertainty and conflict: a point of contact between information-theory and behavior-theory concepts. Psychol. Rev. 64, 329–339 (1957).
125. Hong, S. & Hikosaka, O. The globus pallidus sends reward-related signals to the lateral 
habenula. Neuron 60, 720–729 (2008).
126. Haber, S. N., Lynd-Balta, E. & Mitchell, S. J. The organization of the descending ventral 
pallidal projections in the monkey. J. Comp. Neurol. 329, 111–128 (1993).
127. Haber, S. N. & Knutson, B. The reward circuit: linking primate anatomy and human imaging. Neuropsychopharmacology 35, 4–26 (2010).
128. Tooley, J. et al. Glutamatergic ventral pallidal neurons modulate activity of the habenula–
tegmental circuitry and constrain reward seeking. Biol. Psychiatry 
83, 1012–1023 (2018).
129. Wulff, A. B., Tooley, J., Marconi, L. J. & Creed, M. C. Ventral pallidal modulation of aversion processing. Brain Res. 1713, 62–69 (2019).
130. Blanchard, T. C., Hayden, B. Y. & Bromberg-Martin, E. S. Orbitofrontal cortex uses distinct 
codes for different choice attributes in decisions motivated by curiosity. Neuron 85, 
602–614 (2015).
131. Bussell, J. J. et al. Representations of information value in mouse orbitofrontal cortex during information seeking. Preprint at bioRxiv https://doi.org/10.1101/2023.10.13.562291 
(2023).
132. Wallace, M. L. et al. Genetically distinct parallel pathways in the entopeduncular nucleus for limbic and sensorimotor output of the basal ganglia. Neuron 94, 138–152.e5 (2017).
133. Feng, Y.-Y., Bromberg-Martin, E. S. & Monosov, I. E. Dorsal raphe neurons signal  
integrated value during multi-attribute decision-making. Preprint at bioRxiv   
https://doi.org/10.1101/2023.08.17.553745 (2023).
134. Stuber, G. D., Hnasko, T. S., Britt, J. P., Edwards, R. H. & Bonci, A. Dopaminergic terminals in the nucleus accumbens but not the dorsal striatum corelease glutamate. J. Neurosci.  
30, 8229–8233 (2010).
Nature Reviews Neuroscience | Volume 25 | March 2024 | 195–208 208
Review article135. Chuhma, N. et al. Dopamine neurons mediate a fast excitatory signal via their 
glutamatergic synapses. J. Neurosci. 24, 972–981 (2004).
136. Varga, V. et al. Fast synaptic subcortical control of hippocampal circuits. Science 326, 
449–453 (2009).
137. Yamamoto, S., Monosov, I. E., Yasuda, M. & Hikosaka, O. What and where information in 
the caudate tail guides saccades to visual objects. J. Neurosci. 32, 11005–11016 (2012).
138. Ogawa, S. K., Cohen, J. Y., Hwang, D., Uchida, N. & Watabe-Uchida, M. Organization  
of monosynaptic inputs to the serotonin and dopamine neuromodulatory systems.  
Cell Rep. 8, 1105–1118 (2014).
139. Menegas, W. et al. Dopamine neurons projecting to the posterior striatum form an 
anatomically distinct subclass. eLife 4, e10032 (2015).
140. Ghosh, S. & Zador, A. M. Corticostriatal plasticity established by initial learning persists after behavioral reversal. eNeuro https://doi.org/10.1523/ENEURO.0209-20.2021 (2021).
141. Foley, N. C., Jangraw, D. C., Peck, C. & Gottlieb, J. Novelty enhances visual salience 
independently of reward in the parietal lobe. J. Neurosci. 34, 7947–7957 (2014).
142. Dunsmoor, J. E., Campese, V. D., Ceceli, A. O., LeDoux, J. E. & Phelps, E. A. Novelty-  
facilitated extinction: providing a novel outcome in place of an expected threat diminishes recovery of defensive responses. Biol. Psychiatry 78, 203–209 (2015).
143. Thakral, P. P., Sarah, S. Y. & Rugg, M. D. The hippocampus is sensitive to the mismatch  
in novelty between items and their contexts. Brain Res. 1602, 144–152 (2015).
144. Arriaga, M. & Han, E. B. Structured inhibitory activity dynamics in new virtual 
environments. eLife 8, e47611 (2019).
145. Burns, L. H., Annett, L., Kelly, A. E., Everitt, B. J. & Robbins, T. W. Effects of lesions to amygdala, 
ventral subiculum, medial prefrontal cortex, and nucleus accumbens on the reaction to 
novelty: implications for limbic–striatal interactions. Behav. Neurosci. 110, 60 (1996).
146. Knight, R. T. Contribution of human hippocampal region to novelty detection. Nature  
383, 256–259 (1996).
147. Park, A. J. et al. Reset of hippocampal–prefrontal circuitry facilitates learning. Nature 591, 
615–619 (2021).
148. Chen, S. et al. A hypothalamic novelty signal modulates hippocampal memory. Nature  
586, 270–274 (2020).
149. Samborska, V., Butler, J. L., Walton, M. E., Behrens, T. E. & Akam, T. Complementary task 
representations in hippocampus and prefrontal cortex for generalizing the structure of 
problems. Nat. Neurosci. 25, 1314–1326 (2022).
150. Boorman, E. D., Rajendran, V. G., O’Reilly, J. X. & Behrens, T. E. Two anatomically and 
computationally distinct learning signals predict changes to stimulus-outcome 
associations in hippocampus. Neuron 89, 1343–1354 (2016).
151. Leopold, D. A. & Averbeck, B. B. Self-tuition as an essential design feature of the brain. 
Philos. Trans. R. Soc. B 377, 20200530 (2022).
152. Ten, A., Kaushik, P., Oudeyer, P.-Y. & Gottlieb, J. Humans monitor learning progress in 
curiosity-driven exploration. Nat. Commun. 12, 5972 (2021).
153. Ballesta, S., Shi, W., Conen, K. E. & Padoa-Schioppa, C. Values encoded in orbitofrontal cortex are causally related to economic choices. Nature 588, 450–453 (2020).
154. Hong, S., Jhou, T. C., Smith, M., Saleem, K. S. & Hikosaka, O. Negative reward signals 
from the lateral habenula to dopamine neurons are mediated by rostromedial tegmental 
nucleus in primates. J. Neurosci. 31, 11457–11471 (2011).
155. Pisula, W. Curiosity and Information Seeking in Animal and Human Behavior 2nd edn 
(BrownWalker, 2020).
156. Greenberg, G., Partridge, T., Weiss, E. & Pisula, W. Comparative psychology,  
a new perspective for the 21st century: up the spiral staircase. Devel. Psychobiol.   
https://doi.org/10.1002/dev.10153 (2004).
157. Nussenbaum, K. & Hartley, C. A. Reinforcement learning across development: what 
insights can we draw from a decade of research? Dev. Cogn. Neurosci. 40, 100733 
(2019).
158. Gopnik, A. Childhood as a solution to explore–exploit tensions. Philos. Trans. R. Soc. B  
375, 20190502 (2020).
159. Somerville, L. H. et al. Charting the expansion of strategic exploratory behavior during adolescence. J. Exp. Psychol. Gen. 146, 155 (2017).
160. Meder, B., Wu, C. M., Schulz, E. & Ruggeri, A. Development of directed and random 
exploration in children. Dev. Sci. 24, e13095 (2021).
161. Chu, J. & Schulz, L. E. Play, curiosity, and cognition. Annu. Rev. Dev. Psychol. 2, 317–343 
(2020).
162. Neuringer, A. Operant variability: evidence, functions, and theory. Psychon. Bull. Rev. 9, 
672–705 (2002).
163. Fee, M. S. & Goldberg, J. H. A hypothesis for basal ganglia-dependent reinforcement learning in the songbird. Neuroscience 198, 152–170 (2011).
164. Grill-Spector, K. & Weiner, K. S. The functional architecture of the ventral temporal cortex 
and its role in categorization. Nat. Rev. Neurosci. 15, 536–548 (2014).
165. Kourtzi, Z. & Connor, C. E. Neural representations for object perception: structure, category, and adaptive coding. Annu. Rev. Neurosci. 34, 45–67 (2011).
166. Miller, E. K., Freedman, D. J. & Wallis, J. D. The prefrontal cortex: categories, concepts and 
cognition. Philos. Trans. R. Soc. Lond. Ser. B Biol. Sci. 357, 1123–1136 (2002).
167. Kriegeskorte, N. Deep neural networks: a new framework for modeling biological vision 
and brain information processing. Annu. Rev. Vis. Sci. 1, 417–446 (2015).
168. Poli, F., Serino, G., Mars, R. & Hunnius, S. Infants tailor their attention to maximize learning. Sci. Adv. 6, eabb5053 (2020).
169. Kidd, C., Piantadosi, S. T. & Aslin, R. N. The Goldilocks effect: human infants allocate 
attention to visual sequences that are neither too simple nor too complex. PLoS One 7, 
e36399 (2012).170. Perez, J. & Feigenson, L. Stable individual differences in infants’ responses to violations  
of intuitive physics. Proc. Natl Acad. Sci. 118, e2103805118 (2021).
171. Smith‐ Flores, A. S., Perez, J., Zhang, M. H. & Feigenson, L. Online measures of looking 
and learning in infancy. Infancy 27, 4–24 (2022).
172. Yu, C. & Smith, L. B. Embodied attention and word learning by toddlers. Cognition 125, 
244–262 (2012).
173. Yu, C. & Smith, L. B. Joint attention without gaze following: human infants and their 
parents coordinate visual attention to objects through eye-hand coordination. PLoS One  
8, e79659 (2013).
174. Yu, C. & Smith, L. B. The social origins of sustained attention in one-year-old human infants. Curr. Biol. 26, 1235–1240 (2016).
175. Smith, L. B., Jayaraman, S., Clerkin, E. & Yu, C. The developing infant creates a curriculum for statistical learning. Trends Cogn. Sci. 22, 325–336 (2018).
176. Gibson, E. J. & Collins, W. in The Concept of Development: the Minnesota Symposia on 
Child Psychology 1st edn, Vol. 15 (ed. Collins, W. A.) 55–81 (Psychology Press, 1982).
177. Hirsh, J. B., Mar, R. A. & Peterson, J. B. Psychological entropy: a framework for 
understanding uncertainty-related anxiety. Psychol. Rev. 119, 304–320 (2012).
178. Cheng, K.-H. & Tsai, C.-C. Affordances of augmented reality in science learning: suggestions for future research. J. Sci. Educ. Technol. 22, 449–462 (2013).
179. Thill, S., Caligiore, D., Borghi, A. M., Ziemke, T. & Baldassarre, G. Theories and 
computational models of affordance and mirror systems: an integrative review.  
Neurosci. Biobehav. Rev. 37, 491–521 (2013).
180. Jamone, L. et al. Affordances in psychology, neuroscience, and robotics: a survey.  
IEEE Trans. Cogn. Dev.  Syst. 10, 4–25 (2016).
181. Bharadhwaj, H., Gupta, A. & Tulsiani, S. Visual affordance prediction for guiding robot 
exploration. Preprint at arXiv https://arxiv.org/abs/2305.17783 (2023).
182. Rutler, O. et al. Mice require proprioception to establish long-term visuospatial memory. Preprint at bioRxiv, https://doi.org/10.1101/2023.10.03.560558 (2023).
183. Hartley, C. A. How do natural environments shape adaptive cognition across the 
lifespan? Trends Cogn. Sci. 26, 1029–1030 (2022).
184. Henderson, B. & Moore, S. G. Children’s responses to objects differing in novelty  
in relation to level of curiosity and adult behavior. Child Dev. 51, 457–465 (1980).
185. Blanco, N. J. & Sloutsky, V. M. Systematic exploration and uncertainty dominate young 
children’s choices. Dev. Sci. 24, e13026 (2021).
186. Gershman, S. J. Deconstructing the human algorithms for exploration. Cognition 173, 
34–42 (2018).
187. Wang, J., Yang, Y., Macias, C. & Bonawitz, E. Children with more uncertainty in their intuitive theories seek domain-relevant information. Psychol. Sci. 32, 1147–1156 (2021).
188. Molinaro, G., Cogliati Dezza, I., Bühler, S. K., Moutsiana, C. & Sharot, T. Multifaceted information-seeking motives in children. Nat. Commun. 14, 5505 (2023).
Acknowledgements
This Review was written while on sabbatical at INSERM (Lyon, France) and in part supported by the Neurodis Foundation from July through December of 2022. The author thanks CNRS 
and INSERM, and particularly the team of E. Procyk, for the intellectual stimulation and 
hospitality during this time. Gratitude is also expressed to the Department of Neuroscience at Washington University School of Medicine that has supported me on the scientific journey 
that led to this research, particularly A. Bonni for his initial support and advice. This work was 
supported by the National Institute of Mental Health under award numbers R01MH128344, R01MH110594 and R01MH116937, by Conte Center on the Neurocircuitry of OCD MH10643 
and by the McKnight Foundation. This Review is based on the brilliant and brave work of the 
past and current members of my laboratory, and in particular, I thank K. Kocher for providing fantastic and caring animal care, J. Kael White for discovering neural representations of 
uncertainty in the basal ganglia and linking them directly to the anticipation of information 
gain, E. S. Bromberg-Martin and Y.-Y. Feng for discovering the behavioural and neural algorithms that guide uncertainty reducing information gathering, T. Ogasawara for finding 
the neural circuit that regulates perceptual novelty seeking, K. Zhang for his efforts to assess 
the heterogenous nature of novelty detection in the primate brain, and E. S. Bromberg-Martin and F. Sogukpinar for the many interesting discussions of evolutionary models of curiosity. I 
am grateful to the current members of my laboratory and P. Dayan, C. Hartley, E. Procyk and G. 
Tavoni for the insightful, detailed and critical comments.
Competing interests
The author declares no competing interests.
Additional information
Peer review information Nature Reviews Neuroscience thanks Flores de Lange, who 
co-reviewed with Eva Berlot, and the other, anonymous, reviewer(s) for their contribution  
to the peer review of this work.
Publisher’s note Springer Nature remains neutral with regard to jurisdictional claims in 
published maps and institutional affiliations.
© Springer Nature Limited 2024, corrected publication 2024Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this 
article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the 
terms of such publishing agreement and applicable law.
