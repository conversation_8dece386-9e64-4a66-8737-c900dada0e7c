# -*- coding: utf-8 -*-
"""  
Author: Auto-generated by ChatGPT   
Date: 2025-06-04   

Purpose & Implementation:  
This script visualises the 20241120 experimental results comparing different intrinsic-reward configurations (kde+renyi0, origin+renyi0, kde+renyi4).  
It computes the mean and sample standard deviation of the coverage scores reported in the paper and renders a bar chart with error bars (±1 σ).  
Run the script from PowerShell:
    python scripts/plot_20241120.py
The resulting figure is saved as `figures/20241120_results.png`.  
"""

import os
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np

# ----------------------------
# Raw data (manually transcribed)
# ----------------------------
# NOTE: the "+" sign in some cells indicates "at least" that value in the log.
# For visualisation we conservatively clip it to the numeric part.

data = {
    "kde+renyi0": [356, 658, 201, 573, 1201],
    "origin+renyi0": [308, 707, 485, 1223, 826, 2684, 709],
    "kde+renyi4": [194, 414, 283, 269, 2000, 290],
}

labels = list(data.keys())
means = [np.mean(v) for v in data.values()]
stds = [np.std(v, ddof=1) for v in data.values()]  # sample std (N-1)

# ----------------------------
# Plotting
# ----------------------------
plt.style.use("ggplot")
fig, ax = plt.subplots(figsize=(8, 5))
bar_container = ax.bar(labels, means, yerr=stds, capsize=8, color=["#4C72B0", "#55A868", "#C44E52"])

ax.set_ylabel("Coverage (higher is better)")
ax.set_title("20241120 Experiment Results with ±1 SD")

# Annotate bars with mean values
for rect, m in zip(bar_container, means):
    height = rect.get_height()
    ax.text(rect.get_x() + rect.get_width() / 2.0, height + max(stds) * 0.05, f"{m:.1f}",
            ha="center", va="bottom", fontsize=10)

fig.tight_layout()

# Ensure output directory exists
out_dir = Path(__file__).resolve().parent.parent / "figures"
out_dir.mkdir(parents=True, exist_ok=True)
output_path = out_dir / "20241120_results.png"
fig.savefig(output_path, dpi=300)
print(f"Figure saved to {output_path}") 